﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration8 : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.VideoRankings",
                c => new
                    {
                        Rank = c.Int(nullable: false, identity: true),
                        Video_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Rank)
                .ForeignKey("dbo.Videos", t => t.Video_Id)
                .Index(t => t.Video_Id);
            
            CreateTable(
                "dbo.VideoViews",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Date = c.DateTime(nullable: false),
                        ViewCount = c.Int(nullable: false),
                        Video_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Videos", t => t.Video_Id)
                .Index(t => t.Video_Id);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.VideoViews", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.VideoRankings", "Video_Id", "dbo.Videos");
            DropIndex("dbo.VideoViews", new[] { "Video_Id" });
            DropIndex("dbo.VideoRankings", new[] { "Video_Id" });
            DropTable("dbo.VideoViews");
            DropTable("dbo.VideoRankings");
        }
    }
}
