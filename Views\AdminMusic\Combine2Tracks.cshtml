﻿@model stockhom_soundrom_mvc.ViewModels.AdminMusicCombineViewModel
@{
    ViewData["Title"] = "Combine2Tracks";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}


<style>

    body {
        background-color: black;
    }

    #add_entity {
        position: relative;
        padding: 50px;
    }


        #add_entity .close-button {
            position: absolute;
            top: 0;
            right: 50px;
            cursor: pointer;
            background-color: black;
            border: solid;
            color: white;
        }

    #lbl_entity {
        color: white;
    }


    .btn1 {
        color: white;
        background-color: black;
        border-radius: 3px;
        border-color: white;
        padding-top: 11px;
        padding-bottom: 10px;
        padding-left: 20px;
        padding-right: 20px;
    }



    .profile_container {
        width: 800px;
    }

    .profile_box {
        height: 280px;
        display: inline-block;
        text-align: center;
        margin: 14px;
    }

        .profile_box .cover_art_with_vinyls {
            cursor: pointer;
            transition: all 0.5s ease;
        }

            .profile_box .cover_art_with_vinyls:hover {
                transform: scale(1.1);
            }

            .profile_box .cover_art_with_vinyls img {
                height: 200px;
            }



        .profile_box .entity_name {
            width: 150px;
            text-align: center;
            color: white;
            margin-bottom: 3px;
            display: block;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
        }

    .infobox {
        margin-top: 15px;
        /*margin-left: 50px;*/
        /* height: 60px;*/
        min-height: 60px;
        background-color: black;
        color: white;
        border: solid white;
        border-width: 1px;
    }

        .infobox label {
            width: 100%;
            height: 10px;
            font-size: x-small;
            font-size: x-small;
            display: block;
        }

        .infobox input {
            width: 90%;
            font-size: large;
            background-color: black;
            color: white;
            border: none;
        }

        .infobox .input_list {
            border: solid;
            border-color: #423a3a;
            margin-bottom: 2px;
        }

        .infobox textarea {
            font-size: large;
            background-color: black;
            color: white;
            border: none;
            width: 700px;
            height: 140px;
        }


    .button_container {
        /*margin-left: 50px;*/
        margin-top: 20px;
    }

    #pic_vertical {
        width: 180px;
        height: 180px;
        position: relative;
        border: solid;
        border-color: white;
        color: white;
        /*margin-left: 50px;*/
        vertical-align: central;
        display: inline-block;
        background-size: 100% 100%;
    }

        #pic_vertical:hover {
            background-color: #5d6952;
        }

    #pic_horizontal {
        width: 200px;
        height: 130px;
        position: relative;
        border: solid;
        border-color: white;
        color: white;
        display: inline-block;
        background-size: 100% 100%;
    }

        #pic_horizontal:hover {
            background-color: #5d6952;
        }




    .plus {
        font-size: 73px;
        text-align: center;
        margin: auto;
        color: white;
        font-weight: 900;
    }


    .add_another {
        display: inline;
        text-align: right;
        color: white;
        position: absolute;
        right: 35px;
        font-size: x-small;
    }

        .add_another:hover {
            font-weight: 900;
        }

    .select_related {
        background-color: black;
        color: white;
        margin: 3px;
        position: relative;
    }

    /*.select_related .close-button {
            position: absolute;
            top: 0;
            right: 0;
            display: none;
        }

        .select_related:hover .close-button {
            display: block;
        }

    .close-button {*/
    /* style the close button, such as color, size, etc. */
    /*color:red;
    }*/


    .parent {
        z-index: 1;
        background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
    }

    .child {
        position: absolute;
        z-index: 1;
    }



    .remove {
        position: absolute;
        top: 0px;
        right: 0px;
        display: block;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        border-width: 3px;
        border-style: solid;
        border-color: red;
        border-radius: 100%;
        background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
        background-color: red;
        box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
        transition: all 0.3s ease;
    }
</style>


<div style="/*margin-left: 50px; */ margin-bottom: 20px; font-size: large; color: white;">
    Music
</div>

@{

    Model.Track1.ToString();
    Model.Track2.ToString();



}


<form method="post" id="form" enctype="multipart/form-data">


    <input type="hidden" name="track1Id" value="@Model.Track1.Id" />
    <input type="hidden" name="track2Id" value="@Model.Track2.Id" />


    <div>
        <input type="radio" checked value="@stockhom_soundrom_mvc.Models.MusicCollectionType.maxi_single" id="maxi" name="coltype" /><label style="color:white">Maxi Single</label>
        <input type="radio" value="@stockhom_soundrom_mvc.Models.MusicCollectionType.EP" id="ep" name="coltype" /><label style="color:white">EP</label>
        <input type="radio" value="@stockhom_soundrom_mvc.Models.MusicCollectionType.album" id="albun" name="coltype" /><label style="color:white">Album</label>
    </div>


    <div class="infobox">
        <label>Maxi single / EP / Album title</label>
        @*<input type="text" id="meta_title" value="@Model.NewArtist.MetaTitle" />*@
        @Html.TextBoxFor(m => m.Music.Title)
    </div>

    <div class="infobox" style="width: 300px; display: inline-block">
        <label>Publish Date</label>
        @*<input type="date" value="@Model.NewArtist.PublishedDate" id="published_date" style="color-scheme: dark;" />*@
        @Html.TextBoxFor(m => m.Music.PublishDate, new { type = "datetime-local", style = "color-scheme: dark;" })

    </div>

    <div style="display:inline-block;margin-top:5px">
        <div>
            @Html.CheckBoxFor(m => m.Music.StartCountDown)<span style="color:white"> Start Count down clock </span>
        </div>
        <div>
            @Html.CheckBoxFor(m => m.Music.TopPromo)  <span style="color:white">Tpp Promo</span>
        </div>
    </div>

    <div style="display:block;margin-top:15px">
        <div id="pic_vertical" onclick="pic_vertical_upload.click();">
            <label style="position: relative; top: 5px; left: 5px;">Artwork Square</label>
            <div class="plus">+</div>
        </div>
        <input id='pic_vertical_upload' name="pic_vertical_upload" type='file' value="" style='display: none' accept='.jpg' />


        <div id="pic_horizontal" onclick="pic_horizontal_upload.click();">
            <label style="position: relative; top: 5px; left: 5px;">Artwork Horizontal</label>
            <div class="plus">+</div>
        </div>
        <input id='pic_horizontal_upload' name="pic_horizontal_upload" type='file' value="" style='display: none' accept='.jpg' />
    </div>

    <div class="infobox" id="infobox_buy_links">

        <label>Buy Link</label>

        @*@{if (Model.ViewForUpdate == true)
                {
                    foreach (var buyLink in Model.Track.BuyLinks)
                    {
                        <input type="text" value="@buyLink.Link" name="@Global.BuyLinkTXTName" />
                    }
                }

            }*@


    </div>
    <label id="add_another_buy_link" class="add_another">Add another buy link</label>



    <div class="infobox">
        <label>Spotify Album Link</label>
        @Html.TextBoxFor(m => m.Music.SpotifyAlbumLink)
    </div>

    <div class="infobox">
        <label>Apple Album Link</label>
        @Html.TextBoxFor(m => m.Music.AppleAlbumLink)
    </div>

    <div class="infobox">
        <label>Soundclould Album Link</label>
        @Html.TextBoxFor(m => m.Music.SoundCloudAlbumLink)
    </div>

    <div class="infobox">
        <label>Youtube Music Album Link</label>
        @Html.TextBoxFor(m => m.Music.YoutubeMusicAlbumLink)
    </div>

    <div class="infobox">
        <label>Youtube Album Link</label>
        @Html.TextBoxFor(m => m.Music.YoutubeAlbumLink)
    </div>

    <div class="infobox" style="height: 160px">
        <label>Description</label>
        @Html.TextAreaFor(m => m.Music.Description)
    </div>

    <div class="infobox">
        <label>Meta Title</label>
        @Html.TextBoxFor(m => m.Music.MetaTitle)
    </div>

    <div class="infobox">
        <label>Meta Description</label>
        @Html.TextBoxFor(m => m.Music.MetaDescription)
    </div>

    <div class="button_container">
        <input type="submit" formaction="@Url.Action("PublishMusic","AdminMusic")" value="PUBLISH MAXI SINGLE/ALBUM/EP" class="btn1" />
        <input type="submit" formaction="@Url.Action("SaveMusic","AdminMusic")" name="save" id="btn_save" value="SAVE" class="btn1" />
        <input type="submit" id="preview_button" value="PREVIEW" class="btn1" />
        <input type="button" class="btn1" value="DELETE" />
    </div>


</form>


@section Scripts{

    <script>

    var publishDate = '@Model.Music.PublishDate.ToString("yyyy-MM-ddTHH:mm:ss")';
        document.getElementById('Music_PublishDate').value = publishDate;

    </script>


    <script>

        document.getElementById("add_another_buy_link").addEventListener("click", function () {


            let input = document.createElement("input");
            input.type = "text";
            input.name = "input_text_buylink";
            input.style.border = "solid";
            input.style.borderColor = "white";
            input.style.borderWidth = "1px";

            document.getElementById("infobox_buy_links").appendChild(input);
        });


        function ver_image_upload() {

            var fileInput = document.getElementById('pic_vertical_upload');
            var file = fileInput.files[0];
            _ver_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_vertical');
            div.style.backgroundImage = 'url(' + fileUrl + ')';
        }

        function hor_image_upload() {

            var fileInput = document.getElementById('pic_horizontal_upload');
            var file = fileInput.files[0];
            _hor_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_horizontal');
            div.style.backgroundImage = 'url(' + fileUrl + ')';

        }

        document.getElementById("pic_horizontal_upload").addEventListener("change", hor_image_upload, false);
        document.getElementById("pic_vertical_upload").addEventListener("change", ver_image_upload, false);


    </script>

    <script>

    @{
        var track1_id = Model.Track1.Id;
        var track2_id = Model.Track2.Id;

    }

    var newTab = null;

    $(document).ready(function () {
        $("#preview_button").click(function (e) {
            e.preventDefault();

            var form = $("#form");
            var formData = new FormData(form[0]);

            $.ajax({
                type: "POST",
                url: "@Url.Action("Preview","Music", new { track1_id = track1_id , track2_id = track2_id })",
                data: formData,
                async: false,
                contentType: false,
                processData: false,
                success: function (result) {

                    console.log(result);

                    newTab = window.open();
                    newTab.document.write(result);
                }
            });
        });
    });
    </script>

}