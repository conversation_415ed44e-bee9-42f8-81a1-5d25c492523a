﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - stockhom_soundrom_mvc</title>
    <link href='https://fonts.googleapis.com/css?family=Oswald' rel='stylesheet'>
        <link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>
        <link href='https://fonts.googleapis.com/css?family=Permanent Marker' rel='stylesheet'>
    <link rel="stylesheet" href="~/css/site.css" />
    <script src="~/lib/jquery/dist/jquery.min.js"></script>

    @RenderSection("Styles", required: false)


    <style>


        li {
            padding: 10px;
            margin: 10px;
            cursor: pointer;
        }

        #side_nav_bar {
            width: 200px;
            height: 100%;
            background-color: #2b2e2f;
            color: #FFF;
        }

        td {
            border: 1px solid black;
            width: 10%;
            padding-bottom: 10%;
        }


        #container_main1 {
            width: 100%;
            display: table;
        }


        #container_main2 {
            display: table-row;
        }


        @@media screen and (min-width: 1600px) {

            #left {
                display: table-cell;
            }


            #container1 {
                display: table-cell;
                width: 800px;
            }


            #right {
                display: table-cell;
            }
        }


        @@media screen and (min-width: 800px) and (max-width: 1599px) {

            #left {
                display: table-cell;
            }


            #container1 {
                display: table-cell;
                width: calc(40vw + 100px);
            }


            #right {
                display: table-cell;
            }
        }

        @@media screen and (max-width: 799px) {


            #container_main1 {
                width: 100%;
                display: block;
            }


            #container_main2 {
                display: block;
                width: 100%;
            }

            #left {
                display: none;
            }


            #container1 {
                display: block;
                width: 100%;
            }


            #right {
                display: none;
            }
        }

        @@media (max-width: 2000px) {
            .menu-button-container {
                display: flex;
            }

            .menu {
                position: absolute;
                top: 0;
                margin-top: 50px;
                left: 0;
                flex-direction: column;
                width: 100%;
                justify-content: center;
                align-items: center;
            }

            #menu-toggle ~ .menu li {
                height: 0;
                margin: 0;
                padding: 0;
                border: 0;
                transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            }

            #menu-toggle:checked ~ .menu li {
                border: 1px solid #333;
                height: 2.5em;
                padding: 0.5em;
                transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            }

            .menu > li {
                display: flex;
                justify-content: center;
                margin: 0;
                padding: 0.5em 0;
                width: 100%;
                color: white;
                background-color: #222;
            }

                .menu > li:not(:last-child) {
                    border-bottom: 1px solid #444;
                }
        }






        h2 {
            vertical-align: center;
            text-align: center;
        }

        html,
        body {
            margin: 0;
            height: 100%;
        }

        * {
            box-sizing: border-box;
        }

        .top-nav {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            background-color: lightblue;
            color: #FFF;
            height: 50px;
            padding: 1em;
        }

            .top-nav img {
                height: 30px;
                position: absolute;
                top: 20px;
                left: 50%;
                transform: translate(-50%, -50%);
            }

        .menu {
            display: flex;
            flex-direction: row;
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

            .menu > li {
                margin: 0 1rem;
                overflow: hidden;
            }

        .menu-button-container {
            display: none;
            height: 100%;
            width: 30px;
            cursor: pointer;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        #menu-toggle {
            display: none;
        }

        .menu-button,
        .menu-button::before,
        .menu-button::after {
            display: block;
            background-color: black;
            position: absolute;
            height: 4px;
            width: 30px;
            transition: transform 400ms cubic-bezier(0.23, 1, 0.32, 1);
            border-radius: 2px;
        }

            .menu-button::before {
                content: '';
                margin-top: -8px;
            }

            .menu-button::after {
                content: '';
                margin-top: 8px;
            }

        #menu-toggle:checked + .menu-button-container .menu-button::before {
            margin-top: 0px;
            transform: rotate(405deg);
        }

        #menu-toggle:checked + .menu-button-container .menu-button {
            background: rgba(255, 255, 255, 0);
        }

            #menu-toggle:checked + .menu-button-container .menu-button::after {
                margin-top: 0px;
                transform: rotate(-405deg);
            }
    </style>

</head>
<body>

    @*<div class="header" style="width: 100%; height: 100px; background-color: #2b2e2f;"></div>
        <table height="100%" width="100%">
            <tr>
                <td style="vertical-align: top; background-color: #2b2e2f;">
                    <div id="side_nav_bar">
                        <div class="admin-menu" style="list-style: none;">
                            <li>Presentation</li>
                            <li>Music</li>
                            <li>Style</li>
                            <li>Party</li>
                            <li>Videos</li>
                            <li>The Journal</li>
                            <li>Support</li>
                            <li>Fans</li>

                        </div>

                    </div>
                </td>
                <td style="vertical-align:top">
                    <div>
                        @RenderBody()
                    </div>
                </td>
            </tr>


        </table>*@

    @*<section class="top-nav">
            <img src="logo-one-line.png">
            <div style="text-align: center;">

            </div>
            <input id="menu-toggle" type="checkbox" />
            <label class='menu-button-container' for="menu-toggle">
                <div class='menu-button'></div>
            </label>
            <ul class="menu">
                <li>YOUTUBE</li>
                <li>SPOTIFY</li>
                <li>SOUNDCLOUD</li>
                <li>TICTOK</li>
                <li>INSTAGRAM</li>
                <li>FACEBOOK</li>
                <li>TWITTER</li>
                <li>THE JOURNAL</li>
                <li>SUPPORT</li>
                <li>CONTACT</li>

            </ul>
        </section>

        <div id="container_main1">
            <div id="container_main2">
                <div id="left"></div>
                <div id="container1">
                    @RenderBody()
                </div>
                <div id="right"></div>
            </div>*@



    @RenderBody()



    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>


        //create table function with 2 parameters
        function createTables(row_count, col_count) {

            // Create the table
            let table_left = document.createElement("table");
            table_left.style.width = "100%";
            let table_right = document.createElement("table");
            table_right.style.width = "100%";

            // Create the number of rows specified
            for (let i = 0; i < row_count; i++) {
                let row_left = table_left.insertRow();
                let row_right = table_right.insertRow();

                // Create the number of columns specified
                for (let j = 0; j < col_count / 2; j++) {
                    let cell = row_left.insertCell();
                    cell.style.backgroundImage = "url(icons/vinyl.png)";
                    cell.style.backgroundSize = "cover";
                    cell.style.border = "none";
                    cell.style.position = "relative";
                    cell.id = i + "_" + j;
                }

                for (let j = col_count / 2; j < col_count; j++) {
                    let cell = row_right.insertCell();
                    cell.style.backgroundImage = "url(icons/vinyl.png)";
                    cell.style.backgroundSize = "cover";
                    cell.style.border = "none";
                    cell.id = i + "_" + j;
                }

            }

            // Add the table to the specified div element
            document.getElementById("left").appendChild(table_left);
            document.getElementById("right").appendChild(table_right);

        }

        var cols = 20;
        var rows = 20;

        createTables(20, cols);


        var lastDirection = 0;
        var currentX = 0;
        var currentY = 0;
        var failCount = 0;

        window.onload = async function () {


            //set the pacman to a random cell
            currentX = Math.floor(Math.random() * cols);
            currentY = Math.floor(Math.random() * rows);
            //set the pacman to the current cell
            document.getElementById(currentY + "_" + currentX).style.backgroundImage = "url(icons/pacman.jpg)";
            document.getElementById(currentY + "_" + currentX).style.backgroundSize = "cover";
            // document.getElementById(currentY + "_" + currentX).style.margin = "-10px";
            // document.getElementById(currentY + "_" + currentX).style.padding = "10px";

            while (true) {


                //remove the pacman from the current cell
                document.getElementById(currentY + "_" + currentX).style.backgroundImage = "none";


                pickNextPos(cols, rows);





                //add the pacman to the next cell
                document.getElementById(currentY + "_" + currentX).style.backgroundImage = "url(icons/pacman.jpg)";
                document.getElementById(currentY + "_" + currentX).style.backgroundSize = "cover";
                // document.getElementById(currentY + "_" + currentX).style.margin = "-10px";
                // document.getElementById(currentY + "_" + currentX).style.padding = "10px";

                await new Promise(r => setTimeout(r, 1000));

            }
        }







        function outOfBounds(cols, rows) {

            if (currentX < 0 || currentX >= cols || currentY < 0 || currentY >= rows) {
                return true;
            }
            else {
                return false;
            }
        }

        function alreadyVisited() {

            if (document.getElementById(currentY + "_" + currentX).style.backgroundImage == "url(icons/vinyl.png)") {
                return false;
            }
            else {
                return true;
            }
        }


        function pickNextPos(cols, rows) {

            // Pick a random direction
            let direction = Math.floor(Math.random() * 4);

            //0 = up, 1 = right, 2 = down, 3 = left

            if (direction == 0) {

                currentY--;
                if (outOfBounds(cols, rows)) {
                    currentY++;
                    pickNextPos();
                }
            }
            else if (direction == 1) {
                currentX++;
                if (outOfBounds(cols, rows)) {
                    currentX--;
                    pickNextPos();
                }
            }
            else if (direction == 2) {
                currentY++;
                if (outOfBounds(cols, rows)) {
                    currentY--;
                    pickNextPos();
                }
            }
            else if (direction == 3) {
                currentX--;
                if (outOfBounds(cols, rows)) {
                    currentX++;
                    pickNextPos();
                }
            }
        }




        //create a function which changes tha backgroud color of the clicked li element to red and the rest to blue
        function changeColor() {
            //get all the li elements
            var liElements = document.querySelectorAll("li");
            //loop through the li elements
            for (var i = 0; i < liElements.length; i++) {
                //add an event listener to each li element
                liElements[i].addEventListener("click", function () {
                    console.log("clicked");
                    //loop through the li elements again
                    for (var j = 0; j < liElements.length; j++) {
                        //change the background color of each li element to blue
                        liElements[j].style.backgroundColor = "#2b2e2f";
                    }
                    //change the background color of the clicked li element to red
                    this.style.backgroundColor = "black";
                })
            }
        }

        changeColor();

    </script>


    @RenderSection("Scripts", required: false)
</body>
</html>
