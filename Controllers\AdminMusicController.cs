﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Data.Entity;
using AutoMapper;


namespace stockhom_soundrom_mvc.Controllers
{
    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminMusicController : Controller
    {

        private readonly IWebHostEnvironment _env;

        public AdminMusicController(IWebHostEnvironment env)
        {
            _env = env;


        }



        public IActionResult Index()
        {
            return View(new AdminMusicViewModel());
        }


        public IActionResult ReOrderTracks(int dragid, int dropid, int music_id)
        {
            using (Data data = new Data())
            {
                var music = data.Musics.Where(m => m.Id == music_id).Include(m => m.Tracks).Single();
                var dragTrack = music.Tracks.Where(t => t.Id == dragid).Single();
                var dropTrack = music.Tracks.Where(t => t.Id == dropid).Single();
                var dragTrackOrderedNo = dragTrack.TrackNoInMusicCollection;
                var dropTrackOrderedNo = dropTrack.TrackNoInMusicCollection;


                if (music.Tracks.Count < 2)
                {
                    //nothing
                }
                else if (music.Tracks.Count == 2)
                {
                    dragTrack.TrackNoInMusicCollection = dropTrackOrderedNo;
                    dropTrack.TrackNoInMusicCollection = dragTrackOrderedNo;

                }
                else
                {
                    if (dragTrackOrderedNo > dropTrackOrderedNo)
                    {


                        for (int x = dropTrackOrderedNo; x < dragTrackOrderedNo; x++)
                        {
                            music.Tracks.Where(t => t.TrackNoInMusicCollection == x).Last().TrackNoInMusicCollection = x + 1;
                        }

                        dragTrack.TrackNoInMusicCollection = dropTrackOrderedNo;
                    }
                    else  //dragTrackOrderedNo < dropTrackOrderedNo
                    {
                        for (int x = dropTrackOrderedNo; x >= dragTrackOrderedNo + 1; x--)
                        {
                            music.Tracks.Where(t => t.TrackNoInMusicCollection == x).First().TrackNoInMusicCollection = x - 1;
                        }

                        dragTrack.TrackNoInMusicCollection = dropTrackOrderedNo;
                    }
                }


                data.SaveChanges();
            }



            AdminMusicEditMusicViewModel adminMusicEditMusicViewModel = new AdminMusicEditMusicViewModel();

            using (Data data = new Data())
            {
                adminMusicEditMusicViewModel.Music = data.Musics.Include(m => m.Tracks.Select(t => t.RelatedArtists)).Where(m => m.Id == music_id).FirstOrDefault();
                adminMusicEditMusicViewModel.Music.Tracks = adminMusicEditMusicViewModel.Music.Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();
                adminMusicEditMusicViewModel.state = "bars_only";
            }

            return View("EditMusic", adminMusicEditMusicViewModel);



        }


        public IActionResult DeleteTrack(int track_id, int music_id)
        {
            int deleted_tracks_position_in_collection = 0;

            using (Data data = new Data())
            {
                var track = data.Tracks.Where(t => t.Id == track_id).Include(t => t.BuyLinks).Single();

                deleted_tracks_position_in_collection = track.TrackNoInMusicCollection;


                for (int x = 0; x < track.BuyLinks.Count(); x++)
                {
                    data.TrackBuyLinks.Remove(track.BuyLinks[x]);
                }


                data.SaveChanges();

                data.Tracks.Remove(track);
                data.SaveChanges();
            }

            if (music_id == 0)
            {
                return View("Index", new AdminMusicViewModel());
            }
            else
            {
                //decrement the value of all 

                var loop_var = deleted_tracks_position_in_collection + 1;

                using (Data data = new Data())
                {

                    while (true)
                    {
                        var t = data.Tracks.Where(t => t.TrackNoInMusicCollection == loop_var).FirstOrDefault();
                        if (t != null)
                        {
                            t.TrackNoInMusicCollection = t.TrackNoInMusicCollection - 1;
                            data.SaveChanges();
                            loop_var++;
                        }
                        else
                        {
                            break;
                        }

                    }
                }


                AdminMusicEditMusicViewModel adminMusicEditMusicViewModel = new AdminMusicEditMusicViewModel();

                using (Data data = new Data())
                {
                    adminMusicEditMusicViewModel.Music = data.Musics.Include(m => m.Tracks.Select(t => t.RelatedArtists)).Where(m => m.Id == music_id).FirstOrDefault();
                    adminMusicEditMusicViewModel.Music.Tracks = adminMusicEditMusicViewModel.Music.Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();
                    adminMusicEditMusicViewModel.state = "bars_only";
                }

                return View("EditMusic", adminMusicEditMusicViewModel);
            }
        }

        public IActionResult DeleteMusic(int music_id)
        {
            try
            {


                using (Data data = new Data())
                {
                    var music = data.Musics.Where(m => m.Id == music_id).Include(t => t.BuyLinks).Include(t => t.Tracks).Single();

                    //for (int x = 0; x < music.BuyLinks.Count(); x++)
                    //{
                    //    data.MusicBuyLinks.Remove(music.BuyLinks[x]);
                    //}

                    //for (int x = 0; x < music.Tracks.Count(); x++)
                    //{
                    //    data.Tracks.Remove(music.Tracks[x]);
                    //}

                    music.BuyLinks.Clear();
                    music.Tracks.Clear();

                    data.SaveChanges();

                    data.Musics.Remove(music);
                    data.SaveChanges();
                }
            }
            catch (Exception ex)
            {

            }
            return View("Index", new AdminMusicViewModel());


        }


        /// <summary>
        /// Get the 2 tracks in Database from `track1Id` , `track2Id` . create a `AdminMusicCombineViewModel` types object and pass it to Combine2Tracks.cshtml 
        /// </summary>
        /// <param name="track1Id"></param>
        /// <param name="track2Id"></param>
        /// <returns></returns>
        public IActionResult Combine2Tracks(int track1Id, int track2Id)
        {
            using (Data data = new Data())
            {
                var track1 = data.Tracks.Where(t => t.Id == track1Id).FirstOrDefault();
                var track2 = data.Tracks.Where(t => t.Id == track2Id).FirstOrDefault();

                AdminMusicCombineViewModel adminMusicCombineViewModel = new AdminMusicCombineViewModel()
                {
                    Track1 = track1,
                    Track2 = track2
                };


                return View(adminMusicCombineViewModel);

            }


        }


        /// <summary>
        /// gets related track and music objects from database using trackId and musicId
        /// then add the track to music object and save changes to database.
        /// </summary>
        /// <param name="trackId"></param>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public IActionResult AddTrackToMusic(int trackId, int musicId)
        {
            //update database 

            using (Data data = new Data())
            {
                var track = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                var music = data.Musics.Include(m => m.Tracks).Where(m => m.Id == musicId).FirstOrDefault();

                var noOfExistingTracksInMusic = music.Tracks.Count();
                track.TrackNoInMusicCollection = noOfExistingTracksInMusic + 1;


                music.Tracks.Add(track);
                data.SaveChanges();
            }



            return View("Index", new AdminMusicViewModel());
        }



        /// <summary>
        /// this action is called when a Music profile is clicked. Gets the Music object from database using the `id` parameter 
        /// when selecting the related Muic object is also inclues the Music obejects related Tracks and each tracks  Related artist Objects
        /// Then Tracks are orderd by the `TrackNoInMusicCollection` property. Objects are set to a AdminMusicEditMusicViewModel object and the 
        /// the viewmodel is passed to EditMusic.cshtml 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IActionResult EditMusic(int id = 2)
        {

            AdminMusicEditMusicViewModel adminMusicEditMusicViewModel = new AdminMusicEditMusicViewModel();

            using (Data data = new Data())
            {
                adminMusicEditMusicViewModel.Music = data.Musics.Include(m => m.Tracks.Select(t => t.RelatedArtists)).Where(m => m.Id == id).FirstOrDefault();
                adminMusicEditMusicViewModel.Music.Tracks = adminMusicEditMusicViewModel.Music.Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();
                adminMusicEditMusicViewModel.state = "bars_only";
            }

            return View(adminMusicEditMusicViewModel);
        }


        /// <summary>
        /// this action is called when a single track profile is clicked. It gets the related track from database using `id`.
        /// Including all the related object collecions. `AddSectionVisible` and `ViewForUpdate` in the `AdminMusicViewModel`  is set  to be true.
        /// the viewmodel is passed to index.csthml page.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IActionResult EditTrack(int id = 2)
        {
            AdminMusicViewModel adminMusicViewModel = new AdminMusicViewModel();

            using (Data data = new Data())
            {
                adminMusicViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminMusicViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminMusicViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminMusicViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminMusicViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminMusicViewModel.Track = data.Tracks.Where(t => t.Id == id)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Include(t => t.RelatedStyles)
                    .Include(t => t.RelatedVideos)
                    .Include(t => t.BuyLinks)
                    .Single();
                adminMusicViewModel.AddSectionVisible = true;
                adminMusicViewModel.ViewForUpdate = true;
            }

            return View("index", adminMusicViewModel);

        }




        /// <summary>
        /// This acion is called when click the Music Bar in EditMusic.cshtml
        /// Same as whats happening in `EditMusic` the only difference is the state is set to `edit_music`
        /// the `AdminMusicEditMusicViewModel` is sent to EditMusic.csthml
        /// </summary>
        /// <param name="music_id"></param>
        /// <returns></returns>
        public IActionResult EditMusicEditMusic(int music_id)
        {

            AdminMusicEditMusicViewModel adminMusicEditMusicViewModel = new AdminMusicEditMusicViewModel();

            using (Data data = new Data())
            {
                adminMusicEditMusicViewModel.Music = data.Musics.Include(m => m.BuyLinks).Include(m => m.Tracks.Select(t => t.RelatedArtists)).Where(m => m.Id == music_id).FirstOrDefault();
                adminMusicEditMusicViewModel.Music.Tracks = adminMusicEditMusicViewModel.Music.Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();

                adminMusicEditMusicViewModel.state = "edit_music";
            }

            return View("EditMusic", adminMusicEditMusicViewModel);
        }


        /// <summary>
        /// This action is called when click on the Save button under the Music section in
        /// EditMusic.cshtml page. Inside the function we update the existing music obejct in database from the Music objec
        /// generated from `AdminMusicCombineViewModel`
        /// </summary>
        /// <param name="adminMusicCombineViewModel"></param>
        /// <param name="MusicId"></param>
        /// <param name="pic_vertical_upload"></param>
        /// <param name="pic_horizontal_upload"></param>
        /// <returns></returns>
        public IActionResult EditMusicEditMusicSave(AdminMusicCombineViewModel adminMusicCombineViewModel, int MusicId, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            AdminMusicEditMusicViewModel adminMusicEditMusicViewModel = new AdminMusicEditMusicViewModel();



            using (Data data = new Data())
            {
                var NewMusic = GenerateMusicFromCombineMusicViewModel(data, adminMusicCombineViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);


                var oldMusic = data.Musics.Include(m => m.Tracks).Include(m => m.BuyLinks).Where(m => m.Id == MusicId).Single();

                //remove exising buy links
                oldMusic.BuyLinks.Clear();
                data.SaveChanges();

                oldMusic.MetaDescription = NewMusic.MetaDescription;
                oldMusic.MetaTitle = NewMusic.MetaTitle;
                oldMusic.MusicCollectionType = NewMusic.MusicCollectionType;
                oldMusic.PublishDate = NewMusic.PublishDate;
                oldMusic.SoundCloudAlbumLink = NewMusic.SoundCloudAlbumLink;
                oldMusic.SpotifyAlbumLink = NewMusic.SpotifyAlbumLink;
                oldMusic.StartCountDown = NewMusic.StartCountDown;
                oldMusic.Title = NewMusic.Title;
                oldMusic.TopPromo = NewMusic.TopPromo;
                oldMusic.YoutubeAlbumLink = NewMusic.YoutubeAlbumLink;
                oldMusic.YoutubeMusicAlbumLink = NewMusic.YoutubeMusicAlbumLink;
                oldMusic.AppleAlbumLink = NewMusic.AppleAlbumLink;
                oldMusic.ArtWorkHoriontal = (NewMusic.ArtWorkHoriontal != null) ? NewMusic.ArtWorkHoriontal : oldMusic.ArtWorkHoriontal;
                oldMusic.ArtWorkSquare = (NewMusic.ArtWorkSquare != null) ? NewMusic.ArtWorkSquare : oldMusic.ArtWorkSquare;
                oldMusic.BuyLinks = NewMusic.BuyLinks;
                oldMusic.State = State.Saved;

                data.SaveChanges();

                adminMusicEditMusicViewModel.Music = data.Musics.Include(m => m.Tracks.Select(t => t.RelatedArtists)).Where(m => m.Id == MusicId).FirstOrDefault();
                adminMusicEditMusicViewModel.Music.Tracks = adminMusicEditMusicViewModel.Music.Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();
                adminMusicEditMusicViewModel.state = "bars_only";


            }

            return View("EditMusic", adminMusicEditMusicViewModel);





        }




        public IActionResult EditMusicEditMusicPublish(AdminMusicCombineViewModel adminMusicCombineViewModel, int MusicId, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            AdminMusicEditMusicViewModel adminMusicEditMusicViewModel = new AdminMusicEditMusicViewModel();



            using (Data data = new Data())
            {
                var NewMusic = GenerateMusicFromCombineMusicViewModel(data, adminMusicCombineViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);


                var oldMusic = data.Musics.Include(m => m.Tracks).Include(m => m.BuyLinks).Where(m => m.Id == MusicId).Single();

                //remove exising buy links
                oldMusic.BuyLinks.Clear();
                data.SaveChanges();

                oldMusic.MetaDescription = NewMusic.MetaDescription;
                oldMusic.MetaTitle = NewMusic.MetaTitle;
                oldMusic.MusicCollectionType = NewMusic.MusicCollectionType;
                oldMusic.PublishDate = NewMusic.PublishDate;
                oldMusic.SoundCloudAlbumLink = NewMusic.SoundCloudAlbumLink;
                oldMusic.SpotifyAlbumLink = NewMusic.SpotifyAlbumLink;
                oldMusic.StartCountDown = NewMusic.StartCountDown;
                oldMusic.Title = NewMusic.Title;
                oldMusic.TopPromo = NewMusic.TopPromo;
                oldMusic.YoutubeAlbumLink = NewMusic.YoutubeAlbumLink;
                oldMusic.YoutubeMusicAlbumLink = NewMusic.YoutubeMusicAlbumLink;
                oldMusic.AppleAlbumLink = NewMusic.AppleAlbumLink;
                oldMusic.ArtWorkHoriontal = (NewMusic.ArtWorkHoriontal != null) ? NewMusic.ArtWorkHoriontal : oldMusic.ArtWorkHoriontal;
                oldMusic.ArtWorkSquare = (NewMusic.ArtWorkSquare != null) ? NewMusic.ArtWorkSquare : oldMusic.ArtWorkSquare;
                oldMusic.BuyLinks = NewMusic.BuyLinks;
                oldMusic.State = State.Published;

                data.SaveChanges();

                adminMusicEditMusicViewModel.Music = data.Musics.Include(m => m.Tracks.Select(t => t.RelatedArtists)).Where(m => m.Id == MusicId).FirstOrDefault();
                adminMusicEditMusicViewModel.Music.Tracks = adminMusicEditMusicViewModel.Music.Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();
                adminMusicEditMusicViewModel.state = "bars_only";


            }

            return View("EditMusic", adminMusicEditMusicViewModel);





        }



        /// <summary>
        /// this action is called when click on any track bar in EditMusic.csthml.
        /// Inside the action we get the Parent Music object from database. Assign it to a `AdminMusicEditMusicViewModel`. 
        /// the selected tracks all related data are assigned to AdminMusicEditMusicViewModel object aswell.
        /// </summary>
        /// <param name="music_id"></param>
        /// <param name="track_id"></param>
        /// <returns></returns>
        public IActionResult EditMusicEditTrack(int music_id, int track_id)
        {

            AdminMusicEditMusicViewModel adminMusicEditMusicViewModel = new AdminMusicEditMusicViewModel();

            using (Data data = new Data())
            {

                var OrigninalMusic = data.Musics.Where(m => m.Id == music_id).Include(m => m.Tracks).Single();

                adminMusicEditMusicViewModel.Music = new Music();
                adminMusicEditMusicViewModel.Music.Id = OrigninalMusic.Id;
                adminMusicEditMusicViewModel.Music.Title = OrigninalMusic.Title;

                foreach (var track in OrigninalMusic.Tracks)
                {
                    if (track.Id == track_id) // need to populate with all the data related to track
                    {
                        var trackOBj = data.Tracks.Where(t => t.Id == track.Id)
                            .Include(t => t.RelatedArtists)
                            .Include(t => t.RelatedJournals)
                            .Include(t => t.RelatedStyles)
                            .Include(t => t.RelatedVideos)
                            .Include(t => t.BuyLinks)
                            .Single();
                        adminMusicEditMusicViewModel.Music.Tracks.Add(trackOBj);
                    }
                    else
                    {
                        var trackOBj = data.Tracks.Where(t => t.Id == track.Id).Include(t => t.RelatedArtists).Single();
                        var newTrack = new Track() { Id = trackOBj.Id, TrackTitle = trackOBj.TrackTitle, RelatedArtists = trackOBj.RelatedArtists };
                        adminMusicEditMusicViewModel.Music.Tracks.Add(newTrack);
                    }
                }

                //adminMusicEditMusicViewModel.Music.Tracks = adminMusicEditMusicViewModel.Music.Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();


                adminMusicEditMusicViewModel.state = "edit_track";
                adminMusicEditMusicViewModel.editTrackId = track_id;

                adminMusicEditMusicViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminMusicEditMusicViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminMusicEditMusicViewModel.AllVideos = data.Videos.AsEnumerable().Select(Video => new Video() { Id = Video.Id, VideoTitle = Video.VideoTitle }).ToList();
                adminMusicEditMusicViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();

            }

            return View("EditMusic", adminMusicEditMusicViewModel);
        }









        public IActionResult NewAddSection()
        {
            AdminMusicViewModel adminMusicViewModel = new AdminMusicViewModel();

            using (Data data = new Data())
            {
                adminMusicViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminMusicViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminMusicViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminMusicViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminMusicViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminMusicViewModel.Track = new Track();
                adminMusicViewModel.AddSectionVisible = true;
                adminMusicViewModel.ViewForUpdate = false;
            }

            return View("index", adminMusicViewModel);
        }

        [HttpPost]
        public IActionResult Save(AdminMusicViewModel adminMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {

            using (var data = new Data())
            {
                var track = GenerateTrackFromAdminMusicViewModel(data, adminMusicViewModel, pic_vertical_upload, pic_horizontal_upload, track_file, State.Saved);
                data.Tracks.Add(track);
                data.SaveChanges();
            }

            return RedirectToAction(nameof(NewAddSection));
        }

        [HttpPost]
        public IActionResult EditSave(int track_id, AdminMusicViewModel adminMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {

            using (var data = new Data())
            {
                var track = GenerateTrackFromAdminMusicViewModel(data, adminMusicViewModel, pic_vertical_upload, pic_horizontal_upload, track_file, State.Saved);


                //get the existing track
                var exixstingTrack = data.Tracks.Where(t => t.Id == track_id)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Include(t => t.RelatedStyles)
                    .Include(t => t.RelatedVideos)
                    .Include(t => t.BuyLinks)
                    .Single();

                //track.Id = exixstingTrack.Id;

                //check whether the artworks or track is changed, if not restord from the `exixstingTrack`

                if (string.IsNullOrEmpty(track.ArtWorkSquare))
                {
                    track.ArtWorkSquare = exixstingTrack.ArtWorkSquare;
                }
                if (string.IsNullOrEmpty(track.ArtWorkHoriontal))
                {
                    track.ArtWorkHoriontal = exixstingTrack.ArtWorkHoriontal;
                }
                if (string.IsNullOrEmpty(track.TrackPath))
                {
                    track.TrackPath = exixstingTrack.TrackPath;
                }





                //all the nesated objects that has to be removed
                exixstingTrack.RelatedArtists.Clear();
                exixstingTrack.RelatedJournals.Clear();
                exixstingTrack.RelatedStyles.Clear();
                exixstingTrack.RelatedVideos.Clear();
                exixstingTrack.BuyLinks.Clear();

                data.SaveChanges();


                exixstingTrack.AppleLink = track.AppleLink;
                exixstingTrack.ArtWorkHoriontal = track.ArtWorkHoriontal;
                exixstingTrack.ArtWorkSquare = track.ArtWorkSquare;
                exixstingTrack.BPM = track.BPM;
                exixstingTrack.BuyLinks = track.BuyLinks;
                exixstingTrack.CatalogNumber = track.CatalogNumber;
                exixstingTrack.Description = track.Description;
                exixstingTrack.ISRC = track.ISRC;
                exixstingTrack.MainGenre = track.MainGenre;
                exixstingTrack.MetaDescription = track.MetaDescription;
                exixstingTrack.MetaTitle = track.MetaTitle;
                exixstingTrack.OfferFreeDownload = track.OfferFreeDownload;
                exixstingTrack.PublishDate = track.PublishDate;
                exixstingTrack.RelatedArtists = track.RelatedArtists;
                exixstingTrack.RelatedJournals = track.RelatedJournals;
                exixstingTrack.RelatedStyles = track.RelatedStyles;
                exixstingTrack.RelatedVideos = track.RelatedVideos;
                exixstingTrack.SoundCloudLink = track.SoundCloudLink;
                exixstingTrack.SpotifyLink = track.SpotifyLink;
                exixstingTrack.StartCountDown = track.StartCountDown;
                exixstingTrack.State = State.Saved;
                exixstingTrack.SubGenre = track.SubGenre;
                exixstingTrack.TopPromo = track.TopPromo;
                exixstingTrack.TrackNoInMusicCollection = track.TrackNoInMusicCollection;
                exixstingTrack.TrackPath = track.TrackPath;
                exixstingTrack.TrackTitle = track.TrackTitle;
                exixstingTrack.YoutubeLink = track.YoutubeLink;
                exixstingTrack.YoutubeMusicStreamLink = track.YoutubeMusicStreamLink;




                exixstingTrack.State = State.Saved;
                data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));
        }


        [HttpPost]
        public IActionResult EditPublish(int track_id, AdminMusicViewModel adminMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {

            using (var data = new Data())
            {
                var track = GenerateTrackFromAdminMusicViewModel(data, adminMusicViewModel, pic_vertical_upload, pic_horizontal_upload, track_file, State.Published);

                if (track.PublishDate == DateTime.MinValue) // means the publish day is not set
                {
                    track.PublishDate = DateTime.Now;
                }

                if (track.PublishDate >= DateTime.Now)
                {
                    track.State = State.ToBePublished;
                }
                else
                {
                    track.State = State.Published;
                }



                //get the existing track
                var exixstingTrack = data.Tracks.Where(t => t.Id == track_id)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Include(t => t.RelatedStyles)
                    .Include(t => t.RelatedVideos)
                    .Include(t => t.BuyLinks)
                    .Single();

                //track.Id = exixstingTrack.Id;

                //check whether the artworks or track is changed, if not restord from the `exixstingTrack`

                if (string.IsNullOrEmpty(track.ArtWorkSquare))
                {
                    track.ArtWorkSquare = exixstingTrack.ArtWorkSquare;
                }
                if (string.IsNullOrEmpty(track.ArtWorkHoriontal))
                {
                    track.ArtWorkHoriontal = exixstingTrack.ArtWorkHoriontal;
                }
                if (string.IsNullOrEmpty(track.TrackPath))
                {
                    track.TrackPath = exixstingTrack.TrackPath;
                }





                //all the nesated objects that has to be removed
                exixstingTrack.RelatedArtists.Clear();
                exixstingTrack.RelatedJournals.Clear();
                exixstingTrack.RelatedStyles.Clear();
                exixstingTrack.RelatedVideos.Clear();
                exixstingTrack.BuyLinks.Clear();

                data.SaveChanges();


                exixstingTrack.AppleLink = track.AppleLink;
                exixstingTrack.ArtWorkHoriontal = track.ArtWorkHoriontal;
                exixstingTrack.ArtWorkSquare = track.ArtWorkSquare;
                exixstingTrack.BPM = track.BPM;
                exixstingTrack.BuyLinks = track.BuyLinks;
                exixstingTrack.CatalogNumber = track.CatalogNumber;
                exixstingTrack.Description = track.Description;
                exixstingTrack.ISRC = track.ISRC;
                exixstingTrack.MainGenre = track.MainGenre;
                exixstingTrack.MetaDescription = track.MetaDescription;
                exixstingTrack.MetaTitle = track.MetaTitle;
                exixstingTrack.OfferFreeDownload = track.OfferFreeDownload;
                exixstingTrack.PublishDate = track.PublishDate;
                exixstingTrack.RelatedArtists = track.RelatedArtists;
                exixstingTrack.RelatedJournals = track.RelatedJournals;
                exixstingTrack.RelatedStyles = track.RelatedStyles;
                exixstingTrack.RelatedVideos = track.RelatedVideos;
                exixstingTrack.SoundCloudLink = track.SoundCloudLink;
                exixstingTrack.SpotifyLink = track.SpotifyLink;
                exixstingTrack.StartCountDown = track.StartCountDown;

                exixstingTrack.SubGenre = track.SubGenre;
                exixstingTrack.TopPromo = track.TopPromo;
                exixstingTrack.TrackNoInMusicCollection = track.TrackNoInMusicCollection;
                exixstingTrack.TrackPath = track.TrackPath;
                exixstingTrack.TrackTitle = track.TrackTitle;
                exixstingTrack.YoutubeLink = track.YoutubeLink;
                exixstingTrack.YoutubeMusicStreamLink = track.YoutubeMusicStreamLink;




                exixstingTrack.State = State.Published;
                data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));
        }



        [HttpPost]
        public IActionResult EditMusicEditTrackSave(AdminMusicEditMusicViewModel adminMusicEditMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {




            //existing track

            int existing_track_id = int.Parse(Request.Form["track_id_"]);

            using (Data data = new Data())
            {
                var exsisting_track = data.Tracks.Where(t => t.Id == existing_track_id)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Include(t => t.RelatedStyles)
                    .Include(t => t.RelatedVideos)
                    .Include(t => t.BuyLinks)
                    .FirstOrDefault();


                int loopvar = int.Parse(Request.Form["loop_var"]);



                var slsdkfjdslf = 0;

                var NewTrack = new Track();
                NewTrack.AppleLink = Request.Form[$"Music.Tracks[{loopvar}].AppleLink"];
                NewTrack.ArtWorkHoriontal = Request.Form["pic_horizontal_upload"];
                NewTrack.ArtWorkSquare = Request.Form["pic_vertical_upload"];
                NewTrack.BPM = Request.Form[$"Music.Tracks[{loopvar}].BPM"];
                //NewTrack.BuyLinks = Request.Form[""];
                NewTrack.CatalogNumber = Request.Form[$"Music.Tracks[{loopvar}].CatalogNumber"];
                NewTrack.Description = Request.Form[$"Music.Tracks[{loopvar}].Description"];
                NewTrack.ISRC = Request.Form[$"Music.Tracks[{loopvar}].ISRC"];
                NewTrack.MainGenre = Request.Form[$"Music.Tracks[{loopvar}].MainGenre"];
                NewTrack.MetaDescription = Request.Form[$"Music.Tracks[{loopvar}].MetaDescription"];
                NewTrack.MetaTitle = Request.Form[$"Music.Tracks[{loopvar}].MetaTitle"];
                NewTrack.OfferFreeDownload = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].OfferFreeDownload : exsisting_track.OfferFreeDownload;
                NewTrack.PublishDate = DateTime.Parse(Request.Form[$"Music.Tracks[{loopvar}].PublishDate"]);
                //NewTrack.RelatedArtists = Request.Form[""];
                //NewTrack.RelatedJournals = Request.Form[""];
                //NewTrack.RelatedStyles = Request.Form[""];
                //NewTrack.RelatedVideos = Request.Form[""];
                NewTrack.SoundCloudLink = Request.Form[$"Music.Tracks[{loopvar}].SoundCloudLink"];

                NewTrack.SpotifyLink = Request.Form[$"Music.Tracks[{loopvar}].SpotifyLink"];
                NewTrack.StartCountDown = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].StartCountDown : exsisting_track.StartCountDown;
                NewTrack.SubGenre = Request.Form[$"Music.Tracks[{loopvar}].SubGenre"];
                NewTrack.TopPromo = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].TopPromo : exsisting_track.TopPromo;
                NewTrack.TrackTitle = Request.Form[$"Music.Tracks[{loopvar}].TrackTitle"];
                NewTrack.YoutubeLink = Request.Form[$"Music.Tracks[{loopvar}].YoutubeLink"];
                NewTrack.YoutubeMusicStreamLink = Request.Form[$"Music.Tracks[{loopvar}].YoutubeMusicStreamLink"];





                //related artists
                exsisting_track.RelatedArtists.Clear();
                var relatedArtistIdArray = Request.Form["select_related_artists"];
                if (relatedArtistIdArray.Count > 0)
                {
                    foreach (string id in relatedArtistIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Artists.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedArtists.Add(entity);
                    }
                }

                //select_related_videos
                exsisting_track.RelatedVideos.Clear();
                var relatedVideoIdArray = Request.Form["select_related_videos"];
                if (relatedVideoIdArray.Count > 0)
                {
                    foreach (string id in relatedVideoIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Videos.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedVideos.Add(entity);
                    }
                }


                //select_related_styles
                exsisting_track.RelatedStyles.Clear();
                var relatedStyleIdArray = Request.Form["select_related_styles"];
                if (relatedStyleIdArray.Count > 0)
                {
                    foreach (string id in relatedStyleIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Styles.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedStyles.Add(entity);
                    }
                }


                //select_related_journal_entry
                exsisting_track.RelatedJournals.Clear();
                var relatedJournalIdArray = Request.Form["select_related_journal_entry"];
                if (relatedJournalIdArray.Count > 0)
                {
                    foreach (string id in relatedJournalIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Journals.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedJournals.Add(entity);
                    }
                }


                //buylinks
                exsisting_track.BuyLinks.Clear();
                data.SaveChanges();
                var input_text_array = Request.Form["input_text_buylink"];
                if (input_text_array.Count > 0)
                {
                    foreach (string link in input_text_array)
                    {
                        if (link != "")
                        {
                            exsisting_track.BuyLinks.Add(new TrackBuyLink() { Link = link });

                        }
                    }
                }

                data.SaveChanges();


                if (pic_vertical_upload == null || pic_vertical_upload.Length == 0)
                {


                }
                else
                {
                    exsisting_track.ArtWorkSquare = saveIFromFile(pic_vertical_upload);

                }



                if (pic_horizontal_upload == null || pic_horizontal_upload.Length == 0)
                {


                }
                else
                {
                    exsisting_track.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);

                }


                exsisting_track.AppleLink = NewTrack.AppleLink;
                exsisting_track.BPM = NewTrack.BPM;
                exsisting_track.CatalogNumber = NewTrack.CatalogNumber;
                exsisting_track.Description = NewTrack.Description;
                exsisting_track.ISRC = NewTrack.ISRC;
                exsisting_track.MainGenre = NewTrack.MainGenre;
                exsisting_track.MetaDescription = NewTrack.MetaDescription;
                exsisting_track.MetaTitle = NewTrack.MetaTitle;
                exsisting_track.OfferFreeDownload = NewTrack.OfferFreeDownload;
                exsisting_track.PublishDate = NewTrack.PublishDate;
                exsisting_track.SoundCloudLink = NewTrack.SoundCloudLink;
                exsisting_track.SpotifyLink = NewTrack.SpotifyLink;
                exsisting_track.StartCountDown = NewTrack.StartCountDown;
                exsisting_track.State = State.Saved;
                exsisting_track.SubGenre = NewTrack.SubGenre;
                exsisting_track.TopPromo = NewTrack.TopPromo;
                exsisting_track.TrackNoInMusicCollection = NewTrack.TrackNoInMusicCollection;
                exsisting_track.TrackPath = NewTrack.TrackPath;
                exsisting_track.TrackTitle = NewTrack.TrackTitle;
                exsisting_track.YoutubeLink = NewTrack.YoutubeLink;
                exsisting_track.YoutubeMusicStreamLink = NewTrack.YoutubeMusicStreamLink;
                data.SaveChanges();


                return View("Index", new AdminMusicViewModel());






            }









        }

        public IActionResult EditMusicEditTrackPublish(AdminMusicEditMusicViewModel adminMusicEditMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {




            //existing track

            int existing_track_id = int.Parse(Request.Form["track_id_"]);

            using (Data data = new Data())
            {
                var exsisting_track = data.Tracks.Where(t => t.Id == existing_track_id)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Include(t => t.RelatedStyles)
                    .Include(t => t.RelatedVideos)
                    .Include(t => t.BuyLinks)
                    .FirstOrDefault();


                int loopvar = int.Parse(Request.Form["loop_var"]);



                var slsdkfjdslf = 0;

                var NewTrack = new Track();
                NewTrack.AppleLink = Request.Form[$"Music.Tracks[{loopvar}].AppleLink"];
                NewTrack.ArtWorkHoriontal = Request.Form["pic_horizontal_upload"];
                NewTrack.ArtWorkSquare = Request.Form["pic_vertical_upload"];
                NewTrack.BPM = Request.Form[$"Music.Tracks[{loopvar}].BPM"];
                //NewTrack.BuyLinks = Request.Form[""];
                NewTrack.CatalogNumber = Request.Form[$"Music.Tracks[{loopvar}].CatalogNumber"];
                NewTrack.Description = Request.Form[$"Music.Tracks[{loopvar}].Description"];
                NewTrack.ISRC = Request.Form[$"Music.Tracks[{loopvar}].ISRC"];
                NewTrack.MainGenre = Request.Form[$"Music.Tracks[{loopvar}].MainGenre"];
                NewTrack.MetaDescription = Request.Form[$"Music.Tracks[{loopvar}].MetaDescription"];
                NewTrack.MetaTitle = Request.Form[$"Music.Tracks[{loopvar}].MetaTitle"];
                NewTrack.OfferFreeDownload = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].OfferFreeDownload : exsisting_track.OfferFreeDownload;
                NewTrack.PublishDate = DateTime.Parse(Request.Form[$"Music.Tracks[{loopvar}].PublishDate"]);
                //NewTrack.RelatedArtists = Request.Form[""];
                //NewTrack.RelatedJournals = Request.Form[""];
                //NewTrack.RelatedStyles = Request.Form[""];
                //NewTrack.RelatedVideos = Request.Form[""];
                NewTrack.SoundCloudLink = Request.Form[$"Music.Tracks[{loopvar}].SoundCloudLink"];

                NewTrack.SpotifyLink = Request.Form[$"Music.Tracks[{loopvar}].SpotifyLink"];
                NewTrack.StartCountDown = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].StartCountDown : exsisting_track.StartCountDown;
                NewTrack.SubGenre = Request.Form[$"Music.Tracks[{loopvar}].SubGenre"];
                NewTrack.TopPromo = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].TopPromo : exsisting_track.TopPromo;
                NewTrack.TrackTitle = Request.Form[$"Music.Tracks[{loopvar}].TrackTitle"];
                NewTrack.YoutubeLink = Request.Form[$"Music.Tracks[{loopvar}].YoutubeLink"];
                NewTrack.YoutubeMusicStreamLink = Request.Form[$"Music.Tracks[{loopvar}].YoutubeMusicStreamLink"];





                //related artists
                exsisting_track.RelatedArtists.Clear();
                var relatedArtistIdArray = Request.Form["select_related_artists"];
                if (relatedArtistIdArray.Count > 0)
                {
                    foreach (string id in relatedArtistIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Artists.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedArtists.Add(entity);
                    }
                }

                //select_related_videos
                exsisting_track.RelatedVideos.Clear();
                var relatedVideoIdArray = Request.Form["select_related_videos"];
                if (relatedVideoIdArray.Count > 0)
                {
                    foreach (string id in relatedVideoIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Videos.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedVideos.Add(entity);
                    }
                }


                //select_related_styles
                exsisting_track.RelatedStyles.Clear();
                var relatedStyleIdArray = Request.Form["select_related_styles"];
                if (relatedStyleIdArray.Count > 0)
                {
                    foreach (string id in relatedStyleIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Styles.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedStyles.Add(entity);
                    }
                }


                //select_related_journal_entry
                exsisting_track.RelatedJournals.Clear();
                var relatedJournalIdArray = Request.Form["select_related_journal_entry"];
                if (relatedJournalIdArray.Count > 0)
                {
                    foreach (string id in relatedJournalIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Journals.Where(a => a.Id == intid).First();
                        exsisting_track.RelatedJournals.Add(entity);
                    }
                }


                //buylinks
                exsisting_track.BuyLinks.Clear();
                data.SaveChanges();
                var input_text_array = Request.Form["input_text_buylink"];
                if (input_text_array.Count > 0)
                {
                    foreach (string link in input_text_array)
                    {
                        if (link != "")
                        {
                            exsisting_track.BuyLinks.Add(new TrackBuyLink() { Link = link });

                        }
                    }
                }

                data.SaveChanges();


                if (pic_vertical_upload == null || pic_vertical_upload.Length == 0)
                {


                }
                else
                {
                    exsisting_track.ArtWorkSquare = saveIFromFile(pic_vertical_upload);

                }



                if (pic_horizontal_upload == null || pic_horizontal_upload.Length == 0)
                {


                }
                else
                {
                    exsisting_track.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);

                }


                exsisting_track.AppleLink = NewTrack.AppleLink;
                exsisting_track.BPM = NewTrack.BPM;
                exsisting_track.CatalogNumber = NewTrack.CatalogNumber;
                exsisting_track.Description = NewTrack.Description;
                exsisting_track.ISRC = NewTrack.ISRC;
                exsisting_track.MainGenre = NewTrack.MainGenre;
                exsisting_track.MetaDescription = NewTrack.MetaDescription;
                exsisting_track.MetaTitle = NewTrack.MetaTitle;
                exsisting_track.OfferFreeDownload = NewTrack.OfferFreeDownload;
                exsisting_track.PublishDate = NewTrack.PublishDate;
                exsisting_track.SoundCloudLink = NewTrack.SoundCloudLink;
                exsisting_track.SpotifyLink = NewTrack.SpotifyLink;
                exsisting_track.StartCountDown = NewTrack.StartCountDown;
                exsisting_track.State = State.Published;
                exsisting_track.SubGenre = NewTrack.SubGenre;
                exsisting_track.TopPromo = NewTrack.TopPromo;
                exsisting_track.TrackNoInMusicCollection = NewTrack.TrackNoInMusicCollection;
                exsisting_track.TrackPath = NewTrack.TrackPath;
                exsisting_track.TrackTitle = NewTrack.TrackTitle;
                exsisting_track.YoutubeLink = NewTrack.YoutubeLink;
                exsisting_track.YoutubeMusicStreamLink = NewTrack.YoutubeMusicStreamLink;

                exsisting_track.PublishDate = (exsisting_track.PublishDate == DateTime.MinValue) ? DateTime.Now : exsisting_track.PublishDate;
                data.SaveChanges();




                return View("Index", new AdminMusicViewModel());






            }









        }


        [HttpPost]
        public IActionResult Publish(AdminMusicViewModel adminMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {

            try
            {
                using (var data = new Data())
                {
                    var track = GenerateTrackFromAdminMusicViewModel(data, adminMusicViewModel, pic_vertical_upload, pic_horizontal_upload, track_file, State.Published);

                    if (track.PublishDate == DateTime.MinValue) // means the publish day is not set
                    {
                        track.PublishDate = DateTime.Now;
                    }

                    if (track.PublishDate >= DateTime.Now)
                    {
                        track.State = State.ToBePublished;
                    }
                    else
                    {
                        track.State = State.Published;
                    }

                    data.Tracks.Add(track);
                    data.SaveChanges();
                }


                //so if there is no issue 
                return View("Index", new AdminMusicViewModel());

            }
            catch (Exception ex)
            {
                //there is is an issue so we redirect with `NewAddSection`
                return RedirectToAction(nameof(Index));

            }

        }


        [HttpPost]
        public IActionResult SaveMusic(int track1Id, int track2Id, AdminMusicCombineViewModel adminMusicCombineViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            using (var data = new Data())
            {
                var track1 = data.Tracks.Where(t => t.Id == track1Id).FirstOrDefault();
                var track2 = data.Tracks.Where(t => t.Id == track2Id).FirstOrDefault();



                var music = GenerateMusicFromCombineMusicViewModel(data, adminMusicCombineViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);
                music.Tracks.Add(track1);
                music.Tracks.Add(track2);

                data.Musics.Add(music);
                data.SaveChanges();
            }

            return RedirectToAction("index", new AdminMusicViewModel());


        }

        //PublishMusic

        [HttpPost]
        public IActionResult PublishMusic(int track1Id, int track2Id, AdminMusicCombineViewModel adminMusicCombineViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            try
            {
                using (var data = new Data())
                {
                    var track1 = data.Tracks.Where(t => t.Id == track1Id).FirstOrDefault();
                    var track2 = data.Tracks.Where(t => t.Id == track2Id).FirstOrDefault();


                    track1.TrackNoInMusicCollection = 1;
                    track2.TrackNoInMusicCollection = 2;



                    var music = GenerateMusicFromCombineMusicViewModel(data, adminMusicCombineViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);

                    if (music.PublishDate == DateTime.MinValue) // means the publish day is not set
                    {
                        music.PublishDate = DateTime.Now;
                    }

                    music.Tracks.Add(track1);
                    music.Tracks.Add(track2);

                    data.Musics.Add(music);
                    data.SaveChanges();
                }

                return RedirectToAction("index", new AdminMusicViewModel());
            }
            catch (Exception ex)
            {
                return RedirectToAction("index", new AdminMusicViewModel());
            }

        }

        Track GenerateTrackFromAdminMusicEditMusicViewModel(int track_id, Data data, AdminMusicEditMusicViewModel adminMusicEditMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file, State state)
        {
            var trackFromModel = adminMusicEditMusicViewModel.Music.Tracks.Where(t => t.Id == track_id).FirstOrDefault();
            try
            {
                Track track = new Track()
                {
                    AppleLink = trackFromModel.AppleLink,
                    PublishDate = trackFromModel.PublishDate,
                    BPM = trackFromModel.BPM,
                    BuyLinks = trackFromModel.BuyLinks,
                    ArtWorkHoriontal = "",
                    ArtWorkSquare = "",
                    CatalogNumber = trackFromModel.CatalogNumber,
                    Description = trackFromModel.Description,
                    ISRC = trackFromModel.ISRC,
                    MainGenre = trackFromModel.MainGenre,
                    MetaDescription = trackFromModel.MetaDescription,
                    MetaTitle = trackFromModel.MetaTitle,
                    OfferFreeDownload = trackFromModel.OfferFreeDownload,
                    SoundCloudLink = trackFromModel.SoundCloudLink,
                    SpotifyLink = trackFromModel.SpotifyLink,
                    StartCountDown = trackFromModel.StartCountDown,
                    SubGenre = trackFromModel.SubGenre,
                    TopPromo = trackFromModel.TopPromo,
                    TrackNoInMusicCollection = trackFromModel.TrackNoInMusicCollection,
                    TrackTitle = trackFromModel.TrackTitle,
                    YoutubeLink = trackFromModel.YoutubeLink,
                    YoutubeMusicStreamLink = trackFromModel.YoutubeMusicStreamLink,
                    State = state
                };

                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    track.ArtWorkSquare = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    track.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);
                }
                if (track_file != null && track_file.Length > 0)
                {
                    track.TrackPath = saveIFromFile(track_file);
                }



                //request form data


                var buylinks = Request.Form[Global.BuyLinkTXTName];
                foreach (var buylinksIdString in buylinks)
                {
                    track.BuyLinks.Add(new TrackBuyLink() { Link = buylinksIdString });

                }



                var relatedVideos = Request.Form[Global.RelatedVideosDDLName];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    track.RelatedVideos.Add(relatedVideo);

                }

                var relatedStyles = Request.Form[Global.RelatedStylesDDLName];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    track.RelatedStyles.Add(relatedStyle);

                }

                var relatedJournals = Request.Form[Global.RelatedJournalsDDLName];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    track.RelatedJournals.Add(relatedJournal);



                }

                var relatedArtists = Request.Form[Global.RelatedArtistsDDLName];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.Where(t => t.Id == artistId).FirstOrDefault();
                    track.RelatedArtists.Add(relatedArtist);



                }

                return track;
            }
            catch (Exception ex)
            {
                return null;
            }

        }


        private Track GenerateTrackFromAdminMusicViewModel(Data data, AdminMusicViewModel adminMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file, State state)
        {
            try
            {
                Track track = new Track()
                {
                    AppleLink = adminMusicViewModel.Track.AppleLink,
                    PublishDate = adminMusicViewModel.Track.PublishDate,
                    BPM = adminMusicViewModel.Track.BPM,
                    BuyLinks = adminMusicViewModel.Track.BuyLinks,
                    ArtWorkHoriontal = "",
                    ArtWorkSquare = "",
                    CatalogNumber = adminMusicViewModel.Track.CatalogNumber,
                    Description = adminMusicViewModel.Track.Description,
                    ISRC = adminMusicViewModel.Track.ISRC,
                    MainGenre = adminMusicViewModel.Track.MainGenre,
                    MetaDescription = adminMusicViewModel.Track.MetaDescription,
                    MetaTitle = adminMusicViewModel.Track.MetaTitle,
                    OfferFreeDownload = adminMusicViewModel.Track.OfferFreeDownload,
                    SoundCloudLink = adminMusicViewModel.Track.SoundCloudLink,
                    SpotifyLink = adminMusicViewModel.Track.SpotifyLink,
                    StartCountDown = adminMusicViewModel.Track.StartCountDown,
                    SubGenre = adminMusicViewModel.Track.SubGenre,
                    TopPromo = adminMusicViewModel.Track.TopPromo,
                    TrackNoInMusicCollection = adminMusicViewModel.Track.TrackNoInMusicCollection,
                    TrackTitle = adminMusicViewModel.Track.TrackTitle,
                    YoutubeLink = adminMusicViewModel.Track.YoutubeLink,
                    YoutubeMusicStreamLink = adminMusicViewModel.Track.YoutubeMusicStreamLink,
                    State = state
                };


                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    track.ArtWorkSquare = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    track.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);
                }
                if (track_file != null && track_file.Length > 0)
                {
                    track.TrackPath = saveIFromFile(track_file);
                }



                //request form data


                var buylinks = Request.Form[Global.BuyLinkTXTName];
                foreach (var buylinksIdString in buylinks)
                {
                    track.BuyLinks.Add(new TrackBuyLink() { Link = buylinksIdString });

                }



                var relatedVideos = Request.Form[Global.RelatedVideosDDLName];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    track.RelatedVideos.Add(relatedVideo);

                }

                var relatedStyles = Request.Form[Global.RelatedStylesDDLName];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    track.RelatedStyles.Add(relatedStyle);

                }

                var relatedJournals = Request.Form[Global.RelatedJournalsDDLName];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    track.RelatedJournals.Add(relatedJournal);



                }

                var relatedArtists = Request.Form[Global.RelatedArtistsDDLName];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.Where(t => t.Id == artistId).FirstOrDefault();
                    track.RelatedArtists.Add(relatedArtist);



                }

                return track;
            }
            catch (Exception ex)
            {
                return null;
            }
        }


        private Music GenerateMusicFromCombineMusicViewModel(Data data, AdminMusicCombineViewModel adminMusicCombineViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, State state)
        {
            try
            {
                Music music = new Music()
                {
                    AppleAlbumLink = adminMusicCombineViewModel.Music.AppleAlbumLink,
                    Description = adminMusicCombineViewModel.Music.Description,
                    MetaDescription = adminMusicCombineViewModel.Music.MetaDescription,
                    MetaTitle = adminMusicCombineViewModel.Music.MetaTitle,
                    PublishDate = adminMusicCombineViewModel.Music.PublishDate,
                    SoundCloudAlbumLink = adminMusicCombineViewModel.Music.SoundCloudAlbumLink,
                    SpotifyAlbumLink = adminMusicCombineViewModel.Music.SpotifyAlbumLink,
                    StartCountDown = adminMusicCombineViewModel.Music.StartCountDown,
                    Title = adminMusicCombineViewModel.Music.Title,
                    TopPromo = adminMusicCombineViewModel.Music.TopPromo,
                    YoutubeAlbumLink = adminMusicCombineViewModel.Music.YoutubeAlbumLink,
                    YoutubeMusicAlbumLink = adminMusicCombineViewModel.Music.YoutubeMusicAlbumLink,
                    State = state
                };



                //music.Tracks.Add(adminMusicCombineViewModel.Track1);
                //music.Tracks.Add(adminMusicCombineViewModel.Track2);



                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    music.ArtWorkSquare = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    music.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);
                }




                //request form data


                var buylinks = Request.Form[Global.BuyLinkTXTName];
                foreach (var buylinksIdString in buylinks)
                {
                    music.BuyLinks.Add(new MusicBuyLink() { Link = buylinksIdString });

                }




                music.MusicCollectionType = (MusicCollectionType)Enum.Parse(typeof(MusicCollectionType), Request.Form["coltype"].ToString());



                return music;
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }

    }



}
