﻿@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <link rel="stylesheet" href="~/css/ArtistsStartPage.css" />


}

<div class="container">
    <header></header>
    <p id="p_introduction">
        <b>Artists </b>is the official news channel for stockholm soundorm. iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.
    </p>

    <div class="profile_box_container">
        @{
            using (Data data = new Data())
            {
                var Artists = data.Artists.ToList();

                foreach (var artist in Artists)
                {
                    <div class="profile_box">
                        @{var imageSource = Url.Content($"~/Uploads/{artist.VerticalImageUrl}"); }
                        <a href="@Url.Action("Index","Artist",new { id = artist.Id })">
                            <img src=@imageSource />

                        </a>
                        <label class="artist_name">@artist.ArtistName</label>

                    </div>
                }

            }

        }
    </div>

</div>


