﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration2 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.Styles", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.Tracks", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.Styles", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.Tracks", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.Videos", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.Tracks", "Artist_Id", "dbo.Artists");
            DropIndex("dbo.Styles", new[] { "Journal_Id" });
            DropIndex("dbo.Styles", new[] { "Video_Id" });
            DropIndex("dbo.Tracks", new[] { "Journal_Id" });
            DropIndex("dbo.Tracks", new[] { "Video_Id" });
            DropIndex("dbo.Tracks", new[] { "Artist_Id" });
            DropIndex("dbo.Videos", new[] { "Journal_Id" });
            CreateTable(
                "dbo.StyleJournals",
                c => new
                    {
                        Style_Id = c.Int(nullable: false),
                        Journal_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Style_Id, t.Journal_Id })
                .ForeignKey("dbo.Styles", t => t.Style_Id, cascadeDelete: true)
                .ForeignKey("dbo.Journals", t => t.Journal_Id, cascadeDelete: true)
                .Index(t => t.Style_Id)
                .Index(t => t.Journal_Id);
            
            CreateTable(
                "dbo.TrackArtists",
                c => new
                    {
                        Track_Id = c.Int(nullable: false),
                        Artist_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Track_Id, t.Artist_Id })
                .ForeignKey("dbo.Tracks", t => t.Track_Id, cascadeDelete: true)
                .ForeignKey("dbo.Artists", t => t.Artist_Id, cascadeDelete: true)
                .Index(t => t.Track_Id)
                .Index(t => t.Artist_Id);
            
            CreateTable(
                "dbo.TrackJournals",
                c => new
                    {
                        Track_Id = c.Int(nullable: false),
                        Journal_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Track_Id, t.Journal_Id })
                .ForeignKey("dbo.Tracks", t => t.Track_Id, cascadeDelete: true)
                .ForeignKey("dbo.Journals", t => t.Journal_Id, cascadeDelete: true)
                .Index(t => t.Track_Id)
                .Index(t => t.Journal_Id);
            
            CreateTable(
                "dbo.TrackStyles",
                c => new
                    {
                        Track_Id = c.Int(nullable: false),
                        Style_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Track_Id, t.Style_Id })
                .ForeignKey("dbo.Tracks", t => t.Track_Id, cascadeDelete: true)
                .ForeignKey("dbo.Styles", t => t.Style_Id, cascadeDelete: true)
                .Index(t => t.Track_Id)
                .Index(t => t.Style_Id);
            
            CreateTable(
                "dbo.VideoJournals",
                c => new
                    {
                        Video_Id = c.Int(nullable: false),
                        Journal_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Video_Id, t.Journal_Id })
                .ForeignKey("dbo.Videos", t => t.Video_Id, cascadeDelete: true)
                .ForeignKey("dbo.Journals", t => t.Journal_Id, cascadeDelete: true)
                .Index(t => t.Video_Id)
                .Index(t => t.Journal_Id);
            
            CreateTable(
                "dbo.VideoStyles",
                c => new
                    {
                        Video_Id = c.Int(nullable: false),
                        Style_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Video_Id, t.Style_Id })
                .ForeignKey("dbo.Videos", t => t.Video_Id, cascadeDelete: true)
                .ForeignKey("dbo.Styles", t => t.Style_Id, cascadeDelete: true)
                .Index(t => t.Video_Id)
                .Index(t => t.Style_Id);
            
            CreateTable(
                "dbo.VideoTracks",
                c => new
                    {
                        Video_Id = c.Int(nullable: false),
                        Track_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Video_Id, t.Track_Id })
                .ForeignKey("dbo.Videos", t => t.Video_Id, cascadeDelete: true)
                .ForeignKey("dbo.Tracks", t => t.Track_Id, cascadeDelete: true)
                .Index(t => t.Video_Id)
                .Index(t => t.Track_Id);
            
            DropColumn("dbo.Styles", "Journal_Id");
            DropColumn("dbo.Styles", "Video_Id");
            DropColumn("dbo.Tracks", "Journal_Id");
            DropColumn("dbo.Tracks", "Video_Id");
            DropColumn("dbo.Tracks", "Artist_Id");
            DropColumn("dbo.Videos", "Journal_Id");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Videos", "Journal_Id", c => c.Int());
            AddColumn("dbo.Tracks", "Artist_Id", c => c.Int());
            AddColumn("dbo.Tracks", "Video_Id", c => c.Int());
            AddColumn("dbo.Tracks", "Journal_Id", c => c.Int());
            AddColumn("dbo.Styles", "Video_Id", c => c.Int());
            AddColumn("dbo.Styles", "Journal_Id", c => c.Int());
            DropForeignKey("dbo.VideoTracks", "Track_Id", "dbo.Tracks");
            DropForeignKey("dbo.VideoTracks", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.VideoStyles", "Style_Id", "dbo.Styles");
            DropForeignKey("dbo.VideoStyles", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.VideoJournals", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.VideoJournals", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.TrackStyles", "Style_Id", "dbo.Styles");
            DropForeignKey("dbo.TrackStyles", "Track_Id", "dbo.Tracks");
            DropForeignKey("dbo.TrackJournals", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.TrackJournals", "Track_Id", "dbo.Tracks");
            DropForeignKey("dbo.TrackArtists", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.TrackArtists", "Track_Id", "dbo.Tracks");
            DropForeignKey("dbo.StyleJournals", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.StyleJournals", "Style_Id", "dbo.Styles");
            DropIndex("dbo.VideoTracks", new[] { "Track_Id" });
            DropIndex("dbo.VideoTracks", new[] { "Video_Id" });
            DropIndex("dbo.VideoStyles", new[] { "Style_Id" });
            DropIndex("dbo.VideoStyles", new[] { "Video_Id" });
            DropIndex("dbo.VideoJournals", new[] { "Journal_Id" });
            DropIndex("dbo.VideoJournals", new[] { "Video_Id" });
            DropIndex("dbo.TrackStyles", new[] { "Style_Id" });
            DropIndex("dbo.TrackStyles", new[] { "Track_Id" });
            DropIndex("dbo.TrackJournals", new[] { "Journal_Id" });
            DropIndex("dbo.TrackJournals", new[] { "Track_Id" });
            DropIndex("dbo.TrackArtists", new[] { "Artist_Id" });
            DropIndex("dbo.TrackArtists", new[] { "Track_Id" });
            DropIndex("dbo.StyleJournals", new[] { "Journal_Id" });
            DropIndex("dbo.StyleJournals", new[] { "Style_Id" });
            DropTable("dbo.VideoTracks");
            DropTable("dbo.VideoStyles");
            DropTable("dbo.VideoJournals");
            DropTable("dbo.TrackStyles");
            DropTable("dbo.TrackJournals");
            DropTable("dbo.TrackArtists");
            DropTable("dbo.StyleJournals");
            CreateIndex("dbo.Videos", "Journal_Id");
            CreateIndex("dbo.Tracks", "Artist_Id");
            CreateIndex("dbo.Tracks", "Video_Id");
            CreateIndex("dbo.Tracks", "Journal_Id");
            CreateIndex("dbo.Styles", "Video_Id");
            CreateIndex("dbo.Styles", "Journal_Id");
            AddForeignKey("dbo.Tracks", "Artist_Id", "dbo.Artists", "Id");
            AddForeignKey("dbo.Videos", "Journal_Id", "dbo.Journals", "Id");
            AddForeignKey("dbo.Tracks", "Video_Id", "dbo.Videos", "Id");
            AddForeignKey("dbo.Styles", "Video_Id", "dbo.Videos", "Id");
            AddForeignKey("dbo.Tracks", "Journal_Id", "dbo.Journals", "Id");
            AddForeignKey("dbo.Styles", "Journal_Id", "dbo.Journals", "Id");
        }
    }
}
