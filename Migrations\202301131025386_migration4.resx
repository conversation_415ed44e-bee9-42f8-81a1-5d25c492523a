﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>