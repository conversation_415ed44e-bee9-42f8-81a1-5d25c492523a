﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc
{
    public class Global
    {
        #region//these will be used across the websites to identify all the select elements by name
        public static string RelatedStylesDDLName { get; set; } = "select_related_styles";
        public static string RelatedArtistsDDLName { get; set; } = "select_related_artists";
        public static string RelatedJournalsDDLName { get; set; } = "select_related_journal_entry";
        public static string RelatedVideosDDLName { get; set; } = "select_related_videos";
        public static string RelatedTracksDDLName { get; set; } = "select_related_tracks";
        public static string BuyLinkTXTName { get; set; } = "input_text_buylink";

        #endregion

        public static string DomainLink { get; set; } = "https://stockholmsoundrome.com/";



        public static List<int> PromoAreaSmallerVideoIds = new List<int>();
        public static int PromoAreaBigVideoId = 0;
    }
}
