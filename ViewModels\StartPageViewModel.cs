﻿using stockhom_soundrom_mvc.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.ViewModels
{
    public class StartPageViewModel
    {
        public Track TopPromoTrack { get; set; }
        public bool IsCountDown { get; set; }

        public TimeSpan CountdownStartTime { get; set; }

        public bool IsTrackPromo { get; set; }



        public string Merch_hor_image { get; set; }

        public string Merch_ver_image { get; set; }

        public List<Style> Styles { get; set; } = new List<Style>();

        public List<Journal> Journals { get; set; } = new List<Journal>();

        public bool previewParty { get; set; }

        public string party_hor_image { get; set; }

        public string Party_sqr_image { get; set; }

    }

}
