﻿@model stockhom_soundrom_mvc.ViewModels.ArtistViewModel
@using System.Web
@using System.Data.Entity
@{
    ViewData["Title"] = "Artist";
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <link rel="stylesheet" href="~/css/Artist.css" />


}




<div class="container1">

    <div class="artist_main section">
        @*<img class="back_button" src="~/icons/back.png" />*@

        <h2 class="artist_name">@Model.Artist.ArtistName</h2>
        <hr>
        <br>
        <img class="artist_image" src=@Url.Content($"~/Uploads/{Model.Artist.VerticalImageUrl}")>

        <p id="DescriptionTruncated" class="DescriptionTruncated">@Model.Artist.Description</p>
        <p id="DescriptionExtended" class="DescriptionExtended">@Model.Artist.Description</p>
        <button id="ReadMoreButton" class="button1" onclick="ReadMoreButtonClicked();">READ MORE</button>

    </div>

    <div class="section">

        @using (Data data = new Data())
        {

            Track mostRecentTrack = null;

            try
            {
                mostRecentTrack = Model.Artist.Tracks.Where(t => t.State != State.Saved && t.PublishDate <= DateTime.Today.Date)
                            .OrderBy(t => t.PublishDate.Date).First();
            }
            catch (Exception ex)
            {

            }

            if (mostRecentTrack != null)
            {
                <label class="track_title_and_artist">@mostRecentTrack.TrackTitle - @Model.Artist.ArtistName</label>

                <div class="sc_embedment_container">
                    @Html.Raw(mostRecentTrack.SoundCloudLink)
                </div>


                <div class="div_social_media_icons">
                    @if (string.IsNullOrEmpty(mostRecentTrack.SpotifyLink) == false)
                    {
                        <div class="div_single_icon">
                            <a href="@mostRecentTrack.SpotifyLink">
                                <img src="~/icons/Spotify ikon svart.png" />
                            </a>
                        </div>

                    }


                    @if (string.IsNullOrEmpty(mostRecentTrack.AppleLink) == false)
                    {
                        <div class="div_single_icon">
                            <a href="@mostRecentTrack.AppleLink">
                                <img src="~/icons/Apple music ikon svart.png" />
                            </a>
                        </div>

                    }

                    @if (string.IsNullOrEmpty(mostRecentTrack.YoutubeMusicStreamLink) == false)
                    {
                        <div class="div_single_icon">
                            <a href="@mostRecentTrack.YoutubeMusicStreamLink">
                                <img src="~/icons/YouTube music ikon svart.png" />
                            </a>
                        </div>

                    }



                    @if (mostRecentTrack.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).Count() > 0)
                    {
                        <div class="div_single_icon">
                            <a href="@mostRecentTrack.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).First().Link">
                                <img src="~/icons/Beatport music ikon svart.png" />
                            </a>
                        </div>


                    }
                    @if (mostRecentTrack.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).Count() > 0)
                    {
                        <div class="div_single_icon">
                            <a href="@mostRecentTrack.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).First().Link">
                                <img src="~/icons/Bandcamp music ikon svart.png" />
                            </a>
                        </div>


                    }
                    @if (mostRecentTrack.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).Count() > 0)
                    {

                        <div class="div_single_icon">
                            <a href="@mostRecentTrack.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).First().Link">
                                <img src="~/icons/Juno Download ikon svart.png" />
                            </a>
                        </div>

                    }



                </div>


                var videoRelatedToTrack = data.Videos.Where(v => v.RelatedTracks.Any(v => v.Id == mostRecentTrack.Id)).FirstOrDefault();

                if (videoRelatedToTrack != null)
                {
                    <div class="video-container">
                        <iframe width="560" height="315" src="https://www.youtube.com/embed/@videoRelatedToTrack.YoutubeLink?controls=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                    </div>
                }




                <button id="LoadMoreTracks" class="button1">LOAD MORE TRACKS FROM THIS ARTIST</button>
            }
        }
    </div>


    @if (Model.Artist.Styles != null && Model.Artist.Styles.Any())
    {
        <div class="section">
            <div class="section_title">STYLES RELATED TO THIS ARTIST</div>

            <div class="related_styles">
                @foreach (var style in Model.Artist.Styles)
                {
                    <a href="#">
                        <img class="style_image" src=@Url.Content($"~/Uploads/{style.Picture1}") />
                    </a>
                }

            </div>
        </div>
    }


    @if (Model.Artist.Journals != null && Model.Artist.Journals.Any())
    {
        <div class="section">
            <div class="section_title">JOURNALS RELATED TO THIS ARTIST</div>


            @foreach (var journal in Model.Artist.Journals)
            {
                <div class="related_journal">
                    <a href="@Url.Action("Index","Journal",new { id = journal.Id })">
                        <img class="desktopImage" src=@Url.Content($"~/Uploads/{journal.DesktopImageUrl}") />
                        <img class="mobileImage" src=@Url.Content($"~/Uploads/{journal.MobileImageUrl}") />
                    </a>
                </div>
            }
        </div>
    }




</div>


@section Scripts{

    <script src="~/js/Artist.js"></script>


}