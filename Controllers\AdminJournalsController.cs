﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using stockhom_soundrom_mvc.ViewModels;
using stockhom_soundrom_mvc.Models;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.Data.Entity;

namespace stockhom_soundrom_mvc.Controllers
{
    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminJournalsController : Controller
    {
        public const string RelatedTracksSelectElementsIdentifire = "select_related_tracks";
        public const string RelatedVideoselectElementsIdentifire = "select_related_videos";
        public const string RelatedStylesSelectElementsIdentifire = "select_related_styles";
        public const string RelatedArtistsSelectElementsIdentifire = "select_related_artists";

        private readonly IWebHostEnvironment _env;

        public AdminJournalsController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public IActionResult Index()
        {
            return View(new AdminJournalViewModel());
        }

        public IActionResult NewAddSection()
        {
            AdminJournalViewModel adminJournalViewModel = new AdminJournalViewModel();

            using (Data data = new Data())
            {
                adminJournalViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminJournalViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminJournalViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id,  TrackTitle = trak.TrackTitle }).ToList();
                adminJournalViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminJournalViewModel.Journal = new Journal();
                adminJournalViewModel.AddSectionVisible = true;
            }

            return View("index", adminJournalViewModel);
        }



        [HttpPost]
        public ActionResult Save(AdminJournalViewModel adminJournalViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            //do data validation



            using (var data = new Data())
            {
                var journal = GenerateJournalFromAdminJournalViewModel(data, adminJournalViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);
                data.Journals.Add(journal);
                data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));
        }

        private Journal GenerateJournalFromAdminJournalViewModel(Data data, AdminJournalViewModel adminJournalViewModel, IFormFile desktop_pic_upload, IFormFile mobile_pic_upload, State state)
        {
            try
            {
                Journal journal = new Journal();

                //control bounded data
                journal.Headline = adminJournalViewModel.Journal.Headline;
                journal.MetaTitle = adminJournalViewModel.Journal.MetaTitle;
                journal.MetaDescription = adminJournalViewModel.Journal.MetaDescription;
                journal.BodyText = adminJournalViewModel.Journal.BodyText;
                journal.PublishDate = adminJournalViewModel.Journal.PublishDate;
                journal.StartPageMaterial = adminJournalViewModel.Journal.StartPageMaterial;
                journal.State = state;


                //2 images
                if (desktop_pic_upload != null && desktop_pic_upload.Length > 0)
                {
                    journal.DesktopImageUrl = saveIFromFile(desktop_pic_upload);
                }
                if (mobile_pic_upload != null && mobile_pic_upload.Length > 0)
                {
                    journal.MobileImageUrl = saveIFromFile(mobile_pic_upload);
                }




                //request form data
                var relatedTracks = Request.Form[RelatedTracksSelectElementsIdentifire];
                foreach (var relatedTrackIdString in relatedTracks)
                {
                    int trackId = int.Parse(relatedTrackIdString);
                    var relatedTrack = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                    journal.RelatedTracks.Add(relatedTrack);

                }

                var relatedVideos = Request.Form[RelatedVideoselectElementsIdentifire];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    journal.RelatedVideos.Add(relatedVideo);

                }

                var relatedStyles = Request.Form[RelatedStylesSelectElementsIdentifire];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    journal.RelatedStyle.Add(relatedStyle);

                }

                var relatedArtists = Request.Form[RelatedArtistsSelectElementsIdentifire];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.Where(a => a.Id == artistId).FirstOrDefault();
                    journal.RelatedArtists.Add(relatedArtist);

                }

                return journal;
            }
            catch (Exception ex)
            {
                return null;
            }
        }


        [HttpPost]
        public ActionResult EditSave(int journal_id, AdminJournalViewModel adminJournalViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            //do data validation



            using (var data = new Data())
            {
                var journal = GenerateJournalFromAdminJournalViewModel(data, adminJournalViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);

                var existingJournal = data.Journals.Where(j => j.Id == journal_id)
                    .Include(t => t.RelatedTracks)
                    .Include(t => t.RelatedStyle)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedVideos)
                    .Single();


                if (string.IsNullOrEmpty(journal.DesktopImageUrl))
                {
                    journal.DesktopImageUrl = existingJournal.DesktopImageUrl;
                }
                if (string.IsNullOrEmpty(journal.MobileImageUrl))
                {
                    journal.MobileImageUrl = existingJournal.MobileImageUrl;
                }


                //all the nesated objects that has to be removed
                existingJournal.RelatedTracks.Clear();
                existingJournal.RelatedStyle.Clear();
                existingJournal.RelatedArtists.Clear();
                existingJournal.RelatedVideos.Clear();

                data.SaveChanges();

                existingJournal.BodyText = journal.BodyText;
                existingJournal.Description = journal.Description;
                existingJournal.DesktopImageUrl = journal.DesktopImageUrl;
                existingJournal.Headline = journal.Headline;
                existingJournal.MetaDescription = journal.MetaDescription;
                existingJournal.MetaTitle = journal.MetaTitle;
                existingJournal.MobileImageUrl = journal.MobileImageUrl;
                existingJournal.PublishDate = journal.PublishDate;
                existingJournal.RelatedArtists = journal.RelatedArtists;
                existingJournal.RelatedStyle = journal.RelatedStyle;
                existingJournal.RelatedTracks = journal.RelatedTracks;
                existingJournal.RelatedVideos = journal.RelatedVideos;
                existingJournal.StartPageMaterial = journal.StartPageMaterial;
                existingJournal.State = State.Saved;

               data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));
        }

      




        [HttpPost]
        public ActionResult Publish(AdminJournalViewModel adminJournalViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            //do data validation



            using (Data data = new Data())
            {
                var Journal = GenerateJournalFromAdminJournalViewModel(data, adminJournalViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);

                if (Journal.PublishDate == DateTime.MinValue) // means the publish day is not set
                {
                    Journal.PublishDate = DateTime.Now;
                }

                if (Journal.PublishDate>= DateTime.Now.Date)
                {
                    Journal.State = State.ToBePublished;
                }
                else
                {
                    Journal.State = State.Published;
                }

                data.Journals.Add(Journal);
                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }


        [HttpPost]
        public ActionResult EditPublish(int journal_id, AdminJournalViewModel adminJournalViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            //do data validation



            using (var data = new Data())
            {
                var journal = GenerateJournalFromAdminJournalViewModel(data, adminJournalViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);

                var existingJournal = data.Journals.Where(j => j.Id == journal_id)
                    .Include(t => t.RelatedTracks)
                    .Include(t => t.RelatedStyle)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedVideos)
                    .Single();


                if (string.IsNullOrEmpty(journal.DesktopImageUrl))
                {
                    journal.DesktopImageUrl = existingJournal.DesktopImageUrl;
                }
                if (string.IsNullOrEmpty(journal.MobileImageUrl))
                {
                    journal.MobileImageUrl = existingJournal.MobileImageUrl;
                }


                //all the nesated objects that has to be removed
                existingJournal.RelatedTracks.Clear();
                existingJournal.RelatedStyle.Clear();
                existingJournal.RelatedArtists.Clear();
                existingJournal.RelatedVideos.Clear();

                data.SaveChanges();

                existingJournal.BodyText = journal.BodyText;
                existingJournal.Description = journal.Description;
                existingJournal.DesktopImageUrl = journal.DesktopImageUrl;
                existingJournal.Headline = journal.Headline;
                existingJournal.MetaDescription = journal.MetaDescription;
                existingJournal.MetaTitle = journal.MetaTitle;
                existingJournal.MobileImageUrl = journal.MobileImageUrl;
                existingJournal.PublishDate = journal.PublishDate;
                existingJournal.RelatedArtists = journal.RelatedArtists;
                existingJournal.RelatedStyle = journal.RelatedStyle;
                existingJournal.RelatedTracks = journal.RelatedTracks;
                existingJournal.RelatedVideos = journal.RelatedVideos;
                existingJournal.StartPageMaterial = journal.StartPageMaterial;
                existingJournal.State = State.Published;

                data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));
        }


        public ActionResult ViewForUpdate(AdminJournalViewModel adminJournalViewModel, int id)
        {
            using (Data data = new Data())
            {


                adminJournalViewModel.Journal = data.Journals.Where(j => j.Id == id)
                    .Include(x => x.RelatedArtists)
                    .Include(x => x.RelatedStyle)
                    .Include(x => x.RelatedVideos)
                    .Include(x => x.RelatedTracks)
                    .First();
                adminJournalViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminJournalViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminJournalViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminJournalViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminJournalViewModel.ViewForUpdate = true;
                adminJournalViewModel.AddSectionVisible = true;

                return View("index", adminJournalViewModel);
            }


        }


        public IActionResult DeleteJournal(int journal_id)
        {
            using (Data data = new Data())
            {
                var journalToBeRemoved = data.Journals.Where(a => a.Id == journal_id).Single();
                data.Journals.Remove(journalToBeRemoved);
                data.SaveChanges();
            }

            return View("Index", new AdminJournalViewModel());
        }















       



        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }

    }
}
