﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration3 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.Styles", "Artist_Id", "dbo.Artists");
            DropIndex("dbo.Styles", new[] { "Artist_Id" });
            CreateTable(
                "dbo.StyleArtists",
                c => new
                    {
                        Style_Id = c.Int(nullable: false),
                        Artist_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Style_Id, t.Artist_Id })
                .ForeignKey("dbo.Styles", t => t.Style_Id, cascadeDelete: true)
                .ForeignKey("dbo.Artists", t => t.Artist_Id, cascadeDelete: true)
                .Index(t => t.Style_Id)
                .Index(t => t.Artist_Id);
            
            DropColumn("dbo.Styles", "Artist_Id");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Styles", "Artist_Id", c => c.Int());
            DropForeign<PERSON>ey("dbo.StyleArtists", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.StyleArtists", "Style_Id", "dbo.Styles");
            DropIndex("dbo.StyleArtists", new[] { "Artist_Id" });
            DropIndex("dbo.StyleArtists", new[] { "Style_Id" });
            DropTable("dbo.StyleArtists");
            CreateIndex("dbo.Styles", "Artist_Id");
            AddForeignKey("dbo.Styles", "Artist_Id", "dbo.Artists", "Id");
        }
    }
}
