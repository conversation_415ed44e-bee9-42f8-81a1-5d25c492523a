﻿@model stockhom_soundrom_mvc.ViewModels.MusicViewModel



@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <link rel="stylesheet" href="~/css/Music.css" type="text/css" />


}


<div class="container1">

    <div class="main section">
        @*<img class="back_button" src="~/icons/back.png" />*@

        <h2 class="track_name">@Model.Music.Title </h2>

        <hr>

        <br>

        <p id="description" class="description">@Model.Music.Description</p>

        <div class="streaming_services">

            @if (string.IsNullOrEmpty(Model.Music.SpotifyAlbumLink) == false)
            {
                <a href="@Model.Music.SpotifyAlbumLink">
                    <img class="straming_service_icon" src="~/icons/Spotify ikon svart.png" />
                </a>
            }


            @if (string.IsNullOrEmpty(Model.Music.SoundCloudAlbumLink) == false)
            {
                <a href="@Model.Music.SoundCloudAlbumLink">
                    <img class="straming_service_icon" src="~/icons/Soundcloud ikon svart.png" />
                </a>
            }

            @if (string.IsNullOrEmpty(Model.Music.AppleAlbumLink) == false)
            {
                <a href="@Model.Music.AppleAlbumLink">
                    <img class="straming_service_icon" src="~/icons/Apple music ikon svart.png" />
                </a>
            }



            @if (Model.Music.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).Count() > 0)
            {
                <a href="@Model.Music.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).First().Link">
                    <img class="straming_service_icon" src="~/icons/Beatport music ikon svart.png" />
                </a>
            }
            @if (Model.Music.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).Count() > 0)
            {
                <a href="@Model.Music.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).First().Link">
                    <img class="straming_service_icon" src="~/icons/Bandcamp music ikon svart.png" />
                </a>
            }
            @if (Model.Music.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).Count() > 0)
            {
                <a href="@Model.Music.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).First().Link">
                    <img class="straming_service_icon" src="~/icons/Juno Download ikon svart.png" />
                </a>
            }


        </div>


    </div>


    @foreach (var track in Model.TracksOrdred)
    {

        <div class="track_title_and_artist">
            TRACK @track.TrackNoInMusicCollection : @track.TrackTitle - @track.RelatedArtists.First().ArtistName
        </div>

        <div class="section">
            <div class="sc_embedment_container">
                @Html.Raw(track.SoundCloudLink)
            </div>


            <div class="streaming_services">
                @if (string.IsNullOrEmpty(track.SpotifyLink) == false)
                {
                    <a href="@track.SpotifyLink">
                        <img class="straming_service_icon" src="~/icons/Spotify ikon svart.png" />
                    </a>
                }


                @if (string.IsNullOrEmpty(track.AppleLink) == false)
                {
                    <a href="@track.AppleLink">
                        <img class="straming_service_icon" src="~/icons/Apple music ikon svart.png" />
                    </a>
                }

                @if (string.IsNullOrEmpty(track.YoutubeMusicStreamLink) == false)
                {
                    <a href="@track.YoutubeMusicStreamLink">
                        <img class="straming_service_icon" src="~/icons/YouTube music ikon svart.png" />
                    </a>
                }



                @if (track.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).Count() > 0)
                {
                    <a href="@track.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).First().Link">
                        <img class="straming_service_icon" src="~/icons/Beatport music ikon svart.png" />
                    </a>
                }
                @if (track.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).Count() > 0)
                {
                    <a href="@track.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).First().Link">
                        <img class="straming_service_icon" src="~/icons/Bandcamp music ikon svart.png" />
                    </a>
                }
                @if (track.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).Count() > 0)
                {
                    <a href="@track.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).First().Link">
                        <img class="straming_service_icon" src="~/icons/Juno Download ikon svart.png" />
                    </a>
                }



            </div>

            <div class="video-container">
                <iframe width="560" height="315" src="https://www.youtube.com/embed/@track.YoutubeLink?controls=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>

            <button onclick="window.location.href='https://www.youtube.com/@@sthlmsndrm?sub_confirmation=1'" class="youtube_subs_button">
                <div class="youtube_subs_button_div">
                    <div style="display: inline-block;">SUBSCRIBE</div>
                    <img src="/icons/YouTube ikon svart.png" class="youtube_subs_button_img">
                    <div style="display: inline-block;">CHANNEL</div>
                </div>
            </button>
        </div>
    }


    @if (Model.RelatedStyles != null && Model.RelatedStyles.Any())
    {
        <div class="section">
            <div class="section_title">STYLES RELATED TO THIS COLLECTION / ALBUM / EP</div>

            <div class="related_styles">
                @foreach (var style in Model.RelatedStyles)
                {
                    <a href="@Url.Action("index","Style",new {id=style.Id })">
                        <img class="style_image" src=@Url.Content($"~/Uploads/{style.Picture1}") />
                    </a>
                }

            </div>
        </div>
    }

    @if (Model.RelatedJournals != null && Model.RelatedJournals.Any())
    {
        <div class="section">
            <div class="section_title">JOURNAL ENTRIES RELATED TO THIS COLLECTION / ALBUM / EP</div>


            @foreach (var journal in Model.RelatedJournals)
            {
                <div class="related_journals">
                    <a href="@Url.Action("index","Journal",new {id=journal.Id })">
                        <img class="desktopImage" src=@Url.Content($"~/Uploads/{journal.DesktopImageUrl}") />
                        <img class="mobileImage" src=@Url.Content($"~/Uploads/{journal.MobileImageUrl}") />
                    </a>
                </div>
            }
        </div>
    }





</div>