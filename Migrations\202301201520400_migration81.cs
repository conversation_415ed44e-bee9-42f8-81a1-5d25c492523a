﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration81 : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.BuyLinks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Link = c.String(),
                        Track_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Tracks", t => t.Track_Id)
                .Index(t => t.Track_Id);
            
            AddColumn("dbo.Tracks", "TrackPath", c => c.String());
            AddColumn("dbo.Tracks", "TrackTitle", c => c.String());
            AddColumn("dbo.Tracks", "CatalogNumber", c => c.String());
            AddColumn("dbo.Tracks", "ISRC", c => c.String());
            AddColumn("dbo.Tracks", "MainGenre", c => c.String());
            AddColumn("dbo.Tracks", "SubGenre", c => c.String());
            AddColumn("dbo.Tracks", "BPM", c => c.String());
            AddColumn("dbo.Tracks", "PublishDate", c => c.DateTime(nullable: false));
            AddColumn("dbo.Tracks", "ArtWorkSquare", c => c.String());
            AddColumn("dbo.Tracks", "ArtWorkHoriontal", c => c.String());
            AddColumn("dbo.Tracks", "StartCountDown", c => c.Boolean(nullable: false));
            AddColumn("dbo.Tracks", "TopPromo", c => c.Boolean(nullable: false));
            AddColumn("dbo.Tracks", "SpotifyLink", c => c.String());
            AddColumn("dbo.Tracks", "AppleLink", c => c.String());
            AddColumn("dbo.Tracks", "YoutubeMusicStreamLink", c => c.String());
            AddColumn("dbo.Tracks", "OfferFreeDownload", c => c.Boolean(nullable: false));
            AddColumn("dbo.Tracks", "YoutubeLink", c => c.String());
            AddColumn("dbo.Tracks", "MetaTitle", c => c.String());
            AddColumn("dbo.Tracks", "MetaDescription", c => c.String());
            AddColumn("dbo.Tracks", "Description", c => c.String());
            DropColumn("dbo.Tracks", "Name");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Tracks", "Name", c => c.String());
            DropForeignKey("dbo.BuyLinks", "Track_Id", "dbo.Tracks");
            DropIndex("dbo.BuyLinks", new[] { "Track_Id" });
            DropColumn("dbo.Tracks", "Description");
            DropColumn("dbo.Tracks", "MetaDescription");
            DropColumn("dbo.Tracks", "MetaTitle");
            DropColumn("dbo.Tracks", "YoutubeLink");
            DropColumn("dbo.Tracks", "OfferFreeDownload");
            DropColumn("dbo.Tracks", "YoutubeMusicStreamLink");
            DropColumn("dbo.Tracks", "AppleLink");
            DropColumn("dbo.Tracks", "SpotifyLink");
            DropColumn("dbo.Tracks", "TopPromo");
            DropColumn("dbo.Tracks", "StartCountDown");
            DropColumn("dbo.Tracks", "ArtWorkHoriontal");
            DropColumn("dbo.Tracks", "ArtWorkSquare");
            DropColumn("dbo.Tracks", "PublishDate");
            DropColumn("dbo.Tracks", "BPM");
            DropColumn("dbo.Tracks", "SubGenre");
            DropColumn("dbo.Tracks", "MainGenre");
            DropColumn("dbo.Tracks", "ISRC");
            DropColumn("dbo.Tracks", "CatalogNumber");
            DropColumn("dbo.Tracks", "TrackTitle");
            DropColumn("dbo.Tracks", "TrackPath");
            DropTable("dbo.BuyLinks");
        }
    }
}
