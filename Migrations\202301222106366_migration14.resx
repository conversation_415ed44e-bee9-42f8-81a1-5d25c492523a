﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>