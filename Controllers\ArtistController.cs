﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using stockhom_soundrom_mvc.ViewModels;
using stockhom_soundrom_mvc.Models;
using System.Data.Entity;
using Microsoft.AspNetCore.Http;
using System.IO;
using Microsoft.AspNetCore.Hosting;

namespace stockhom_soundrom_mvc.Controllers
{
    public class ArtistController : Controller
    {
        private readonly IWebHostEnvironment _env;

        public ArtistController(IWebHostEnvironment env)
        {
            _env = env;
        }


        public IActionResult Index(int id)
        {
            ArtistViewModel artistViewModel = new ArtistViewModel();

            using (Data data = new Data())
            {

                artistViewModel.Artist = data.Artists
                    .Where(x => x.Id == id)
                    .Include(x => x.Journals)
                    .Include(x => x.Styles)
                    .Include(x => x.Videos)
                    .Include(x => x.Tracks)
                    .First();
            }

            return View(artistViewModel);
        }

        [HttpPost]
        public IActionResult Preview(AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            ArtistViewModel artistViewModel = new ArtistViewModel();

            using (Data data = new Data())
            {

                artistViewModel.Artist = GenerateArtistFromAdminArtistViewModel(data, adminArtistViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);

            }

            return View("Index", artistViewModel);
        }

        private Artist GenerateArtistFromAdminArtistViewModel(Data data, AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, State state)
        {
            try
            {
                Artist artist = new Artist();

                //control bounded data
                artist.ArtistName = adminArtistViewModel.Artist.ArtistName;
                artist.Description = adminArtistViewModel.Artist.Description;
                artist.MetaTitle = adminArtistViewModel.Artist.MetaTitle;
                artist.MetaDescription = adminArtistViewModel.Artist.MetaDescription;
                artist.PublishedDate = adminArtistViewModel.Artist.PublishedDate;
                artist.State = state;


                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    artist.VerticalImageUrl = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    artist.HorizontalImageUrl = saveIFromFile(pic_horizontal_upload);
                }




                //request form data
                var relatedTracks = Request.Form[Global.RelatedTracksDDLName];
                foreach (var relatedTrackIdString in relatedTracks)
                {
                    int trackId = int.Parse(relatedTrackIdString);
                    var relatedTrack = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                    artist.Tracks.Add(relatedTrack);

                }

                var relatedVideos = Request.Form[Global.RelatedVideosDDLName];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    artist.Videos.Add(relatedVideo);

                }

                var relatedStyles = Request.Form[Global.RelatedStylesDDLName];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    artist.Styles.Add(relatedStyle);

                }

                var relatedJournals = Request.Form[Global.RelatedJournalsDDLName];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    artist.Journals.Add(relatedJournal);

                }

                return artist;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }
    }
}
