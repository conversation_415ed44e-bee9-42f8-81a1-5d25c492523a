﻿@using System.Data.Entity;

@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}



@section Styles{

    <link rel="stylesheet" href="~/css/VideosStartPage.css" />


}

<div class="promo-area">
    <div class="big-promo">
        @using (Data data = new Data())
        {
            var video = data.VideoRankings10.First().Video;


            <img class="desktopImage" src=@Url.Content($"~/Uploads/{video.HorizontalPosterLinkUrl}")>
            <img class="mobileImage" src=@Url.Content($"~/Uploads/{video.VerticalPosterUrl}")>
            <div class="youtube-play-btn"></div>
        }
    </div>


    @using (Data data = new Data())
    {
        <div class="small-promo-area">
            @foreach (var vr30 in data.VideoRankings30.ToList())
            {
                <div class="small-promo">
                    <img src=@Url.Content($"~/Uploads/{vr30.Video.VerticalPosterUrl}")>
                    <div class="youtube-play-btn"></div>
                </div>
            }
        </div>
    }


</div>


@using (Data data = new Data())
{
    @foreach (var music in data.Musics.Include(m => m.Tracks.Select(t => t.RelatedVideos)))
    {

        bool firstOneDone = false;

        <div class="music_collection_name">@music.Title</div>
        <div class="album-videos">

            @foreach (var track in music.Tracks)
            {
                foreach (var video in track.RelatedVideos)
                {
                    <div class="poster">
                        @if (firstOneDone == false)
                        {
                            <img src=@Url.Content($"~/Uploads/{video.HorizontalPosterLinkUrl}")>
                        }
                        else
                        {
                            <img src=@Url.Content($"~/Uploads/{video.VerticalPosterUrl}")>
                        }
                        <div class="youtube-play-btn"></div>
                    </div>
                }
            }


        </div>
    }
}

@section Scripts{



}