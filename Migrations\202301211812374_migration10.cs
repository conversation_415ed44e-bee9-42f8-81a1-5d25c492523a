﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration10 : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Musics",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        MusicCollectionType = c.Int(nullable: false),
                        PublishDate = c.DateTime(nullable: false),
                        StartCountDown = c<PERSON>(nullable: false),
                        TopPromo = c<PERSON>(nullable: false),
                        ArtWorkSquare = c.String(),
                        ArtWorkHoriontal = c.String(),
                        YoutubeMusicAlbumLink = c.String(),
                        YoutubeAlbumLink = c.String(),
                        Description = c.String(),
                        MetaTitle = c.String(),
                        MetaDescription = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            AddColumn("dbo.Tracks", "Music_Id", c => c.Int());
            AddColumn("dbo.BuyLinks", "Music_Id", c => c.Int());
            CreateIndex("dbo.Tracks", "Music_Id");
            CreateIndex("dbo.BuyLinks", "Music_Id");
            AddForeignKey("dbo.BuyLinks", "Music_Id", "dbo.Musics", "Id");
            AddForeignKey("dbo.Tracks", "Music_Id", "dbo.Musics", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Tracks", "Music_Id", "dbo.Musics");
            DropForeignKey("dbo.BuyLinks", "Music_Id", "dbo.Musics");
            DropIndex("dbo.BuyLinks", new[] { "Music_Id" });
            DropIndex("dbo.Tracks", new[] { "Music_Id" });
            DropColumn("dbo.BuyLinks", "Music_Id");
            DropColumn("dbo.Tracks", "Music_Id");
            DropTable("dbo.Musics");
        }
    }
}
