﻿@model stockhom_soundrom_mvc.ViewModels.JournalViewModel
@using System.Data.Entity;


@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <style>

        .container1 {
            text-align: center;
            max-width: 800px;
            padding: 5%;
            padding-top: 1%;
        }

        .main {
            text-align: center;
            width: 100%;
            margin: auto;
            position: relative;
        }

            .main .back_button {
                width: 20px;
                height: 20px;
                position: absolute;
                top: 0;
                left: 0;
            }

                .main .back_button:hover {
                    background-color: lightgray;
                }


        .related_artists1 {
            height: 32vw;
            text-align: left;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
        }

            .related_artists1 .artist_image {
                display: inline;
                height: 100%;
            }


        .track_title_and_artist {
            display: block;
            text-align: left;
            font-weight: 600;
            /*margin-left: 2.5vw;*/
        }

        .streaming_services {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 4vw;
            gap: 4vw;
            justify-content: flex-start;
        }

            .streaming_services .straming_service_icon {
                width: 35px;
                height: 35px;
            }


        .main .track_name {
            display: inline-block;
            min-width: 100px;
            color: black;
            margin: 0;
        }


        .description {
            margin-top: 2vw;
            margin-bottom: 2vw;
            text-align: left;
            width: 100%;
        }

        .DescriptionTruncated {
            width: 100%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: black;
            font-size: 16px;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
            text-align: left;
        }

        .DescriptionExtended {
            width: 100%;
            display: none;
            font-size: 16px;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
            text-align: left;
        }


        .button1 {
            width: 100%;
            display: block;
            margin: auto;
            background-color: white;
            text-size-adjust: auto;
        }

            .button1:hover {
                background-color: lightgray;
            }

        .related_styles {
            height: 22vw;
            text-align: left;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
        }

            .related_styles .style_image {
                display: inline;
                height: 100%;
            }




        .video-container {
            position: relative;
            padding-bottom: 56.25%;
            /* 16:9 */
            height: 0;
            /*     margin-bottom: 5vw;*/
        }

            .video-container iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }

        .sc_embedment_container {
            width: 100%;
            margin-bottom: 4vw;
        }

        @@media screen and (max-width: 600px) {

            .desktopImage {
                display: none;
            }


            .mobileImage {
                display: block;
                width: 100%;
            }

            h1 {
                font-size: 8vw;
            }
        }


        @@media screen and (min-width: 601px) {

            .desktopImage {
                display: block;
                width: 100%;
            }


            .mobileImage {
                display: none;
            }

            h1 {
                font-size: 4vw;
            }
        }

        .section_title {
            text-align: left;
            margin-top: 2vw;
            margin-bottom: 2vw;
            font-weight: 900;
        }

        .related_journals {
        }

            .related_journals img {
                width: 100%;
            }



        .section {
            margin-bottom: 10vw;
        }

        ::-webkit-scrollbar {
            height: 4px;
            /* height of horizontal scrollbar ← You're missing this */
            width: 4px;
            /* width of vertical scrollbar */
            border: 1px solid #d5d5d5;
        }

        .sc_embedment_container {
            width: 100%;
            margin-bottom: 1vw;
        }

        .track_and_vid {
            margin-bottom: 4vw;
        }


        @@media screen and (max-width: 584px) {

            .youtube_subs_button {
                width: 100%;
                height: 15vw;
                font-size: 5vw;
                font-weight: 600;
                color: white;
                background-color: black;
                border-radius: 10px;
            }


            .youtube_subs_button_div {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .youtube_subs_button_img {
                width: 14vw;
                height: 14vw;
            }
        }

        /*youtube subscribe button all other elements inside it */
        @@media screen and (min-width: 585px) {

            .youtube_subs_button {
                width: 100%;
                height: 5vw;
                font-size: 2vw;
                font-weight: 600;
                color: white;
                background-color: black;
                border-radius: 10px;
            }


            .youtube_subs_button_div {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .youtube_subs_button_img {
                width: 4vw;
                height: 4vw;
            }
        }
    </style>



}

<div class="container1">

    <div class="main section">
        @*<img class="back_button" src="~/icons/back.png" />
            <h2 class="track_name">[title] </h2>
            <hr>
            <br>*@
        <img class="desktopImage" src="@Url.Content($"~/Uploads/{Model.Journal.DesktopImageUrl}")" />
        <img class="mobileImage" src="@Url.Content($"~/Uploads/{Model.Journal.MobileImageUrl}")" />

        <h1>@Model.Journal.Headline</h1>
        <div class="DescriptionSection">
            <p style="text-align:left">@Model.Journal.BodyText</p>
        </div>

    </div>

    <div class="section">
        @using (Data data = new Data())
        {

            Journal journal = new Journal();

            try
            {
                journal = data.Journals.Where(j => j.Id == Model.Journal.Id).Include(j => j.RelatedTracks.Select(t => t.RelatedVideos)).Single();
            }
            catch (Exception ex)
            {
                journal = Model.Journal;
            }

            if (journal.RelatedTracks.Count > 0)
            {
                <label></label>
                <div class="section_title">MUSIC AND VIDEO RELATED TO THIS ENTRY</div>

                var trackIds = journal.RelatedTracks.Select(t => t.Id).ToList();

                if (data.Musics.Any(m => m.Tracks.Any(t => trackIds.Contains(t.Id)))) // music
                {


                    int journal_related_tracks_id = journal.RelatedTracks.First().Id;

                    var music = data.Musics.Where(m => m.Tracks.Any(t => t.Id == journal_related_tracks_id)).Include(m => m.Tracks).First();

                    foreach (var track in music.Tracks)
                    {

                        <div class="track_and_vid">
                            @{var tracksArtist = data.Artists.Where(a => a.Tracks.Any(t => t.Id == track.Id)).First();}

                            <div class="track_title_and_artist">@track.TrackTitle - @tracksArtist.ArtistName</div>

                            <div class="sc_embedment_container">
                                @Html.Raw(track.SoundCloudLink)
                            </div>

                            @{var video = data.Videos.Where(v => v.RelatedTracks.Any(t => t.Id == track.Id)).FirstOrDefault();}

                            @if (video != null)
                            {
                                <div class="video-container">
                                    <iframe width="560" height="315" src="https://www.youtube.com/embed/@video.YoutubeLink?controls=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                </div>

                                <button onclick="window.location.href='https://www.youtube.com/@@sthlmsndrm?sub_confirmation=1'" class="youtube_subs_button">
                                    <div class="youtube_subs_button_div">
                                        <div style="display: inline-block;">SUBSCRIBE</div>
                                        <img src="/icons/YouTube ikon svart.png" class="youtube_subs_button_img">
                                        <div style="display: inline-block;">CHANNEL</div>
                                    </div>
                                </button>
                            }
                        </div>
                    }


                }
                else //track
                {
                    var track = journal.RelatedTracks.First();



                    var tracksArtist = data.Artists.Where(a => a.Tracks.Any(t => t.Id == track.Id)).FirstOrDefault();

                    if (tracksArtist != null)
                    {
                        <div class="track_title_and_artist">@track.TrackTitle - @tracksArtist.ArtistName</div>

                    }
                    else
                    {
                        <div class="track_title_and_artist">@track.TrackTitle</div>
                    }


                    <div class="sc_embedment_container">
                        @Html.Raw(track.SoundCloudLink)
                    </div>

                    var video = data.Videos.Where(v => v.RelatedTracks.Any(t => t.Id == track.Id)).FirstOrDefault();

                    if (video != null)
                    {
                        <div class="video-container">
                            <iframe width="560" height="315" src="https://www.youtube.com/embed/@video.YoutubeLink?controls=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                        </div>
                        <button onclick="window.location.href='https://www.youtube.com/@@sthlmsndrm?sub_confirmation=1'" class="youtube_subs_button">
                            <div class="youtube_subs_button_div">
                                <div style="display: inline-block;">SUBSCRIBE</div>
                                <img src="/icons/YouTube ikon svart.png" class="youtube_subs_button_img">
                                <div style="display: inline-block;">CHANNEL</div>
                            </div>
                        </button>
                    }

                }
            }
        }
    </div>

    <div class="section">
        <div class="section_title">STYLE RELATED TO THIS ENTRY</div>



        @using (Data data = new Data())
        {

            var style = data.Styles.Where(s => s.Journals.Any(j => j.Id == Model.Journal.Id)).FirstOrDefault();



            if (style != null)
            {
                <div class="related_styles">
                    <a href="@style.ExternalLink">
                        <img class="style_image" src=@Url.Content($"~/Uploads/{style.Picture1}") />
                    </a>
                </div>
            }
        }



    </div>
</div>