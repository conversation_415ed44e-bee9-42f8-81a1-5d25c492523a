﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Controllers
{
    public class AdminLoginController : Controller
    {
        public IActionResult Index()
        {
            return View(new AdminLoginViewModel());
        }

        [HttpPost]
        public IActionResult Login(AdminLoginViewModel adminLoginViewModel)
        {
            using (Data data = new Data())
            {
                var admin = data.Admins.Where(a => a.Email == adminLoginViewModel.Email &&
                  a.Password == adminLoginViewModel.Password).FirstOrDefault();

                if(admin != null)
                {
                    HttpContext.Session.SetInt32("UserId", admin.Id);

                    return RedirectToAction("Index", "AdminMusic");
                    
                }
                else
                {
                    return View("Index", new AdminLoginViewModel() { LoginFailed = true });
                }


            }

        }
    }
}
