﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Controllers
{
    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminPresentationController : Controller
    {

        private readonly IWebHostEnvironment _env;

        public AdminPresentationController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public IActionResult Index()
        {
            using (Data data = new Data())
            {
                var presentation = data.Presentations.FirstOrDefault();

                if (presentation != null)
                {
                    return View(new AdminPresentationViewModel() { Presentation = presentation });
                }
                else
                {
                    return View(new AdminPresentationViewModel());

                }
            }

        }


        public IActionResult Update(AdminPresentationViewModel adminPresentationViewModel, IFormFile music_hor_pic, IFormFile music_square_pic, IFormFile merch_hor_pic, IFormFile merch_square_pic)
        {
            using (Data data = new Data())
            {
                var Existingpresentation = data.Presentations.FirstOrDefault();


                if (Existingpresentation == null)
                {
                    Presentation newPresentataion = new Presentation();
                    newPresentataion.MerchText = adminPresentationViewModel.Presentation.MerchText;
                    newPresentataion.MusicText = adminPresentationViewModel.Presentation.MusicText;


                    if (music_hor_pic != null && music_hor_pic.Length > 0)
                    {
                        newPresentataion.HorizontalMusicImage = saveIFromFile(music_hor_pic);
                    }
                    if (music_square_pic != null && music_square_pic.Length > 0)
                    {
                        newPresentataion.SquareMusicImage = saveIFromFile(music_square_pic);
                    }
                    if (merch_hor_pic != null && merch_hor_pic.Length > 0)
                    {
                        newPresentataion.HorizontalMerchImage = saveIFromFile(merch_hor_pic);
                    }
                    if (merch_square_pic != null && merch_square_pic.Length > 0)
                    {
                        newPresentataion.SquareMerchImage = saveIFromFile(merch_square_pic);
                    }

                    data.Presentations.Add(newPresentataion);
                    data.SaveChanges();
                }
                else
                {
                    Existingpresentation.MerchText = adminPresentationViewModel.Presentation.MerchText;
                    Existingpresentation.MusicText = adminPresentationViewModel.Presentation.MusicText;

                    if (music_hor_pic != null && music_hor_pic.Length > 0)
                    {
                        Existingpresentation.HorizontalMusicImage = saveIFromFile(music_hor_pic);
                    }
                    if (music_square_pic != null && music_square_pic.Length > 0)
                    {
                        Existingpresentation.SquareMusicImage = saveIFromFile(music_square_pic);
                    }
                    if (merch_hor_pic != null && merch_hor_pic.Length > 0)
                    {
                        Existingpresentation.HorizontalMerchImage = saveIFromFile(merch_hor_pic);
                    }
                    if (merch_square_pic != null && merch_square_pic.Length > 0)
                    {
                        Existingpresentation.SquareMerchImage = saveIFromFile(merch_square_pic);
                    }

                    data.SaveChanges();

                }







                return RedirectToAction("Index");

            }
        }

        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }



    }
}
