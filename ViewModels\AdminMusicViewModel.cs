﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using stockhom_soundrom_mvc.Models;

namespace stockhom_soundrom_mvc.ViewModels
{
    public class AdminMusicViewModel
    {
        public Track Track { get; set; } = new Track();
        public bool ViewForUpdate { get; set; }
        public bool AddSectionVisible { get; set; }
        public List<Journal> AllJournals { get; internal set; } = new List<Journal>();
        public List<Style> AllStyles { get; internal set; } = new List<Style>();
        public List<Track> AllTracks { get; internal set; } = new List<Track>();
        public List<Video> AllVideos { get; internal set; } = new List<Video>();
        public List<Artist> AllArtists { get; set; } = new List<Artist>();
       
    }
}
