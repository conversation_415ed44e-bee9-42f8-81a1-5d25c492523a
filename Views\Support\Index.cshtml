﻿
@section Styles{

    <style>


        .container1 {
            text-align: center;
            max-width: 800px;
            padding: 5%;
            padding-top: 1%;
        }


        @@media screen and (min-width: 585px) {
            #headline {
                font-size: 2vw
            }

            #bodytext {
                text-align: left;
                font-size: 1.5vw;
            }
        }

        @@media screen and (max-width: 584px) {
            #headline {
                font-size: 4vw
            }

            #bodytext {
                text-align: left;
                font-size: 2.5vw;
            }
        }
    </style>
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}

@{


    var headline = "";
    var bodytext = "";

    using (Data data = new Data())
    {

        var support = data.Supports.FirstOrDefault();

        if (support != null)
        {
            headline = support.Headline;
            bodytext = support.BodyText;
        }
    }

}


<div class="container1">

    <div id="headline" >@headline</div>
    <hr />

    <div id="bodytext" >@bodytext</div>



</div>


@section Scripts{

    <script>
        const parent = document.querySelector('.parent');
        const movingImage = document.querySelector('.moving-image');

        function moveImage() {
            const parentWidth = parent.clientWidth;
            const parentHeight = parent.clientHeight;
            const imageWidth = movingImage.clientWidth;
            const imageHeight = movingImage.clientHeight;

            const randomX = Math.floor(Math.random() * (parentWidth - imageWidth));
            const randomY = Math.floor(Math.random() * (parentHeight - imageHeight));

            movingImage.style.top = randomY + 'px';
            movingImage.style.left = randomX + 'px';
        }

        setInterval(moveImage, 2000); // Change the value to adjust the speed of movement (in milliseconds)
    </script>


    <script>

        function showEmail() {


            $.ajax({
                url: "/Contact/GetEmail",
                type: "GET",
                async: false,
                success: function (response) {

                    document.getElementById("display_email").innerHTML = response;
                    document.getElementById("display_email").style.display = "block";
                    document.getElementById("check").style.display = "none";
                },
                error: function (xhr) {
                    // handle error
                }
            });



        }


    </script>

}




