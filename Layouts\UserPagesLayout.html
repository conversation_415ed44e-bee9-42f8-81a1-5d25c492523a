﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style>
        body {
            /* position: relative; */
        }

        header {
            width: 100%;
            height: 5vw;
            position: fixed;
            top: 0px;
            right: 0px;
            left: 0px;
            z-index: 1;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            color: #FFF;
            /* padding: 1em; */
        }

            header img {
                height: 30px;
                position: absolute;
                top: 20px;
                left: 50%;
                transform: translate(-50%, -50%);
            }

        .menu {
            display: flex;
            flex-direction: row;
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

            .menu > li {
                margin: 0 1rem;
                overflow: hidden;
            }

        .menu-button-container {
            display: none;
            height: 100%;
            width: 30px;
            cursor: pointer;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: absolute;
            left: 5px;
        }

        #menu-toggle {
            display: none;
        }

        .menu-button,
        .menu-button::before,
        .menu-button::after {
            display: block;
            background-color: black;
            position: absolute;
            height: 4px;
            width: 30px;
            transition: transform 400ms cubic-bezier(0.23, 1, 0.32, 1);
            border-radius: 2px;
        }

            .menu-button::before {
                content: '';
                margin-top: -8px;
            }

            .menu-button::after {
                content: '';
                margin-top: 8px;
            }

        #menu-toggle:checked + .menu-button-container .menu-button::before {
            margin-top: 0px;
            transform: rotate(405deg);
        }

        #menu-toggle:checked + .menu-button-container .menu-button {
            background: rgba(255, 255, 255, 0);
        }

            #menu-toggle:checked + .menu-button-container .menu-button::after {
                margin-top: 0px;
                transform: rotate(-405deg);
            }

        @media (max-width: 2000px) {
            .menu-button-container {
                display: flex;
            }

            .menu {
                position: absolute;
                top: 0;
                margin-top: 5vw;
                left: 0;
                flex-direction: column;
                width: 100%;
                justify-content: center;
                align-items: center;
            }

            #menu-toggle ~ .menu li {
                height: 0;
                margin: 0;
                padding: 0;
                border: 0;
                transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            }

            #menu-toggle:checked ~ .menu li {
                height: 1.5em;
                padding: 0.5em;
                transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            }

            .menu > li {
                display: flex;
                justify-content: center;
                margin: 0;
                padding: 0.5em 0;
                width: 100%;
                color: white;
                background-color: #222;
            }

                .menu > li:not(:last-child) {
                    border-bottom: 1px solid #444;
                }
        }



        .pacman_left {
            display: table-cell;
            position: relative;
            /* top: 5vw; */
            width: 20vw;
            height: 100vh;
            /* background-image: linear-gradient(to bottom right, #ff7f00, #ff2a68); */
            position: fixed;
            top: 5vw;
            left: 0px;
        }

        .content {
            display: table-cell;
            position: relative;
            /* top: 5vw; */
            width: 60vw;
            height: 500vh;
            position: relative;
            top: 5vw;
            left: 19vw;
            z-index: 0;
        }

        .pacman_right {
            display: table-cell;
            position: relative;
            /* top: 5vw; */
            width: 20vw;
            height: 100vh;
            /* background-image: linear-gradient(to bottom right, #4c00ff, #75ff2a); */
            position: fixed;
            top: 5vw;
            right: 0px;
        }



        #pacman_table_left td {
            background-size: cover;
            width: 2vw;
            height: 2vw;
            border: none;
        }

        #pacman_table_right td {
            background-size: cover;
            width: 2vw;
            height: 2vw;
            border: none;
        }




        @media screen and (min-width: 1333px) {

            .content {
                display: table-cell;
                position: relative;
                /* top: 5vw; */

                height: 500vh;
                position: relative;
                top: 5vw;
                left: calc(100vw/2 - 400px);
                z-index: 0;
                width: 800px;
            }

            .pacman_left {
                display: table-cell;
                position: relative;
                /* top: 5vw; */
                height: 100vh;
                position: fixed;
                top: 5vw;
                left: 0px;
                width: calc(100vw/2 - 400px);
            }

            .pacman_right {
                display: table-cell;
                position: relative;
                /* top: 5vw; */
                height: 100vh;
                position: fixed;
                top: 5vw;
                right: 0px;
                width: calc(100vw/2 - 400px);
            }


            #pacman_table_left td {
                background-size: cover;
                width: 1.5vw;
                height: 1.5vw;
                border: none;
                position: relative;
                overflow: visible;
            }

            #pacman_table_right td {
                background-size: cover;
                width: 1.5vw;
                height: 1.5vw;
                border: none;
                position: relative;
                overflow: visible;
            }
        }


        @media screen and (max-width: 584px) {

            .pacman_left {
                display: none;
            }

            .pacman_right {
                display: none;
            }

            .content {
                display: table-cell;
                position: relative;
                /* top: 5vw; */
                width: 100vw;
                height: 500vh;
                position: relative;
                top: 5vw;
                left: 0;
                z-index: 0;
            }
        }

        .open_mouth {
            background-image: url('icons/opened.png');
            background-size: cover;
            width: 5vw;
            height: 7vw;
            position: absolute;
            right: -1vw;
            bottom: -1vw;
            z-index: 1;
        }

        .closed_mouth {
            background-image: url('icons/closed.png');
            background-size: cover;
            width: 5vw;
            height: 7vw;
            position: absolute;
            right: -1vw;
            bottom: -1vw;
            z-index: 1;
        }
    </style>

</head>

<body>
    <header>
        <img src="logo-one-line.png">
        <div style="text-align: center;">

        </div>
        <input id="menu-toggle" type="checkbox" />
        <label class='menu-button-container' for="menu-toggle">
            <div class='menu-button'></div>
        </label>
        <ul class="menu">
            <li>YOUTUBE</li>
            <li>SPOTIFY</li>
            <li>SOUNDCLOUD</li>
            <li>TICTOK</li>
            <li>INSTAGRAM</li>
            <li>FACEBOOK</li>
            <li>TWITTER</li>
            <li>THE JOURNAL</li>
            <li>SUPPORT</li>
            <li>CONTACT</li>

        </ul>
    </header>
    <div id="left" class="pacman_left"> </div>
    <div class="content"> </div>
    <div id="right" class="pacman_right"></div>
</body>


<script>
    //create table function with 2 parameters
    function createTables(row_count, col_count) {

        // Create the table
        let table_left = document.createElement("table");
        table_left.style.width = "100%";
        table_left.id = "pacman_table_left";

        let table_right = document.createElement("table");
        table_right.style.width = "100%";
        table_right.id = "pacman_table_right";


        // Create the number of rows specified
        for (let i = 0; i < row_count; i++) {
            let row_left = table_left.insertRow();
            let row_right = table_right.insertRow();

            // Create the number of columns specified
            for (let j = 0; j < col_count / 2; j++) {
                let cell = row_left.insertCell();
                cell.style.position = "relative";
                cell.style.backgroundImage = "url(icons/vinyl.png)";
                // cell.style.backgroundSize = "cover";
                // cell.style.width = "1.5vw";
                // cell.style.height = "1.5vw";
                // cell.style.border = "none";

                cell.id = i + "_" + j;
                cell.style.zIndex = '0';
            }

            for (let j = col_count / 2; j < col_count; j++) {
                let cell = row_right.insertCell();
                cell.style.backgroundImage = "url(icons/vinyl.png)";
                cell.style.position = "relative";
                // cell.style.backgroundSize = "cover";
                // cell.style.border = "none";
                // cell.style.width = "1.5vw";
                // cell.style.height = "1.5vw";
                cell.id = i + "_" + j;
                cell.style.zIndex = '0';
            }

        }

        // Add the table to the specified div element
        document.getElementById("left").appendChild(table_left);
        document.getElementById("right").appendChild(table_right);

    }


    var cols = 20;
    var rows = 40;


    createTables(rows, cols);

    var lastDirection = 0;
    var currentX = 0;
    var currentY = 0;
    var failCount = 0;



    window.onload = async function () {


        //set the pacman to a random cell
        currentX = Math.floor(Math.random() * cols);
        currentY = Math.floor(Math.random() * rows);
        //set the pacman to the current cell
        // SetOverlay(currentY + "_" + currentX, 'icons/closed.png');


        while (true) {


            //remove the vinyl from the current cell
            document.getElementById(currentY + "_" + currentX).style.backgroundImage = "none";
            // removeOverlay(currentY + "_" + currentX);
            document.getElementById(currentY + "_" + currentX).innerHTML = '';
            //CurrentX and CurrntY values to randomly be in up , down , previous or next box
            pickNextPos(cols, rows);




            //if current cell is empty
            if (document.getElementById(currentY + "_" + currentX).style.backgroundImage == "none") {
                //add the pacman to the next cell
                // SetOverlay(currentY + "_" + currentX, 'icons/closed.png');
                const newDiv = document.createElement("div");
                newDiv.classList.add("closed_mouth");
                document.getElementById(currentY + "_" + currentX).appendChild(newDiv);


                await new Promise(r => setTimeout(r, 1000));

            }
            else // there is a vinyl to be eaten in the cell
            {
                //jump to the cell and open the mouth
                // SetOverlay(currentY + "_" + currentX, 'icons/opened.png');
                const newDiv = document.createElement("div");
                newDiv.classList.add("open_mouth");
                document.getElementById(currentY + "_" + currentX).appendChild(newDiv);

                //stay with opened mouth for few millisecounds
                await new Promise(r => setTimeout(r, 400));
                // removeOverlay(currentY + "_" + currentX);
                document.getElementById(currentY + "_" + currentX).innerHTML = '';

                //close mouth
                // SetOverlay(currentY + "_" + currentX, 'icons/closed.png');
                const newDiv1 = document.createElement("div");
                newDiv1.classList.add("closed_mouth");
                document.getElementById(currentY + "_" + currentX).appendChild(newDiv1);
                document.getElementById(currentY + "_" + currentX).style.backgroundImage = '';

                await new Promise(r => setTimeout(r, 1000));

            }





        }
    }

    function outOfBounds(cols, rows) {

        if (currentX < 0 || currentX >= cols || currentY < 0 || currentY >= rows) {
            return true;
        }
        else {
            return false;
        }
    }

    function alreadyVisited() {

        if (document.getElementById(currentY + "_" + currentX).style.backgroundImage == "url(icons/vinyl.png)") {
            return false;
        }
        else {
            return true;
        }
    }


    function pickNextPos(cols, rows) {

        // Pick a random direction
        let direction = Math.floor(Math.random() * 4);

        //0 = up, 1 = right, 2 = down, 3 = left

        if (direction == 0) {

            currentY--;
            if (outOfBounds(cols, rows)) {
                currentY++;
                pickNextPos();
            }
        }
        else if (direction == 1) {
            currentX++;
            if (outOfBounds(cols, rows)) {
                currentX--;
                pickNextPos();
            }
        }
        else if (direction == 2) {
            currentY++;
            if (outOfBounds(cols, rows)) {
                currentY--;
                pickNextPos();
            }
        }
        else if (direction == 3) {
            currentX--;
            if (outOfBounds(cols, rows)) {
                currentX++;
                pickNextPos();
            }
        }
    }

    function SetOverlay(td_id, image_path) {
        // Get the td element with the specified ID
        const td = document.getElementById(td_id);

        // Create a new img element to hold the overlay image
        const overlay = document.createElement('img');

        overlay.id = td_id + "_overlay";

        // Set the src attribute of the img element to the overlay image
        overlay.src = image_path;

        // Set the position of the overlay to "absolute"
        overlay.style.position = 'fixed';

        // Set the bottom and left position of the overlay to match the td element
        // const tdRect = td.getBoundingClientRect();
        // overlay.style.bottom = tdRect.bottom + 'px';
        // overlay.style.left = tdRect.left + 'px';

        var left = td.offsetLeft;
        var bottom = td.offsetTop + td.offsetHeight;
        overlay.style.left = left + 'px';
        overlay.style.bottom = bottom + 'px';
        console.log("overlay_left " + left);
        console.log("overlay_bottom " + bottom);
        console.log("td x " + td.x);
        console.log("td  y" + td.y);



        // Set the size of the overlay in vw units
        overlay.style.width = '5vw';
        overlay.style.height = '5vw';
        overlay.style.zIndex = '2';

        // Add the overlay to the document
        document.body.appendChild(overlay);

        // Store a reference to the overlay element in a data attribute of the td element
        td.setAttribute('data-overlay', overlay.id);
    }

    function removeOverlay(td_id) {
        // Get the td element with the specified ID
        const td = document.getElementById(td_id);

        // Get the ID of the overlay element stored in the data attribute
        const overlayId = td.getAttribute('data-overlay');

        // If an overlay element exists, remove it from the document and delete the data attribute
        if (overlayId) {
            const overlay = document.getElementById(overlayId);
            overlay.parentNode.removeChild(overlay);
            td.removeAttribute('data-overlay');
        }
    }




</script>

</html>