﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Controllers
{
    public class JournalController : Controller
    {
        public const string RelatedTracksSelectElementsIdentifire = "select_related_tracks";
        public const string RelatedVideoselectElementsIdentifire = "select_related_videos";
        public const string RelatedStylesSelectElementsIdentifire = "select_related_styles";
        public const string RelatedArtistsSelectElementsIdentifire = "select_related_artists";

        private readonly IWebHostEnvironment _env;

        public JournalController(IWebHostEnvironment env)
        {
            _env = env;
        }



        public IActionResult Index(int id=1)
        {

            JournalViewModel JournalViewModel = new JournalViewModel();

            using (Data data = new Data())
            {
                JournalViewModel.Journal = data.Journals.Where(j => j.Id == id).FirstOrDefault();

            }

            return View(JournalViewModel);
        }


        [HttpPost]
        public ActionResult Preview(int journal_id, AdminJournalViewModel adminJournalViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {



            JournalViewModel journalViewModel = new JournalViewModel();
            using (Data data = new Data())
            {
                var existingJournal = data.Journals.Where(j => j.Id == journal_id)
                    .Include(t => t.RelatedTracks)
                    .Include(t => t.RelatedStyle)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedVideos)
                    .Single();


                var jrnl = GenerateJournalFromAdminJournalViewModel(data, adminJournalViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);
                jrnl.Id = journal_id;
                if (string.IsNullOrEmpty(jrnl.DesktopImageUrl))
                {
                    jrnl.DesktopImageUrl = existingJournal.DesktopImageUrl;
                }
                if (string.IsNullOrEmpty(jrnl.MobileImageUrl))
                {
                    jrnl.MobileImageUrl = existingJournal.MobileImageUrl;
                }

                if (jrnl.PublishDate == DateTime.MinValue) // means the publish day is not set
                {
                    jrnl.PublishDate = DateTime.Now;
                }

                if (jrnl.PublishDate >= DateTime.Now)
                {
                    jrnl.State = State.ToBePublished;
                }
                else
                {
                    jrnl.State = State.Published;
                }

                journalViewModel.Journal = jrnl;
            }
            return View("Index", journalViewModel);
        }

        private Journal GenerateJournalFromAdminJournalViewModel(Data data, AdminJournalViewModel adminJournalViewModel, IFormFile desktop_pic_upload, IFormFile mobile_pic_upload, State state)
        {
            try
            {
                Journal journal = new Journal();

                //control bounded data
                journal.Headline = adminJournalViewModel.Journal.Headline;
                journal.MetaTitle = adminJournalViewModel.Journal.MetaTitle;
                journal.MetaDescription = adminJournalViewModel.Journal.MetaDescription;
                journal.BodyText = adminJournalViewModel.Journal.BodyText;
                journal.PublishDate = adminJournalViewModel.Journal.PublishDate;
                journal.StartPageMaterial = adminJournalViewModel.Journal.StartPageMaterial;
                journal.State = state;


                //2 images
                if (desktop_pic_upload != null && desktop_pic_upload.Length > 0)
                {
                    journal.DesktopImageUrl = saveIFromFile(desktop_pic_upload);
                }
                if (mobile_pic_upload != null && mobile_pic_upload.Length > 0)
                {
                    journal.MobileImageUrl = saveIFromFile(mobile_pic_upload);
                }




                //request form data
                var relatedTracks = Request.Form[RelatedTracksSelectElementsIdentifire];
                foreach (var relatedTrackIdString in relatedTracks)
                {
                    int trackId = int.Parse(relatedTrackIdString);
                    var relatedTrack = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                    journal.RelatedTracks.Add(relatedTrack);

                }

                var relatedVideos = Request.Form[RelatedVideoselectElementsIdentifire];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    journal.RelatedVideos.Add(relatedVideo);

                }

                var relatedStyles = Request.Form[RelatedStylesSelectElementsIdentifire];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    journal.RelatedStyle.Add(relatedStyle);

                }

                var relatedArtists = Request.Form[RelatedArtistsSelectElementsIdentifire];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.Where(a => a.Id == artistId).FirstOrDefault();
                    journal.RelatedArtists.Add(relatedArtist);

                }

                return journal;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }
    }
}
