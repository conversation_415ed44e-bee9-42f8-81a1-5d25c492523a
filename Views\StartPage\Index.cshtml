﻿@using System.Data.Entity;
@using System.Globalization;
@model stockhom_soundrom_mvc.ViewModels.StartPageViewModel;

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{


    <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700,900" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="/css/flipdown/flipdown.css">
    <link rel="stylesheet" type="text/css" href="/css/StartPage.css">



}


<div class="container1">



    @if (Model.IsTrackPromo)
    {
        <div id="track_promo" style="margin-bottom:6vw">
            <div id="promo_track_image">
                <img src="@Url.Content($"~/Uploads/{Model.TopPromoTrack.ArtWorkHoriontal}")" class="img1 desktopImage" />
                <img src=" @Url.Content($"~/Uploads/{Model.TopPromoTrack.ArtWorkSquare } ") " class="img1 mobileImage" />
            </div>

            <div id="promo_track_title">
                @Model.TopPromoTrack.TrackTitle.ToUpper()
            </div>

            <div id="promo_track_artists">
                @if (Model.TopPromoTrack.RelatedArtists.Count > 1)
                {
                    @Model.TopPromoTrack.RelatedArtists[0].ArtistName <span>X</span> @Model.TopPromoTrack.RelatedArtists[1].ArtistName
                }
                else
                {
                    @Model.TopPromoTrack.RelatedArtists[0].ArtistName
                }
            </div>

            @if (Model.IsCountDown == true)
            {
                <div>
                    <div id="flipdown" class="flipdown" style=" margin: auto;"></div>
                </div>




                <div id="div_enter_email_address">Enter email address to get notified when this drop happens</div>
                <div class="email_txt_and_button">
                    <input class="email_txt" id="email_input1" type="email" placeholder="Enter your email address" required>
                    <input type="button" onclick="submitButton1Clicked('email_input1');" class="email_button" value="SUBMIT">
                </div>
            }


            @if (Model.IsCountDown == false)
            {
                <div id="div_social_media_icons">
                    <div class="div_single_icon">
                        <a href="https://www.youtube.com/channel/UCKPaXTnkqH3HsDPkBLWp0ww">
                            <img src="/social_media/YouTube ikon svart.png" alt="">
                        </a>
                    </div>
                    <div class="div_single_icon">
                        <a href="https://www.tiktok.com/@@stockholmsoundrome?_t=8YIBVXYhjgz&_r=1">
                            <img src="\social_media\TikTok ikon svart.png" alt="">
                        </a>
                    </div>

                    <div class="div_single_icon">
                        <a href="https://www.instagram.com/stockholmsoundrome/">
                            <img src="\social_media\Instagram ikon svart.png" alt="">
                        </a>
                    </div>
                    <div class="div_single_icon">
                        <a href="https://open.spotify.com/user/317xbywnfrm6rprveujzttet644i?si=2c30047e5401451a">
                            <img src="\social_media\Spotify ikon svart.png" alt="">
                        </a>
                    </div>

                    <div class="div_single_icon">
                        <a href="https://soundcloud.com/stockholmsoundrome">
                            <img src="\social_media\SoundCloud ikon svart.png" alt="">
                        </a>
                    </div>

                    <div class="div_single_icon">
                        <a href="https://www.facebook.com/stockholmsoundrome">
                            <img src="\social_media\Facebook ikon svart.png" alt="">
                        </a>
                    </div>

                    <div class="div_single_icon">
                        <a href="https://twitter.com/SthlmSoundrome">
                            <img src="\social_media\Twitter ikon svart.png" alt="">
                        </a>
                    </div>

                    <div class="div_single_icon">
                        <a href="https://www.snapchat.com/add/sthlmsoundrome?share_id=n9cJpQCGWGE&locale=en-GB">
                            <img src="\social_media\Snap ikon svart.png" alt="">
                        </a>
                    </div>





                </div>
            }





        </div>


        <table class="three_heads">
            <tbody>
                <tr>
                    <td style="width: 20%;border:none"><img style="width: 77%;" src="/icons/closed.png"></td>
                    <td style="width: 60%;border: none;text-align: center;"><img style="width: 51%;" src="/icons/closed.png"></td>
                    <td style="width: 20%;border: none"><img style="width: 75%;" src="/icons/closed.png"></td>
                </tr>
            </tbody>
        </table>

    }


    <div id="merch">
        <div>
            @using (Data data = new Data())
            {
                var presentation = data.Presentations.FirstOrDefault();

                if (presentation != null && presentation.HorizontalMerchImage != "" && presentation.SquareMerchImage != "")
                {
                    <div id="music_secion">
                        <div>
                            <div style="position:relative;">
                                <img class="img1 desktopImage" src="@Url.Content($"~/Uploads/{data.Presentations.First().HorizontalMerchImage}")" />
                                <div class="desktop_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                                    <div class="overlay_text">@presentation.MerchText</div>
                                </div>
                            </div>
                            <div style="position:relative;">
                                <img class="img1 mobileImage" src=" @Url.Content($"~/Uploads/{data.Presentations.First().SquareMerchImage} ") " />
                                <div class="mobile_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                                    <div class="overlay_text">@presentation.MerchText</div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>

    <div>
        <div class="related_styles">
            @foreach (var style in Model.Styles)
            {
                <a href="@Url.Action("index", "Style", new { id = style.Id })">
                    <img class="style_image" src=@Url.Content($"~/Uploads/{style.Picture1}") />
                </a>
            }
        </div>
    </div>


    @if (Model.Styles.Count > 0)
    {
        <input type="button" class="button_type_3" value="CHECK OUR MERCH" />
    }

    <table class="three_heads">
        <tbody>
            <tr>
                <td style="width: 20%;border:none"><img style="width: 77%;" src="/icons/closed.png"></td>
                <td style="width: 60%;border: none;text-align: center;"><img style="width: 51%;" src="/icons/closed.png"></td>
                <td style="width: 20%;border: none"><img style="width: 75%;" src="/icons/closed.png"></td>
            </tr>
        </tbody>
    </table>


    @if (Model.IsCountDown == false)
    {
        using (Data data = new Data())
        {
            var presentation = data.Presentations.FirstOrDefault();

            if (presentation != null && presentation.HorizontalMusicImage != "" && presentation.SquareMusicImage != "")
            {
                <div id="music_secion">
                    <div>
                        <div style="position:relative;">
                            <img class="img1 desktopImage" src="@Url.Content($"~/Uploads/{data.Presentations.First().HorizontalMusicImage}")" />
                            <div class="desktop_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                                <div class="overlay_text">@presentation.MusicText</div>
                            </div>
                        </div>
                        <div style="position:relative;">
                            <img class="img1 mobileImage" src=" @Url.Content($"~/Uploads/{data.Presentations.First().SquareMusicImage} ") " />
                            <div class="mobile_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                                <div class="overlay_text">@presentation.MusicText</div>
                            </div>
                        </div>
                    </div>
                </div>
            }


            var tracks = data.Tracks.Where(t => t.PublishDate < DateTime.Now && t.State != State.Saved).Include(t => t.RelatedArtists).ToList();


            <div class="all_tracks_slides">
                @foreach (var track in tracks)
                {
                    <div class="track_img_and_info">
                        <a style="text-decoration:none;color:inherit" href="@Url.Action("index", "track", new { id = track.Id })">
                            <img class="track_img" src=@Url.Content($"~/Uploads/{track.ArtWorkSquare}") />
                            <div class="track_info">
                                <div class="track_title text_ellipsis">@track.TrackTitle</div>
                                <div class="track_artist_name text_ellipsis">@track.RelatedArtists.FirstOrDefault().ArtistName</div>
                            </div>
                        </a>
                    </div>
                }


            </div>

        }

        <table class="three_heads">
            <tbody>
                <tr>
                    <td style="width: 20%;border:none"><img style="width: 77%;" src="/icons/closed.png"></td>
                    <td style="width: 60%;border: none;text-align: center;"><img style="width: 51%;" src="/icons/closed.png"></td>
                    <td style="width: 20%;border: none"><img style="width: 75%;" src="/icons/closed.png"></td>
                </tr>
            </tbody>
        </table>
    }








    <div id="catch_stock_soundrom">WANT TO CATCH THE STOCKHOLM SOUNDROM?</div>
    <div id="some_ways_to_catch">Here are some ways to catch it.</div>


    @if (Model.IsCountDown == false)
    {
        <div class="email_txt_and_button">
            <input class="email_txt" id="email_input2" type="email" placeholder="Enter your email address" required>
            <input type="button" onclick="submitButton1Clicked('email_input2');" class="email_button" value="SUBMIT">
        </div>
    }


    <button onclick="window.location.href='https://www.youtube.com/@@sthlmsndrm?sub_confirmation=1'" class="youtube_subs_button">
        <div class="youtube_subs_button_div">
            <div style="display: inline-block;">SUBSCRIBE</div>
            <img src="icons/YouTube ikon svart.png" class="youtube_subs_button_img">
            <div style="display: inline-block;">CHANNEL</div>
        </div>
    </button>


    <div id="div_social_media_icons">
        <div class="div_single_icon">
            <a href="https://www.youtube.com/channel/UCKPaXTnkqH3HsDPkBLWp0ww">
                <img src="\social_media\YouTube ikon svart.png" alt="">
            </a>
        </div>
        <div class="div_single_icon">
            <a href="https://www.tiktok.com/@@stockholmsoundrome?_t=8YIBVXYhjgz&_r=1">
                <img src="\social_media\TikTok ikon svart.png" alt="">
            </a>
        </div>

        <div class="div_single_icon">
            <a href="https://www.instagram.com/stockholmsoundrome/">
                <img src="\social_media\Instagram ikon svart.png" alt="">
            </a>
        </div>
        <div class="div_single_icon">
            <a href="https://open.spotify.com/user/317xbywnfrm6rprveujzttet644i?si=2c30047e5401451a">
                <img src="\social_media\Spotify ikon svart.png" alt="">
            </a>
        </div>

        <div class="div_single_icon">
            <a href="https://soundcloud.com/stockholmsoundrome">
                <img src="\social_media\SoundCloud ikon svart.png" alt="">
            </a>
        </div>

        <div class="div_single_icon">
            <a href="https://www.facebook.com/stockholmsoundrome">
                <img src="\social_media\Facebook ikon svart.png" alt="">
            </a>
        </div>

        <div class="div_single_icon">
            <a href="https://twitter.com/SthlmSoundrome">
                <img src="\social_media\Twitter ikon svart.png" alt="">
            </a>
        </div>

        <div class="div_single_icon">
            <a href="https://www.snapchat.com/add/sthlmsoundrome?share_id=n9cJpQCGWGE&locale=en-GB">
                <img src="\social_media\Snap ikon svart.png" alt="">
            </a>
        </div>





    </div>


    <table class="three_heads">
        <tbody>
            <tr>
                <td style="width: 20%;border:none"><img style="width: 77%;" src="/icons/closed.png"></td>
                <td style="width: 60%;border: none;text-align: center;"><img style="width: 51%;" src="/icons/closed.png"></td>
                <td style="width: 20%;border: none"><img style="width: 75%;" src="/icons/closed.png"></td>
            </tr>
        </tbody>
    </table>



    @if (Model.previewParty == true)
    {
        <div class="party">
            <img class="img1 desktopImage" src="@Url.Content($"~/Uploads/{Model.party_hor_image}")" />
            <img class="img1 mobileImage" src="@Url.Content($"~/Uploads/{Model.Party_sqr_image}")" />
        </div>
        <input type="button" class="button_type_3" value="Buy Tickets" />

        <table class="three_heads">
            <tbody>
                <tr>
                    <td style="width: 20%;border:none"><img style="width: 77%;" src="/icons/closed.png"></td>
                    <td style="width: 60%;border: none;text-align: center;"><img style="width: 51%;" src="/icons/closed.png"></td>
                    <td style="width: 20%;border: none"><img style="width: 75%;" src="/icons/closed.png"></td>
                </tr>
            </tbody>
        </table>
    }
    else
    {
        using (Data data = new Data())
        {
            if (data.Parties.FirstOrDefault() != null)
            {

                var party = data.Parties.FirstOrDefault();

                <div id="parent_div" style="position: relative; ">
                    <img class="img1 desktopImage" src="@Url.Content($"~/Uploads/{party.HorizontalPoster}")" />
                    <img class="img1 mobileImage" src="@Url.Content($"~/Uploads/{party.SquarePoster}")" />

                    @*<img src="/images/rock.jpg" style="width: 100%;height: auto; z-index: 1;">*@
                    <div id="box" style="text-shadow: 1px 1px black; position: absolute; top: 50%; left: 10%; border: solid white; z-index: 0; height: 33.33%; width: 18.75%; transform: translate(0,-50%); border-radius: 5%;">
                        <div id="city" style="text-shadow: 1px 1px black; color: white; position: absolute; top: 7%; left: 50%; /* transition: t; */ transform: translate(-50%,0); font-size: 1.2vw; font-style: italic;">
                            Stockholm
                        </div>
                        <div id="month" style="text-shadow: 1px 1px black; color: white; position: absolute; top: 22%; left: 50%; /* transition: t; */ transform: translate(-50%,0); font-size: 2vw; font-weight: 600;">
                            AUG
                        </div>
                        <div id="date" style="text-shadow: 1px 1px black; color: white; position: absolute; top: 43%; left: 50%; /* transition: t; */ transform: translate(-50%,0); font-size: 3vw;">
                            19
                        </div>
                        <div id="start-and-endtime" style=" text-shadow: 1px 1px black; color: white; position: absolute; top: 80%; left: 50%; /* transition: t; */ transform: translate(-50%,0); font-size: 1.2vw;">
                            24-04
                        </div>

                    </div>
                    <div id="top_title" style="text-shadow: 1px 1px black; color: white; position: absolute; top: 23%; left: 10%; font-size: 2vw; width: 50%; display: block; font-weight: 600;">
                        RELEASE PARTY
                    </div>
                    <div id="main_title" style="text-shadow: 1px 1px black; color: white; position: absolute; left: 32%; top: 33%; width: 65%; font-size: 3vw; font-weight: 600;">
                        FALL IN LOVE 
                        WITH THE BAD GUY
                    </div>
                    <div id="sub_title" style="text-shadow: 1px 1px black; color: white; position: absolute; left: 32%; top: 57%; width: 65%; font-size: 2vw; font-style: italic;">
                        with Klarc X Lurk Cobain
                    </div>


                </div>
                <input type="button" onclick="window.location.href='@party.PurchaseLink'" class="button_type_3" value="Buy Tickets" />

                <table class="three_heads">
                    <tbody>
                        <tr>
                            <td style="width: 20%;border:none"><img style="width: 77%;" src="/icons/closed.png"></td>
                            <td style="width: 60%;border: none;text-align: center;"><img style="width: 51%;" src="/icons/closed.png"></td>
                            <td style="width: 20%;border: none"><img style="width: 75%;" src="/icons/closed.png"></td>
                        </tr>
                    </tbody>
                </table>
            }
        }
    }








    <div id="journal_top">
        @if (Model.Journals.Count > 0)
        {
            int tracking_var_desc = Model.Journals.Count();
            int tracking_var_asce = 1;

            <div class="c">

                @foreach (var journal in Model.Journals)
                {
                    <input class="slider_input" type="radio" name="a" id="cr-@tracking_var_asce" @((tracking_var_asce == 1) ? "checked" : "")>
                    <label class="slider_label" id="<EMAIL>" onclick="handleClick(this.id)" for="cr-@tracking_var_asce" style="--hue: 32"></label>
                    <div class="ci" style="--z: @tracking_var_desc">
                        <img class="desktopImage" src="@Url.Content($"~/Uploads/{journal.DesktopImageUrl}")">
                        <img class="mobileImage" src="@Url.Content($"~/Uploads/{journal.MobileImageUrl}")">
                    </div>


                    tracking_var_desc--;
                    tracking_var_asce++;


                }

            </div>
        }

    </div>


    <div id="journal_data" style="margin-bottom: 3vw;">
        @if (Model.Journals.Count > 0)
        {
            bool first_track = true;



            @foreach (var journal in Model.Journals)
            {
                <div class="DescriptionSection" id="<EMAIL>" style="display: @(first_track ? "block" : "none")">
                    <p class="DescriptionTruncated">@journal.BodyText.ToString()</p>
                    <button onclick="window.location='@Url.Action("Index", "Journal", new { id = journal.Id })'" class="button_type_3">READ MORE</button>
                </div>
                @if (first_track == true)
                {
                    first_track = false;
                }

            }


        }
    </div>


    @*email input prompt*@


    <div id="email-prompt-overlay"></div>
    <div id="email-prompt">
        <button id="cross_button">&#x2715;</button>

        <div id="div_new">
            <select id="country" class="country" name="country">
                <option>Select country (Optional)</option>
                <option value="AF">Afghanistan</option>
                <option value="AX">Aland Islands</option>
                <option value="AL">Albania</option>
                <option value="DZ">Algeria</option>
                <option value="AS">American Samoa</option>
                <option value="AD">Andorra</option>
                <option value="AO">Angola</option>
                <option value="AI">Anguilla</option>
                <option value="AQ">Antarctica</option>
                <option value="AG">Antigua and Barbuda</option>
                <option value="AR">Argentina</option>
                <option value="AM">Armenia</option>
                <option value="AW">Aruba</option>
                <option value="AU">Australia</option>
                <option value="AT">Austria</option>
                <option value="AZ">Azerbaijan</option>
                <option value="BS">Bahamas</option>
                <option value="BH">Bahrain</option>
                <option value="BD">Bangladesh</option>
                <option value="BB">Barbados</option>
                <option value="BY">Belarus</option>
                <option value="BE">Belgium</option>
                <option value="BZ">Belize</option>
                <option value="BJ">Benin</option>
                <option value="BM">Bermuda</option>
                <option value="BT">Bhutan</option>
                <option value="BO">Bolivia</option>
                <option value="BQ">Bonaire, Sint Eustatius and Saba</option>
                <option value="BA">Bosnia and Herzegovina</option>
                <option value="BW">Botswana</option>
                <option value="BV">Bouvet Island</option>
                <option value="BR">Brazil</option>
                <option value="IO">British Indian Ocean Territory</option>
                <option value="BN">Brunei Darussalam</option>
                <option value="BG">Bulgaria</option>
                <option value="BF">Burkina Faso</option>
                <option value="BI">Burundi</option>
                <option value="KH">Cambodia</option>
                <option value="CM">Cameroon</option>
                <option value="CA">Canada</option>
                <option value="CV">Cape Verde</option>
                <option value="KY">Cayman Islands</option>
                <option value="CF">Central African Republic</option>
                <option value="TD">Chad</option>
                <option value="CL">Chile</option>
                <option value="CN">China</option>
                <option value="CX">Christmas Island</option>
                <option value="CC">Cocos (Keeling) Islands</option>
                <option value="CO">Colombia</option>
                <option value="KM">Comoros</option>
                <option value="CG">Congo</option>
                <option value="CD">Congo, Democratic Republic of the Congo</option>
                <option value="CK">Cook Islands</option>
                <option value="CR">Costa Rica</option>
                <option value="CI">Cote D'Ivoire</option>
                <option value="HR">Croatia</option>
                <option value="CU">Cuba</option>
                <option value="CW">Curacao</option>
                <option value="CY">Cyprus</option>
                <option value="CZ">Czech Republic</option>
                <option value="DK">Denmark</option>
                <option value="DJ">Djibouti</option>
                <option value="DM">Dominica</option>
                <option value="DO">Dominican Republic</option>
                <option value="EC">Ecuador</option>
                <option value="EG">Egypt</option>
                <option value="SV">El Salvador</option>
                <option value="GQ">Equatorial Guinea</option>
                <option value="ER">Eritrea</option>
                <option value="EE">Estonia</option>
                <option value="ET">Ethiopia</option>
                <option value="FK">Falkland Islands (Malvinas)</option>
                <option value="FO">Faroe Islands</option>
                <option value="FJ">Fiji</option>
                <option value="FI">Finland</option>
                <option value="FR">France</option>
                <option value="GF">French Guiana</option>
                <option value="PF">French Polynesia</option>
                <option value="TF">French Southern Territories</option>
                <option value="GA">Gabon</option>
                <option value="GM">Gambia</option>
                <option value="GE">Georgia</option>
                <option value="DE">Germany</option>
                <option value="GH">Ghana</option>
                <option value="GI">Gibraltar</option>
                <option value="GR">Greece</option>
                <option value="GL">Greenland</option>
                <option value="GD">Grenada</option>
                <option value="GP">Guadeloupe</option>
                <option value="GU">Guam</option>
                <option value="GT">Guatemala</option>
                <option value="GG">Guernsey</option>
                <option value="GN">Guinea</option>
                <option value="GW">Guinea-Bissau</option>
                <option value="GY">Guyana</option>
                <option value="HT">Haiti</option>
                <option value="HM">Heard Island and Mcdonald Islands</option>
                <option value="VA">Holy See (Vatican City State)</option>
                <option value="HN">Honduras</option>
                <option value="HK">Hong Kong</option>
                <option value="HU">Hungary</option>
                <option value="IS">Iceland</option>
                <option value="IN">India</option>
                <option value="ID">Indonesia</option>
                <option value="IR">Iran, Islamic Republic of</option>
                <option value="IQ">Iraq</option>
                <option value="IE">Ireland</option>
                <option value="IM">Isle of Man</option>
                <option value="IL">Israel</option>
                <option value="IT">Italy</option>
                <option value="JM">Jamaica</option>
                <option value="JP">Japan</option>
                <option value="JE">Jersey</option>
                <option value="JO">Jordan</option>
                <option value="KZ">Kazakhstan</option>
                <option value="KE">Kenya</option>
                <option value="KI">Kiribati</option>
                <option value="KP">Korea, Democratic People's Republic of</option>
                <option value="KR">Korea, Republic of</option>
                <option value="XK">Kosovo</option>
                <option value="KW">Kuwait</option>
                <option value="KG">Kyrgyzstan</option>
                <option value="LA">Lao People's Democratic Republic</option>
                <option value="LV">Latvia</option>
                <option value="LB">Lebanon</option>
                <option value="LS">Lesotho</option>
                <option value="LR">Liberia</option>
                <option value="LY">Libyan Arab Jamahiriya</option>
                <option value="LI">Liechtenstein</option>
                <option value="LT">Lithuania</option>
                <option value="LU">Luxembourg</option>
                <option value="MO">Macao</option>
                <option value="MK">Macedonia, the Former Yugoslav Republic of</option>
                <option value="MG">Madagascar</option>
                <option value="MW">Malawi</option>
                <option value="MY">Malaysia</option>
                <option value="MV">Maldives</option>
                <option value="ML">Mali</option>
                <option value="MT">Malta</option>
                <option value="MH">Marshall Islands</option>
                <option value="MQ">Martinique</option>
                <option value="MR">Mauritania</option>
                <option value="MU">Mauritius</option>
                <option value="YT">Mayotte</option>
                <option value="MX">Mexico</option>
                <option value="FM">Micronesia, Federated States of</option>
                <option value="MD">Moldova, Republic of</option>
                <option value="MC">Monaco</option>
                <option value="MN">Mongolia</option>
                <option value="ME">Montenegro</option>
                <option value="MS">Montserrat</option>
                <option value="MA">Morocco</option>
                <option value="MZ">Mozambique</option>
                <option value="MM">Myanmar</option>
                <option value="NA">Namibia</option>
                <option value="NR">Nauru</option>
                <option value="NP">Nepal</option>
                <option value="NL">Netherlands</option>
                <option value="AN">Netherlands Antilles</option>
                <option value="NC">New Caledonia</option>
                <option value="NZ">New Zealand</option>
                <option value="NI">Nicaragua</option>
                <option value="NE">Niger</option>
                <option value="NG">Nigeria</option>
                <option value="NU">Niue</option>
                <option value="NF">Norfolk Island</option>
                <option value="MP">Northern Mariana Islands</option>
                <option value="NO">Norway</option>
                <option value="OM">Oman</option>
                <option value="PK">Pakistan</option>
                <option value="PW">Palau</option>
                <option value="PS">Palestinian Territory, Occupied</option>
                <option value="PA">Panama</option>
                <option value="PG">Papua New Guinea</option>
                <option value="PY">Paraguay</option>
                <option value="PE">Peru</option>
                <option value="PH">Philippines</option>
                <option value="PN">Pitcairn</option>
                <option value="PL">Poland</option>
                <option value="PT">Portugal</option>
                <option value="PR">Puerto Rico</option>
                <option value="QA">Qatar</option>
                <option value="RE">Reunion</option>
                <option value="RO">Romania</option>
                <option value="RU">Russian Federation</option>
                <option value="RW">Rwanda</option>
                <option value="BL">Saint Barthelemy</option>
                <option value="SH">Saint Helena</option>
                <option value="KN">Saint Kitts and Nevis</option>
                <option value="LC">Saint Lucia</option>
                <option value="MF">Saint Martin</option>
                <option value="PM">Saint Pierre and Miquelon</option>
                <option value="VC">Saint Vincent and the Grenadines</option>
                <option value="WS">Samoa</option>
                <option value="SM">San Marino</option>
                <option value="ST">Sao Tome and Principe</option>
                <option value="SA">Saudi Arabia</option>
                <option value="SN">Senegal</option>
                <option value="RS">Serbia</option>
                <option value="CS">Serbia and Montenegro</option>
                <option value="SC">Seychelles</option>
                <option value="SL">Sierra Leone</option>
                <option value="SG">Singapore</option>
                <option value="SX">Sint Maarten</option>
                <option value="SK">Slovakia</option>
                <option value="SI">Slovenia</option>
                <option value="SB">Solomon Islands</option>
                <option value="SO">Somalia</option>
                <option value="ZA">South Africa</option>
                <option value="GS">South Georgia and the South Sandwich Islands</option>
                <option value="SS">South Sudan</option>
                <option value="ES">Spain</option>
                <option value="LK">Sri Lanka</option>
                <option value="SD">Sudan</option>
                <option value="SR">Suriname</option>
                <option value="SJ">Svalbard and Jan Mayen</option>
                <option value="SZ">Swaziland</option>
                <option value="SE">Sweden</option>
                <option value="CH">Switzerland</option>
                <option value="SY">Syrian Arab Republic</option>
                <option value="TW">Taiwan, Province of China</option>
                <option value="TJ">Tajikistan</option>
                <option value="TZ">Tanzania, United Republic of</option>
                <option value="TH">Thailand</option>
                <option value="TL">Timor-Leste</option>
                <option value="TG">Togo</option>
                <option value="TK">Tokelau</option>
                <option value="TO">Tonga</option>
                <option value="TT">Trinidad and Tobago</option>
                <option value="TN">Tunisia</option>
                <option value="TR">Turkey</option>
                <option value="TM">Turkmenistan</option>
                <option value="TC">Turks and Caicos Islands</option>
                <option value="TV">Tuvalu</option>
                <option value="UG">Uganda</option>
                <option value="UA">Ukraine</option>
                <option value="AE">United Arab Emirates</option>
                <option value="GB">United Kingdom</option>
                <option value="US">United States</option>
                <option value="UM">United States Minor Outlying Islands</option>
                <option value="UY">Uruguay</option>
                <option value="UZ">Uzbekistan</option>
                <option value="VU">Vanuatu</option>
                <option value="VE">Venezuela</option>
                <option value="VN">Viet Nam</option>
                <option value="VG">Virgin Islands, British</option>
                <option value="VI">Virgin Islands, U.s.</option>
                <option value="WF">Wallis and Futuna</option>
                <option value="EH">Western Sahara</option>
                <option value="YE">Yemen</option>
                <option value="ZM">Zambia</option>
                <option value="ZW">Zimbabwe</option>
            </select>
            <div id="under_country">
                Why do you want to know where i live?
                <br>
                Because when we plan to throw our next bad ass party in your country you
                will be the first to know.
            </div>
            <div id="input_div2">
                <input id="txt2" class="email_txt" type="email" placeholder="Enter your email address" required>
                <input type="button" onclick="submitButton2Clicked();" class="email_button" id="btn2" value="SUBMIT">
            </div>
        </div>
    </div>


</div>





@section Scripts{

    <script type="text/javascript" src="js/flipdown/flipdown.js"></script>


    <script type="text/javascript" src="js/start_page.js"></script>


    <script>
    var seconds = @Model.CountdownStartTime.TotalSeconds;
    seconds = Math.round(seconds);

    document.addEventListener('DOMContentLoaded', () => {

    //// Unix timestamp (in seconds) to count down to
    var timespan = (new Date().getTime() / 1000) + seconds;

    // Set up FlipDown
    var flipdown = new FlipDown(timespan)

    // Start the countdown
    .start()

    // Do something when the countdown ends
    .ifEnded(() => {
    console.log('The countdown has ended!');

    // here we can reload the page

    location.reload();

    });


    });

    </script>

    <script>

        function setJournalEntryWitdh() {

            var element = document.getElementById('journal_top');

            var offwidth = element.offsetWidth;

            var height = (offwidth * 9) / 16;

            document.getElementsByClassName("c")[0].style.height = height + 'px';



        }

        window.addEventListener("load", setJournalEntryWitdh);
        window.addEventListener("resize", setJournalEntryWitdh);




    </script>
}
