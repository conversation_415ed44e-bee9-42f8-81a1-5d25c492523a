﻿/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

body {
    font-family: '<PERSON><PERSON>', Arial, sans-serif;
}


/*h2 {
    vertical-align: center;
    text-align: center;
}

html,
body {
    margin: 0;
    height: 100%;
}*/

/** {
    font-family: "Raleway";
    box-sizing: border-box;
}*/

/*.top-nav {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #00BAF0;
    color: #FFF;
    height: 50px;
    padding: 1em;
}

    .top-nav img {
        height: 30px;
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translate(-50%, -50%);
    }

.menu {
    display: flex;
    flex-direction: row;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

    .menu > li {
        margin: 0 1rem;
        overflow: hidden;
    }


.admin-menu {*/
    /*display: flex;
    flex-direction: row;
    list-style-type: none;
    margin: 0;
    padding: 0;*/
/*}

    .admin-menu > li {*/
        /*margin: 0 1rem;
        overflow: hidden;*/
    /*}

.menu-button-container {
    display: none;
    height: 100%;
    width: 30px;
    cursor: pointer;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

#menu-toggle {
    display: none;
}

.menu-button,
.menu-button::before,
.menu-button::after {
    display: block;
    background-color: #fff;
    position: absolute;
    height: 4px;
    width: 30px;
    transition: transform 400ms cubic-bezier(0.23, 1, 0.32, 1);
    border-radius: 2px;
}

    .menu-button::before {
        content: '';
        margin-top: -8px;
    }

    .menu-button::after {
        content: '';
        margin-top: 8px;
    }

#menu-toggle:checked + .menu-button-container .menu-button::before {
    margin-top: 0px;
    transform: rotate(405deg);
}

#menu-toggle:checked + .menu-button-container .menu-button {
    background: rgba(255, 255, 255, 0);
}

    #menu-toggle:checked + .menu-button-container .menu-button::after {
        margin-top: 0px;
        transform: rotate(-405deg);
    }

@media (max-width: 2000px) {
    .menu-button-container {
        display: flex;
    }

    .menu {
        position: absolute;
        top: 0;
        margin-top: 50px;
        left: 0;
        flex-direction: column;
        width: 100%;
        justify-content: center;
        align-items: center;
    }

    #menu-toggle ~ .menu li {
        height: 0;
        margin: 0;
        padding: 0;
        border: 0;
        transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
    }

    #menu-toggle:checked ~ .menu li {
        border: 1px solid #333;
        height: 2.5em;
        padding: 0.5em;
        transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
    }

    .menu > li {
        display: flex;
        justify-content: center;
        margin: 0;
        padding: 0.5em 0;
        width: 100%;
        color: white;
        background-color: #222;
    }

        .menu > li:not(:last-child) {
            border-bottom: 1px solid #444;
        }
}*/


/*a.navbar-brand {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}*/

/* Provide sufficient contrast against white background */
/*a {
  color: #0366d6;
}

.btn-primary {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}*/

/* Sticky footer styles
-------------------------------------------------- */
/*html {
  font-size: 14px;
}
@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.border-top {
  border-top: 1px solid #e5e5e5;
}
.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy {
  font-size: 1rem;
  line-height: inherit;
}*/

/* Sticky footer styles
-------------------------------------------------- */
/*html {
  position: relative;
  min-height: 100%;
}

body {*/
/* Margin bottom by footer height */
/*margin-bottom: 60px;
}
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;*/ /* Vertically center the text there */
/*}*/
