﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>H4sIAAAAAAAACuwd227cuvG9QP9B0FNbJF5fXlpjfQ4SJ2ndxrGRdYyeJ0OW6LUQXbYS17Bb9Mv60E/qL5S68zKkSN1W3hP4ZS2SwxnOhcMhOfzff/67/Pk5DKwnlKR+HJ3ZRweHtoUiN/b8aH1mb/HD2z/aP//0298sP3rhs3Vb1TvJ6pGWUXpmP2K8OV0sUvcRhU56EPpuEqfxAz5w43DhePHi+PDwT4ujowUiIGwCy7KWX7cR9kOU/0P+PY8jF23w1gkuYw8FafmdlKxyqNYXJ0TpxnHRmZ3i2P3+GId3abyNvIT8CJ/cg6Kdbb0LfIfgtELBg205URRjBxOMT7+laIWTOFqvNuSDE9y8bBCp9+AEKSopOW2q6xJ1eJwRtWgaVqDcLUEzNAR4dFKO0oJv3mmsi6Eux/EjGW/8klGdj+WZ/S7Bfopti+/q9DxIsmrqgT4omr+xqkpvq0pvSaU3taAQecr+3ljn2wBvE3QWoS1OnOCNdb29D3z3b+jlJv6OorNoGwQ0wgRlUsZ8IJ+uk3iDEvzyFT2UZFx4trVg2y34hnUzqk1B4kWET45t6wvp3LkPUC0P1HCscJygP6MIJQ5G3rWDMUoIOy88lI+o0DvXFxkI7LtOcBE6a/QtCaqeiTASDbOtS+f5M4rW+PHMJj9t65P/jLzqS4nNt8gnCkka4WSL2jr8S5z4/4wjPGGXhSxkv0fv6gNK3cTfFDoxcl+XCDs3Pg7QJD1NSVmue+kj8j4QmR69txWmeyGG+aD8IqgdB+eL8+Svcy3kIP413iYRaWJbX1GQV0gf/U0JvJDGu6bOJ2KVvsZZN1zR3Yr8cDPUYrj8xknWCOvjtcIvAVJiVdUQcCoKZBiVpab43CSO+x3GJy+6K8Az+DAFdY8VPmxpha0uPre+h2IYn7zoLi9AHoAWVC5gB1aCkFwumvlQOUuWotB5mizb/5gnNQz7dxxvJpuzLuN7P0DTzcrI8QI/mmYq2c9J633svdygZzzV7EjPjdnvG7JeaZ+zxLkvwddEyC4JhISsOyqI72NisZyoC8CBJ9PSXOZTDGia8xJwPmVLBHPMFZvOFiVirZMYhBlbIpnG+mLWOp1BmLElkilMiZn25FWytOPUlbf+MXG1KyQZpmks7rXvZmvoo6k6IivjaTo6Gb0joqLISdEgNv3jcxYCcILPfvR93xZP2raeX6hIpoLBViriwon+LjHw3KJq6GVKaV8li5RiRJRLlKJKLxufE9rZxuetf9h4u0UJxwltSeWvXv52X7fz6gmv6gcxGdpOGIzTGMEWLZMBo9Mx0qJrMio7J7EZRbHaaJR1elmNHGBnq5G3/mE12qzGL/EWb+/RJG5CzpJpvNBqR+M6TokfNO2WRtFnNqJT9DtkIODHxsUr9Ia1V/H8XCJZ5BvGF1R+QKd4OYymJKg+WPzI2GdWosm61cPFkjpM00o8ualcb5rehkz4Jhfti/RT4KybswqdwzkE2sCTNlFeDyXBCzG/9H46y5NLFN6jhN95tK1bJ9iSL4cCE5kGK+eJqnykrnwTv0diD8fi6BfjTH98l6ax6+cDSvuXjfKz3X6MPEu9LqZNVC7yl2RE/Q0ZQ8L5M/sPAiVSkLU32ICst6WUQJcLiio1seyyQoaYZI3R4FWuRvVJlew2NhCrYyrDEtrKVdnSpTepO+BquQ5S48UvinoTygdmzPTBgEhukpZhJZuxG7zKRZE+obJQ/ogcBad9NX4yH6A34bLN+PGUF/AktFDk3IqhKOcifCNLOOuaaCHI+ylDEc6HKcyMhQHl/FkeGXbSgz1tsnh4cCD6FHK4U6g4e1KoBTO52e5D70i2u/B6yVlf7JCjEJXTRpbUDuDVkgO7pWOblmtDHukM2AphPmja+NbcSCzUAJplngCh5nELiCriKAAoh62lebXqEJqXWtXSvIpBCs1LZeeaU0ziKKBWvFQleIeIP6Hb6hjXWDfDxSKm4wpTQBpc+WPALIEaxN+wsXWRdraCHGumHkR5XkFNOQsDILzGcyC6VUznarRhrWK6NumTMr0SRRnpkDVu96K7kM3Z3lZ16UAyH9YSiVb51DpeNYV1ZZUUhEv86HH4DQfOZEOg8rT1fe0uwyHxrkcxAFCErmVEAO9b1//uMRqsxz2ebnBhwJaxKKtp0lDUHmI0SkjAcMDWpsNwCIfpxaFQ+uhaXnq7TGs55ePYC+7gvpT+1hkC9ti70T7gJFEFo2u3vC5bLorLd+WH5UJyS2956Ww2JARM3dorv1ir4sre+duV+Q22sICxcJlB5RcRdU84TsgxW66UdE0w/eQnKc6WGffkCJhtnXuhUC1fhEjc66oLdp0h8qjyuqv62e+ijcZNOh5eM4ifCF0hinBOIhLkRWxImq7I/qyTANfezuNgG0ayq3Oq1uJFNhqWWKoPGbqxRsOGyvWh05fTaKj0d31ozDYuDY4p0IdHHdenoVGfzWBJ8RMK9eFyt8VoqFyRPsxyb4uGVX4SYSwXnCbwqkdtp5U1OePHq7KWoismtI6aXoURzFVd2nIcXRcu43CSzhYaSCh36YYRUK7MwH7Ud2sYq1F/3S9tbK7B0ACbr8Z6LdXqDjrN33Xh9Jsv3it7IXUAO1oLcIWlYSsk7caxFPTtB5Y5zXcDiawvODDiWH81hnQMQjruAOkEhHRiAom5g0ADYwr04bH3EGiAbMleaZl0rd1Ry3J4HbRM0m4cLRO9V5nfuiOulJGLwbhS7FiYc0XSbhyu/EKf76XBMAUGKyzqDC+ztqK+m6/XqGO60IKNKu6yYuMO5MLLNq7S7jyUX+PqbU42nQ1CAe4TFWg08ZKoZqA3JFk+ZXE1yJ8RIpTiQGmZkwIYZFSygaq7NsaqDPt1xKoEY4gXH0E05i+3uWoyPTetwFkYjoMBwwhuy3YcxQLWALwF93k7IlVGh3fCWWPN5ZuBvNXXXHjneR7c3QfNrTYWjLhbNQJ5K91CBMeQ3TeZB1/ZbZgJZ4nePOWPAJj45Qqdzcv1dRY+PdBxHAtgA/AWPo7wunQWPuVgxGW+Mchr7blXdT5iHgyHD1y8qpkYOsfRhedlU5DjulZbfgBkVuxmTpS8PgvOHVTpwuyqLchtMMzVNqbsCZdZsZs9MjOhF9Gb38JJHKPjBYo5W3+6lpzfmdJEauHVj7vGCJFjJJ6fH+C/SLN7yPVNTS1qhxIMQ/+ca6W1iSQf+kGc88EFYhD3fHbiIBzY4qvUUbzyS/1/fWCrPCzVnmtdOD1VVLEtQvwTsavk5NTqhQR3w4OswsHqH8F54JO4YVPh0on8B5Ti4tawfXx4dMwlaZ9PwvRFmnqBVtb0yROV+9mYtqYiN8xJIctNHj05ifvoJL8Lneff01D75R/vBVbMMd4LHJCOoxc8Ie1qb2hDYwjm/O4FkUnGkQuoUv6Mcy7vh5JJEhv3kw8weXE/teUSFO+5NvDJhIdQLVqxPPIbD5ow+N5vU7DeGiqzurmbQgER7mpeRB56PrP/lbc6tS7+flc3fGNdJcQNOLUOrX+rx9Uwre1+WAcxe2w/SeQyxA4BrM4COwSwk0GAAdlcOysclM11ymnxlSgdEAh6tUrX0Ys0zK64H2PFnGUaQDvERIX9wInHmAZdw4AJB2fjLPz6VjFjrTnkx4T0NFexBSBWlu8Jtqt705HZ3FC1k00NWvJGI27WfdPSAAGzyal7aEYRzxcrKyJ3bbxr+jEbvKpdL96N51X05lwPvTNiXR+92ynvZq13nZdjRryTGdiZc25Cc23mnvbQOcXO7LA613RkNnhVu73UuVvZsZsRmNdjrtsp7+Y51+WkdbaWRozrbi13yrfZWsvO0Q8jrskmxJlzbcIJVsa1tuzN4MkEPs1ce6LDI+FExFX0gcQiMbLeucWu9bmTuo4njll2YkDZe51jkEdCL8EkAUgECSVZmMcJyNmAFCfkdIBwg+c68SPX3zgBQzlXSzM4lZFVw+NLPqANIiyPMESkTn+qU3U1cG6Q24bAIA2nxp0bioUQ9ySM20MhMmHr1GKkPoA9gSC13zricwC2Z9QdSYboOIqAg1Yy564SJLuGPbj8KG6MmS5SpxeeFiMEcA5m2t6JjwFDJ5Yf5aHGyQRI1w+agfmBpjC2YA8MkMkENgsL9Hr8oF0K0YR+kLEYzcQPUpwUn5cdEpeE9Oc9sEH6K79ZWCDFYf7ZLOR3JTiTLeINBWfHS/giuqXr+9wWrySI7yZMIjp5X9C0xRaMIj4FnROID0OLTn95g9kI0Ox9n10K0YS+j7EY7dz30b79Pi97BKzlme97YI0MlvKzMEYaWQpmFBHanRRNFxEyFaNdR4Q0EzPMyxKJTjX9eQ/skL5TPSczJM+eMZs12a7EZ7I1maH4zGJN1p4oZF7mR4xH0Z/3wPzoh57mZH4U2VxmE0zclfxMFkw0lJ8dBxPNXpTt/6KqQVDg1Xm+JkvwXXu9Jq/q9ue6psvz6jiu72ZMy2/4ZWHx7S+dJ4QVLwgX2W/ObO8+80eKU4qyVxglzwurXheGwEtfsgPfHpY/PQzBlrwVBz5LLH+VGIIseX8QfLFY/mAxBDkv0hwN+ZCvmGLp2GgPPnPCBR4olQxRFTT7kpPGFkt7MyMtHw1ZZ2WhtKu8XFMY5HSxxVLR0KaLCUHBvalYRlXQ7Es2hnShtCeTMZTpK10o7UiiuRqPhddPWra9GS5JEydOn0I+OnEqkZ1/lb0A0P89UI6W6gnOVqLhLHSQpwjkZpwD4fCj5i2PpLfzmpFW6mkdFcmAuRWeAxiaYDmjdTL4Az6m+JLnXEjW0GitbPtDcBkSbLZgaKLbGD2+Rk9LeDG1tPKarTYCp0WjT38eltg2HrPVhp2opiCWO7gg5y1cUY4847FTT26pCIYcO/G1gMGJlvNY41jHIHo8LeHw6YK2t+u15+nOnAcmLeb7OOS3cV8r23/vWXsHA1C4nZrcZyqPwHvRzNGfxyBck+9M5WHt++SEs7tobZQrk/MPwXPRAaA/j0K6JtOVyf97ujBTkC6k8hfpVW5vDGDQDKbC7gRWwQ4peWWFoYnT1HYFYUJu9rpsuShCLuUH8q+Qg53Ev7ekdVjcbCex99RfNyCyvPIRyjciG6BVnYvoIa5i8BxGVRUgOZtHwuLZsDw4LibFLkpTP1rb1q0TbEmVj+E98i6iqy3ebDEhGYX3wQs9GFkgX9X/ciHgvLzKc8GlQ5BA0PSz1HlX0futH3g13p+AvAESENkOQZksMeMl2XDAaE1oLCF9ifn342WAyuGrNzZuULjJbE56Fa2cJwLEHLdvKfqM1o77UqXSlwNpZwQ77MsPvrNOnDAtYTTtyb9Ehr3w+af/AwAA//8DAKMOOlocvgAA</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>