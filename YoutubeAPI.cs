﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Google.Apis.YouTubeAnalytics.v1;
using Google.Apis.Auth.OAuth2;
using Google.Apis.YouTube.v3;
using System.Threading;
using Google.Apis.Util.Store;
using System.IO;
using Google.Apis.Services;
using stockhom_soundrom_mvc.Models;


namespace stockhom_soundrom_mvc
{
    public class YoutubeAPI
    {

        YouTubeService youtubeService = null;

        public YoutubeAPI()
        {

        }

        public async Task Method()
        {
            UserCredential credential;
            using (var stream = new FileStream("Credentials\\youtube_api.json", FileMode.Open, FileAccess.Read))
            {
                credential = await GoogleWebAuthorizationBroker.AuthorizeAsync(
                    GoogleClientSecrets.Load(stream).Secrets,

                    new[] { YouTubeService.Scope.YoutubeForceSsl },
                    "user",
                    CancellationToken.None,
                    new FileDataStore(this.GetType().ToString())
                );
            }
            youtubeService = new YouTubeService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = this.GetType().ToString(),

            });

        }


        public int GetVideoCountForVideoId(string video_id)
        {



            // Define the YouTube API request
            var videoRequest = youtubeService.Videos.List("statistics");
            videoRequest.Id = video_id;

            // Execute the request and retrieve the response
            var videoResponse = videoRequest.Execute();

            // Retrieve the view count from the video response
            var viewCount = videoResponse.Items[0].Statistics.ViewCount;

            return (int)viewCount;

        }



        public void SetVideoRankingsforLast10Days()
        {
            Dictionary<Video, int> VideoAndIncreasedViewCount = new Dictionary<Video, int>();
            Dictionary<Video, int> AscendingDict = new Dictionary<Video, int>();


            using (Data data = new Data())
            {
                var videos = data.Videos.ToList();
                foreach (var video in videos)
                {
                    int viewCount = IncreasedViewCountLastNDaysd(video,10);
                    if (viewCount > 0)
                    {
                        VideoAndIncreasedViewCount.Add(video, viewCount);
                    }
                }


                var x = VideoAndIncreasedViewCount.OrderByDescending(v => v.Value).ToList();
                x = x.Where(pair => pair.Value > 0).ToList();


                var rankings = data.VideoRankings10.ToList();

                foreach (var ranking in rankings)
                {
                    data.VideoRankings10.Remove(ranking);
                    data.SaveChanges();
                }


                foreach (var kv in x)
                {
                    data.VideoRankings10.Add(new VideoRanking10() { Rank = kv.Value, Video = kv.Key });
                    data.SaveChanges();
                }
            }
        }


        public void SetVideoRankingsforLast30Days()
        {
           


            Dictionary<Video, int> VideoAndIncreasedViewCount = new Dictionary<Video, int>();
            Dictionary<Video, int> AscendingDict = new Dictionary<Video, int>();


            using (Data data = new Data())
            {
                var videos = data.Videos.ToList();
                foreach (var video in videos)
                {
                    int viewCount = IncreasedViewCountLastNDaysd(video, 30);
                    if (viewCount > 0)
                    {
                        VideoAndIncreasedViewCount.Add(video, viewCount);
                    }
                }


                var x = VideoAndIncreasedViewCount.OrderByDescending(v => v.Value).ToList();
                x = x.Where(pair => pair.Value > 0).ToList();


                var rankings = data.VideoRankings30.ToList();

                foreach (var ranking in rankings)
                {
                    data.VideoRankings30.Remove(ranking);
                    data.SaveChanges();
                }


                foreach (var kv in x)
                {
                    data.VideoRankings30.Add(new VideoRanking30() { Rank = kv.Value, Video = kv.Key });
                    data.SaveChanges();
                }
            }
        }


        public int IncreasedViewCountLastNDaysd(Video video, int daysN)
        {
            //get all the VideoView Objects related to this video
            //find the VideoView object which is 10 days before today
            //if there is no such object then look for the object which date condition
            ////such that its date should be less than 10 days from 
            ///

            using (Data data = new Data())
            {
                var relatedVVs = data.VideoViews.Where(vv => vv.Video.Id == video.Id).ToList();
                var latestDate = relatedVVs.Max(vv => vv.Date);
                var latestDateObj = relatedVVs.Where(vv => vv.Date == latestDate).FirstOrDefault();
                var date10DaysBefore = latestDate.AddDays(daysN*-1);
                var _10DaysOrFurtherstObj = relatedVVs.Where(vv => vv.Date == latestDate).FirstOrDefault();


                if (_10DaysOrFurtherstObj == null)
                {
                    var date = relatedVVs.Where(vv => vv.Date > date10DaysBefore).Min(vv => vv.Date);
                    _10DaysOrFurtherstObj = relatedVVs.Where(vv => vv.Date == date).FirstOrDefault();
                }

                return latestDateObj.ViewCount - _10DaysOrFurtherstObj.ViewCount;


            }
        }


        public void AddTodaysCountForAllVideos()
        {
            using (Data data = new Data())
            {
                var allVideos = data.Videos.ToList();

                foreach (var video in allVideos)
                {
                    int todaysVideoCount = GetVideoCountForVideoId(video.YoutubeLink);

                    data.VideoViews.Add(new VideoViews() { Video = video, Date = DateTime.Today, ViewCount = todaysVideoCount });
                    data.SaveChanges();

                }

            }
        }







    }
}
