﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>