﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Controllers
{

    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminPartyController : Controller
    {
        private readonly IWebHostEnvironment _env;

        public AdminPartyController(IWebHostEnvironment env)
        {
            _env = env;
        }


        public IActionResult Index()
        {
            return View(new AdminPartyViewModel());
        }

        public IActionResult NewAddSection()
        {
            AdminPartyViewModel adminArtistViewModel = new AdminPartyViewModel();


            adminArtistViewModel.Party = new Models.Party();
            adminArtistViewModel.AddSectionVisible = true;


            return View("index", adminArtistViewModel);
        }

        [HttpPost]
        public ActionResult Save(AdminPartyViewModel adminPartyViewModel, IFormFile picture1, IFormFile picture2)
        {

            using (var data = new Data())
            {
                var party = GeneratePartyFromAdminPartyViewModel(data, adminPartyViewModel, picture1, picture2, State.Saved);
                data.Parties.Add(party);
                data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));




        }


        [HttpPost]
        public ActionResult Publish(AdminPartyViewModel adminPartyViewModel, IFormFile picture1, IFormFile picture2)
        {
            //do data validation



            using (Data data = new Data())
            {
                var party = GeneratePartyFromAdminPartyViewModel(data, adminPartyViewModel, picture1, picture2, State.Published);

                if (party.StartDate == DateTime.MinValue) // means the publish day is not set
                {
                    party.StartDate = DateTime.Now;
                }

                if (party.StartDate >= DateTime.Now)
                {
                    party.State = State.ToBePublished;
                }
                else
                {
                    party.State = State.Published;
                }

                data.Parties.Add(party);
                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }


        public ActionResult ViewForUpdate(AdminPartyViewModel  adminPartyViewModel, int id)
        {
            using (Data data = new Data())
            {


                adminPartyViewModel.Party = data.Parties.Where(p => p.Id == id).First();
                adminPartyViewModel.ViewForUpdate = true;
                adminPartyViewModel.AddSectionVisible = true;

                return View("index", adminPartyViewModel);
            }


        }



        [HttpPost]
        public ActionResult EditSave(int party_id, AdminPartyViewModel adminPartyViewModel, IFormFile picture1, IFormFile picture2)
        {
            //do data validation



            using (Data data = new Data())
            {
                var party = GeneratePartyFromAdminPartyViewModel(data, adminPartyViewModel, picture1, picture2, State.Saved);

                var existingParty = data.Parties.Where(p => p.Id == party_id).FirstOrDefault();
                    

                if (string.IsNullOrEmpty(party.SquarePoster))
                {
                    party.SquarePoster = existingParty.SquarePoster;
                }
                if (string.IsNullOrEmpty(party.HorizontalPoster))
                {
                    party.HorizontalPoster = existingParty.HorizontalPoster;
                }


                data.SaveChanges();

                existingParty.HorizontalPoster = party.HorizontalPoster;
                existingParty.MainTitle = party.MainTitle;
                existingParty.PurchaseLink = party.PurchaseLink;
                existingParty.SquarePoster = party.SquarePoster;
                existingParty.StartDate = party.StartDate;
                existingParty.State = State.Saved;
                existingParty.SubTitle = party.SubTitle;
                existingParty.TopTitle = party.TopTitle;

                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }


        [HttpPost]
        public ActionResult EditPublish(int party_id, AdminPartyViewModel adminPartyViewModel, IFormFile picture1, IFormFile picture2)
        {
            //do data validation



            using (Data data = new Data())
            {
                var party = GeneratePartyFromAdminPartyViewModel(data, adminPartyViewModel, picture1, picture2, State.Saved);

                var existingParty = data.Parties.Where(p => p.Id == party_id).FirstOrDefault();


                if (string.IsNullOrEmpty(party.SquarePoster))
                {
                    party.SquarePoster = existingParty.SquarePoster;
                }
                if (string.IsNullOrEmpty(party.HorizontalPoster))
                {
                    party.HorizontalPoster = existingParty.HorizontalPoster;
                }


                data.SaveChanges();

                existingParty.HorizontalPoster = party.HorizontalPoster;
                existingParty.MainTitle = party.MainTitle;
                existingParty.PurchaseLink = party.PurchaseLink;
                existingParty.SquarePoster = party.SquarePoster;
                existingParty.StartDate = party.StartDate;
                existingParty.State = State.Published;
                existingParty.SubTitle = party.SubTitle;
                existingParty.TopTitle = party.TopTitle;

                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }




        private Party GeneratePartyFromAdminPartyViewModel(Data data, AdminPartyViewModel adminPartyViewModel, IFormFile square_image, IFormFile horizontal_image, State state)
        {
            try
            {
                Party party = new Party();

                //control bounded data
             


                party.MainTitle = adminPartyViewModel.Party.MainTitle;
                party.PurchaseLink = adminPartyViewModel.Party.PurchaseLink;
                party.StartDate = adminPartyViewModel.Party.StartDate;
                party.SubTitle = adminPartyViewModel.Party.SubTitle;
                party.TopTitle = adminPartyViewModel.Party.TopTitle;
                party.State = State.Saved;


                //2 images
                if (square_image != null && square_image.Length > 0)
                {
                    party.SquarePoster = saveIFromFile(square_image);
                }
                if (horizontal_image != null && horizontal_image.Length > 0)
                {
                    party.HorizontalPoster = saveIFromFile(horizontal_image);
                }




                

                return party;
            }
            catch (Exception ex)
            {
                return null;
            }

        }


        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }


        public IActionResult DeleteParty(int party_id)
        {
            using (Data data = new Data())
            {
                var partyToBeRemoved = data.Parties.Where(a => a.Id == party_id).Single();
                data.Parties.Remove(partyToBeRemoved);
                data.SaveChanges();
            }

            return View("Index", new AdminPartyViewModel());
        }




    }
}
