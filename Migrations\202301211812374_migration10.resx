﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>