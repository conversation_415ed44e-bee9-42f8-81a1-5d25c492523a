﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace stockhom_soundrom_mvc.Controllers
{
    public class MusicController : Controller
    {

        private readonly IWebHostEnvironment _env;

        public MusicController(IWebHostEnvironment env)
        {
            _env = env;


        }

        public IActionResult Index(int id = 10)
        {
            MusicViewModel musicViewModel = new MusicViewModel();


            using (Data data = new Data())
            {
                musicViewModel.Music = data.Musics
                        .Where(x => x.Id == id)
                        .Include(x => x.BuyLinks)
                        .Include(x => x.Tracks.Select(t => t.RelatedArtists))
                        .Include(x => x.Tracks.Select(t => t.RelatedJournals))
                        .Include(x => x.Tracks.Select(t => t.RelatedStyles))
                        .First();

                musicViewModel.TracksOrdred = data.Musics.Where(x => x.Id == id).First().Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();

                foreach (var track in musicViewModel.Music.Tracks)
                {
                    if (track.RelatedJournals != null && track.RelatedJournals.Count > 0)
                    {
                        musicViewModel.RelatedJournals.AddRange(track.RelatedJournals?.ToList());
                    }
                    if (track.RelatedStyles != null && track.RelatedStyles.Count > 0)
                    {
                        musicViewModel.RelatedStyles.AddRange(track.RelatedStyles?.ToList());
                    }
                }


            }

            return View(musicViewModel);
        }




        //to preview before creating Music. 
        [HttpPost]
        public IActionResult Preview(AdminMusicCombineViewModel adminMusicCombineViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {


            var track1_id = int.Parse(Request.QueryString.Value.Split('&').First().Split('=').Last());
            var track2_id = int.Parse(Request.QueryString.Value.Split('&').Last().Split('=').Last());

            MusicViewModel musicViewModel = new MusicViewModel();




            using (Data data = new Data())
            {

                Track track1 = data.Tracks.Where(t => t.Id == track1_id)
                        .Include(x => x.BuyLinks)
                        .Include(x => x.RelatedArtists)
                        .Include(x => x.RelatedJournals)
                        .Include(x => x.RelatedStyles)
                        .Include(x => x.RelatedVideos)
                        .First();
                track1.TrackNoInMusicCollection = 1;

                Track track2 = data.Tracks.Where(t => t.Id == track2_id)
                        .Include(x => x.BuyLinks)
                        .Include(x => x.RelatedArtists)
                        .Include(x => x.RelatedJournals)
                        .Include(x => x.RelatedStyles)
                        .Include(x => x.RelatedVideos)
                        .First();
                track2.TrackNoInMusicCollection = 2;

                musicViewModel.Music = GenerateMusicFromCombineMusicViewModel(data, adminMusicCombineViewModel, pic_vertical_upload, pic_horizontal_upload, State.ToBePublished);
                musicViewModel.TracksOrdred.Add(track1);
                musicViewModel.TracksOrdred.Add(track2);

                foreach (var track in musicViewModel.TracksOrdred)
                {
                    if (track.RelatedJournals != null && track.RelatedJournals.Count > 0)
                    {
                        musicViewModel.RelatedJournals.AddRange(track.RelatedJournals?.ToList());
                    }
                    if (track.RelatedStyles != null && track.RelatedStyles.Count > 0)
                    {
                        musicViewModel.RelatedStyles.AddRange(track.RelatedStyles?.ToList());
                    }
                }

            }

            return View("Index", musicViewModel);
        }



        //to preview after creating Music. 
        [HttpPost]
        public IActionResult PreviewEdit(int music_id, AdminMusicEditMusicViewModel adminMusicEditMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            MusicViewModel musicViewModel = new MusicViewModel();
            musicViewModel.Music = adminMusicEditMusicViewModel.Music;


            using (Data data = new Data())
            {

                if (pic_horizontal_upload == null || pic_horizontal_upload.Length == 0)
                {
                    musicViewModel.Music.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);
                }
                else
                {
                    musicViewModel.Music.ArtWorkHoriontal = data.Musics.Where(m => m.Id == music_id).FirstOrDefault().ArtWorkHoriontal;
                }

                if (pic_vertical_upload == null || pic_vertical_upload.Length == 0)
                {
                    musicViewModel.Music.ArtWorkSquare = saveIFromFile(pic_vertical_upload);
                }
                else
                {
                    musicViewModel.Music.ArtWorkSquare = data.Musics.Where(m => m.Id == music_id).FirstOrDefault().ArtWorkSquare;
                }

                musicViewModel.TracksOrdred = data.Musics.Where(x => x.Id == music_id).Include(m => m.Tracks.Select(t => t.RelatedArtists)).First().Tracks.OrderBy(t => t.TrackNoInMusicCollection).ToList();

                foreach (var track in musicViewModel.Music.Tracks)
                {
                    if (track.RelatedJournals != null && track.RelatedJournals.Count > 0)
                    {
                        musicViewModel.RelatedJournals.AddRange(track.RelatedJournals?.ToList());
                    }
                    if (track.RelatedStyles != null && track.RelatedStyles.Count > 0)
                    {
                        musicViewModel.RelatedStyles.AddRange(track.RelatedStyles?.ToList());
                    }
                }

            }
            return View("Index", musicViewModel);
        }


        private Music GenerateMusicFromCombineMusicViewModel(Data data, AdminMusicCombineViewModel adminMusicCombineViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, State state)
        {
            try
            {
                Music music = new Music()
                {
                    AppleAlbumLink = adminMusicCombineViewModel.Music.AppleAlbumLink,
                    Description = adminMusicCombineViewModel.Music.Description,
                    MetaDescription = adminMusicCombineViewModel.Music.MetaDescription,
                    MetaTitle = adminMusicCombineViewModel.Music.MetaTitle,
                    PublishDate = adminMusicCombineViewModel.Music.PublishDate,
                    SoundCloudAlbumLink = adminMusicCombineViewModel.Music.SoundCloudAlbumLink,
                    SpotifyAlbumLink = adminMusicCombineViewModel.Music.SpotifyAlbumLink,
                    StartCountDown = adminMusicCombineViewModel.Music.StartCountDown,
                    Title = adminMusicCombineViewModel.Music.Title,
                    TopPromo = adminMusicCombineViewModel.Music.TopPromo,
                    YoutubeAlbumLink = adminMusicCombineViewModel.Music.YoutubeAlbumLink,
                    YoutubeMusicAlbumLink = adminMusicCombineViewModel.Music.YoutubeMusicAlbumLink,
                    State = state
                };



                //music.Tracks.Add(adminMusicCombineViewModel.Track1);
                //music.Tracks.Add(adminMusicCombineViewModel.Track2);



                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    music.ArtWorkSquare = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    music.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);
                }




                //request form data


                var buylinks = Request.Form[Global.BuyLinkTXTName];
                foreach (var buylinksIdString in buylinks)
                {
                    music.BuyLinks.Add(new MusicBuyLink() { Link = buylinksIdString });

                }




                music.MusicCollectionType = (MusicCollectionType)Enum.Parse(typeof(MusicCollectionType), Request.Form["coltype"].ToString());



                return music;
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }
    }
}
