﻿@model stockhom_soundrom_mvc.ViewModels.AdminPresentationViewModel
@using stockhom_soundrom_mvc.Controllers
@using System.IO
@using System.Linq;
@using System.Data.Entity;


@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}

@section Styles{






    <style>
        body {
            background-color: black;
        }

        form {
            position: relative;
        }

        #add_entity {
            position: relative;
            padding: 50px;
        }


            #add_entity .close-button {
                position: absolute;
                top: 0;
                right: 50px;
                cursor: pointer;
                background-color: black;
                border: solid;
                color: white;
            }

        #lbl_entity {
            color: white;
            font-size:large;
        }


        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }



        .profile_container {
            width: 800px;
        }

        .profile_box {
            width: 220px;
            height: 170px;
            display: inline-block;
            text-align: center;
            margin: 14px;
        }

            .profile_box img {
                height: 170px;
                width: 220px;
                cursor: pointer;
                transition: all 0.5s ease;
            }

                .profile_box img:hover {
                    transform: scale(1.1);
                }

            .profile_box .entity_name {
                width: 150px;
                text-align: center;
                color: white;
                margin-bottom: 3px;
                display: block;
            }

            .profile_box .state {
                border: solid;
                color: white;
                display: block;
            }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }


        .button_container {
            /*margin-left: 50px;*/
            margin-top: 20px;
        }






        .plus {
            font-size: 73px;
            text-align: center;
            margin: auto;
            color: white;
            font-weight: 900;
        }


        .add_another {
            display: inline;
            text-align: right;
            color: white;
            position: absolute;
            right: 35px;
            font-size: x-small;
        }

            .add_another:hover {
                color: blue;
            }

        .select_related {
            background-color: black;
            color: white;
            margin: 3px;
            position: relative;
        }

        /*.select_related .close-button {
        position: absolute;
        top: 0;
        right: 0;
        display: none;
        }

        .select_related:hover .close-button {
        display: block;
        }

        .close-button {*/
        /* style the close button, such as color, size, etc. */
        /*color:red;
        }*/


        .parent {
            z-index: 1;
            background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
        }

        .child {
            position: absolute;
            z-index: 1;
        }



        .remove {
            position: absolute;
            top: 0px;
            right: 0px;
            display: block;
            box-sizing: border-box;
            width: 20px;
            height: 20px;
            border-width: 3px;
            border-style: solid;
            border-color: red;
            border-radius: 100%;
            background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
            background-color: red;
            box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
            transition: all 0.3s ease;
        }


        .tag {
            padding: 5px;
            background: aliceblue;
            border: black 2px solid;
            border-radius: 0px 15px 15px 0px;
            display: inline-block;
            cursor: pointer;
            color: black;
        }

        #tagContainer {
        }

        #taginputText {
            border: none;
            background: none;
        }

        #container {
            background: white;
            padding: 10px;
        }


        .pic_square {
            width: 120px;
            height: 120px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
            margin-left:30px;
        }

        .pic_horizontal {
            width: 240px;
            height: 120px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
        }
    </style>



}


<div class="container">
    <div style="/*margin-left: 50px; */ margin-bottom: 20px">
        <label id="lbl_entity">Presentation</label>

    </div>

    <div>


        <form id="form" method="post" enctype="multipart/form-data">






            <div style="color:white;margin-top:40px;margin-bottom:10px">Music Start Page</div>

            <div id="pic_1" class="pic_horizontal" style="background-image: url(@Url.Content("~/Uploads/"+Model.Presentation.HorizontalMusicImage))" onclick="music_hor_pic.click();">
                <div class="desktop_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                    <div style="color: white; font-size:20px; font-weight: 900; text-shadow: 3px 3px black; ">@Model.Presentation.MusicText</div>
                </div>
            </div>
            <input id='music_hor_pic' name="music_hor_pic" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_2" class="pic_square" style="background-image: url(@Url.Content("~/Uploads/"+Model.Presentation.SquareMusicImage))" onclick="music_square_pic.click();">
                <div class="desktop_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                    <div style="color: white; font-size:10px; font-weight: 900; text-shadow: 3px 3px black; ">@Model.Presentation.MusicText</div>
                </div>
            </div>
            <input id='music_square_pic' name="music_square_pic" type='file' value="" style='display: none' accept='.jpg' />


            <div class="infobox">
                <label>Text</label>
                @Html.TextBoxFor(m => m.Presentation.MusicText)
            </div>


            <div style="color:white;margin-top:40px;margin-bottom:10px">Style Start Page</div>
            <div id="pic_3" class="pic_horizontal" style="background-image: url(@Url.Content("~/Uploads/"+Model.Presentation.HorizontalMerchImage))" onclick="merch_hor_pic.click();">
                <div class="desktop_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                    <div style="color: white; font-size:20px; font-weight: 900; text-shadow: 3px 3px black; ">@Model.Presentation.MerchText</div>
                </div>
            </div>
            <input id='merch_hor_pic' name="merch_hor_pic" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_4" class="pic_square" style="background-image: url(@Url.Content("~/Uploads/"+Model.Presentation.SquareMerchImage))" onclick="merch_square_pic.click();">
                <div class="desktop_img_overlay" style="position: absolute; top: 50%; left: 50%; text-align: center; transform: translate(-50%,-50%);">
                    <div style="color: white; font-size:10px; font-weight: 900; text-shadow: 3px 3px black; ">@Model.Presentation.MerchText</div>
                </div>
            </div>
            <input id='merch_square_pic' name="merch_square_pic" type='file' value="" style='display: none' accept='.jpg' />


            <div class="infobox">
                <label>Text</label>
                @Html.TextBoxFor(m => m.Presentation.MerchText)
            </div>


            <div class="button_container">
                <input type="submit" formaction="@Url.Action("Update","AdminPresentation")" value="Update" class="btn1" />
            </div>

        </form>


    </div>



</div>


@section Scripts{


    <script>


















        function image_upload(input_file_elem_id, pic_display_div_id) {

            console.log("660");



            var fileInput = document.getElementById(input_file_elem_id);
            var file = fileInput.files[0];

            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById(pic_display_div_id);
            div.style.backgroundImage = 'url(' + fileUrl + ')';

            console.log("673");

        }

        document.getElementById("music_hor_pic").addEventListener("change", function () {
            console.log("406");
            image_upload('music_hor_pic', 'pic_1');
        }, false);

        document.getElementById("music_square_pic").addEventListener("change", function () {
            image_upload('music_square_pic', 'pic_2');
        }, false);
        document.getElementById("merch_hor_pic").addEventListener("change", function () {
            image_upload('merch_hor_pic', 'pic_3');
        }, false);

        document.getElementById("merch_square_pic").addEventListener("change", function () {
            image_upload('merch_square_pic', 'pic_4');
        }, false);



        function SaveImageAndReturnGuid(file) {

            var guid = null;


            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                type: "POST",
                url: "SaveImage.ashx",
                data: formData,
                contentType: false,
                processData: false,
                async: false,
                success: function (response) {
                    guid = response;
                },
                error: function (ex) {
                    test1 = ex;
                }
            });


            return guid;
        }









        function confirmDelete(id) {
            if (confirm("Are you sure you want to delete this?")) {

                window.location.href = "/AdminArtists/DeleteArtist?artist_id=" + id;

            }
        }


    </script>





}