﻿@{
    ViewData["Title"] = "Home Page";
}

@section Styles{

    <link rel="stylesheet" type="text/css" href="~/css/teststyle.css" />

}





@{

    <div style="background-color:white">


        @using (Data data = new Data())
        {

            var journals = data.Journals.ToList();

            @foreach (var item in journals)
            {
                @Html.Label(item.Headline, item.Headline, new { style = "color:red" });
                <br />
            }
        }


    </div>
}


