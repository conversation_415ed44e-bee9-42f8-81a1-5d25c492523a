﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>