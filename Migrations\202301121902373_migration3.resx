﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>