﻿@model stockhom_soundrom_mvc.ViewModels.AdminMusicEditMusicViewModel
@{
    ViewData["Title"] = "EditMusic";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}


@{
    var tempx = 0;
    var selected_track_publish_date = DateTime.MinValue;
    var datetime_element_id = "";

}


@section Styles{

    <link rel="stylesheet" href="~/css/chosen.min.css" />

}

<style>



    body {
        background-color: black;
    }

    .bar_music {
        background-color: gray;
        color: #ffffff;
        font-size: 10px;
        font-weight: bold;
        padding: 10px;
        margin: 10px;
        position: relative;
    }

        .bar_music:hover {
            background-color: black
        }

        .bar_music input {
            position: absolute;
            right: 10px;
            top: 10px;
        }


    .bar_track {
        background-color: rgb(71, 69, 69);
        color: #ffffff;
        font-size: 10px;
        font-weight: bold;
        padding: 10px;
        margin: 10px;
        position: relative;
    }

        .bar_track:hover {
            background-color: black
        }

        .bar_track .track_title {
            position: absolute;
            left: 15vw;
            top: 10px;
        }

        .bar_track .artist_name {
            position: absolute;
            left: 50vw;
            top: 10px;
        }

        .bar_track input[value="Delete"] {
            position: absolute;
            right: 10px;
            top: 10px;
        }

    #add_entity {
        position: relative;
        padding: 50px;
    }


        #add_entity .close-button {
            position: absolute;
            top: 0;
            right: 50px;
            cursor: pointer;
            background-color: black;
            border: solid;
            color: white;
        }

    #lbl_entity {
        color: white;
    }


    .btn1 {
        color: white;
        background-color: black;
        border-radius: 3px;
        border-color: white;
        padding-top: 11px;
        padding-bottom: 10px;
        padding-left: 20px;
        padding-right: 20px;
    }



    .profile_container {
        width: 800px;
    }

    .profile_box {
        height: 280px;
        display: inline-block;
        text-align: center;
        margin: 14px;
    }

        .profile_box .cover_art_with_vinyls {
            cursor: pointer;
            transition: all 0.5s ease;
        }

            .profile_box .cover_art_with_vinyls:hover {
                transform: scale(1.1);
            }

            .profile_box .cover_art_with_vinyls img {
                height: 200px;
            }



        .profile_box .entity_name {
            width: 150px;
            text-align: center;
            color: white;
            margin-bottom: 3px;
            display: block;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
        }

    .infobox {
        margin-top: 15px;
        /*margin-left: 50px;*/
        /* height: 60px;*/
        min-height: 60px;
        background-color: black;
        color: white;
        border: solid white;
        border-width: 1px;
    }

        .infobox label {
            width: 100%;
            height: 10px;
            font-size: x-small;
            font-size: x-small;
            display: block;
        }



        .infobox input {
            width: 90%;
            font-size: large;
            background-color: black;
            color: white;
            border: none;
        }

        .infobox .input_list {
            border: solid;
            border-color: #423a3a;
            margin-bottom: 2px;
        }

        .infobox textarea {
            font-size: large;
            background-color: black;
            color: white;
            border: none;
            width: 700px;
            height: 140px;
        }


    .button_container {
        /*margin-left: 50px;*/
        margin-top: 20px;
    }

    #pic_vertical {
        width: 180px;
        height: 180px;
        position: relative;
        border: solid;
        border-color: white;
        color: white;
        /*margin-left: 50px;*/
        vertical-align: central;
        display: inline-block;
        background-size: 100% 100%;
    }

        #pic_vertical:hover {
            background-color: #5d6952;
        }

    #pic_horizontal {
        width: 200px;
        height: 130px;
        position: relative;
        border: solid;
        border-color: white;
        color: white;
        display: inline-block;
        background-size: 100% 100%;
    }

        #pic_horizontal:hover {
            background-color: #5d6952;
        }




    .plus {
        font-size: 73px;
        text-align: center;
        margin: auto;
        color: white;
        font-weight: 900;
    }


    .add_another {
        display: inline;
        text-align: right;
        color: white;
        position: absolute;
        right: 35px;
        font-size: x-small;
    }

        .add_another:hover {
            font-weight: 900;
        }

    .select_related {
        background-color: black;
        color: white;
        margin: 3px;
        position: relative;
    }
</style>


<h1>EditMusic</h1>



<div class="bar_music" onclick="location.href='@Url.Action("EditMusicEditMusic", "AdminMusic", new { music_id = Model.Music.Id })'">
    <div>@Model.Music.Title</div>
    <input type="button" value="Delete" onclick="event.stopPropagation();confirmMusicDelete('@Model.Music.Id');">
</div>

@if (Model.state == "edit_music")
{
    <form method="post" id="form_music" enctype="multipart/form-data">


        @*<input type="hidden" name="track1Id" value="@Model.Track1.Id" />
            <input type="hidden" name="track2Id" value="@Model.Track2.Id" />*@


        <div>
            <input type="radio" @(Model.Music.MusicCollectionType == MusicCollectionType.maxi_single ? "checked=\"checked\"" : "") value="@stockhom_soundrom_mvc.Models.MusicCollectionType.maxi_single" id="maxi" name="coltype" /><label style="color:white">Maxi Single</label>
            <input type="radio" @(Model.Music.MusicCollectionType == MusicCollectionType.EP ? "checked=\"checked\"" : "") value="@stockhom_soundrom_mvc.Models.MusicCollectionType.EP" id="ep" name="coltype" /><label style="color:white">EP</label>
            <input type="radio" @(Model.Music.MusicCollectionType == MusicCollectionType.album ? "checked=\"checked\"" : "") value="@stockhom_soundrom_mvc.Models.MusicCollectionType.album" id="albun" name="coltype" /><label style="color:white">Album</label>
        </div>


        <div class="infobox">
            <label>Maxi single / EP / Album title</label>
            @*<input type="text" id="meta_title" value="@Model.NewArtist.MetaTitle" />*@
            @Html.TextBoxFor(m => m.Music.Title)
        </div>

        <div class="infobox" style="width: 300px; display: inline-block">
            <label>Publish Date</label>
            @*<input type="date" value="@Model.NewArtist.PublishedDate" id="published_date" style="color-scheme: dark;" />*@
            @Html.TextBoxFor(m => m.Music.PublishDate, new { type = "datetime-local", style = "color-scheme: dark;" })

        </div>

        <div style="display:inline-block;margin-top:5px">
            <div>
                @Html.CheckBoxFor(m => m.Music.StartCountDown)<span style="color:white"> Start Count down clock </span>
            </div>
            <div>
                @Html.CheckBoxFor(m => m.Music.TopPromo)  <span style="color:white">Top Promo</span>
            </div>
        </div>

        <div style="display:block;margin-top:15px">
            <div id="pic_vertical" name="pic_vertical" style="background-image: url(@Url.Content("~/Uploads/"+Model.Music.ArtWorkSquare));" onclick="pic_vertical_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Artwork Square</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_vertical_upload' name="pic_vertical_upload" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_horizontal" name="pic_horizontal" style="background-image: url(@Url.Content("~/Uploads/"+Model.Music.ArtWorkHoriontal));" onclick="pic_horizontal_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Artwork Horizontal</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_horizontal_upload' name="pic_horizontal_upload" type='file' value="" style='display: none' accept='.jpg' />
        </div>

        <div class="infobox" id="infobox_buy_links">

            <label>Buy Link</label>

            @foreach (var buylink in Model.Music.BuyLinks)
            {
                <input value="@buylink.Link" name="input_text_buylink" />
            }
        </div>
        <label id="add_another_buy_link" class="add_another">Add another buy link</label>



        <div class="infobox">
            <label>Spotify Album Link</label>
            @Html.TextBoxFor(m => m.Music.SpotifyAlbumLink)
        </div>

        <div class="infobox">
            <label>Apple Album Link</label>
            @Html.TextBoxFor(m => m.Music.AppleAlbumLink)
        </div>

        <div class="infobox">
            <label>Soundclould Album Link</label>
            @Html.TextBoxFor(m => m.Music.SoundCloudAlbumLink)
        </div>

        <div class="infobox">
            <label>Youtube Music Album Link</label>
            @Html.TextBoxFor(m => m.Music.YoutubeMusicAlbumLink)
        </div>

        <div class="infobox">
            <label>Youtube Album Link</label>
            @Html.TextBoxFor(m => m.Music.YoutubeAlbumLink)
        </div>

        <div class="infobox" style="height: 160px">
            <label>Description</label>
            @Html.TextAreaFor(m => m.Music.Description)
        </div>

        <div class="infobox">
            <label>Meta Title</label>
            @Html.TextBoxFor(m => m.Music.MetaTitle)
        </div>

        <div class="infobox">
            <label>Meta Description</label>
            @Html.TextBoxFor(m => m.Music.MetaDescription)
        </div>

        <div class="button_container">
            <input type="submit" formaction="@Url.Action("EditMusicEditMusicPublish","AdminMusic",new { MusicId=Model.Music.Id })" value="PUBLISH MAXI SINGLE/ALBUM/EP" class="btn1" />
            <input type="submit" formaction="@Url.Action("EditMusicEditMusicSave","AdminMusic",new { MusicId=Model.Music.Id })" name="save" id="btn_save" value="SAVE" class="btn1" />
            <input type="submit" onclick="open_preview(true,@Model.Music.Id);" value="PREVIEW" class="btn1" />

        </div>


    </form>

}




@{int loopvar = 0; }


@foreach (var track in Model.Music.Tracks)
{

    var relatedArtistName = (track.RelatedArtists.Count > 0) ? track.RelatedArtists[0].ArtistName : "";



    int track_no = loopvar + 1;
    <div id="@track.Id" draggable="true" ondrop="drop(event)" ondragover="allowDrop(event)" ondragstart="drag(event)" class="bar_track" onclick="location.href='@Url.Action("EditMusicEditTrack", "AdminMusic", new { music_id = Model.Music.Id, track_id = track.Id })'">
        <div id="@track.Id">Track @track_no</div>
        <div id="@track.Id" class="track_title">@track.TrackTitle</div>
        <div id="@track.Id" class="artist_name">@relatedArtistName</div>
        <input id="@track.Id" type="button" value="Delete" onclick="event.stopPropagation(); confirmTrackDelete('@track.Id','@Model.Music.Id');">
    </div>
    @if (Model.state == "edit_track" && track.Id == Model.editTrackId)
    {



        <form id="form_track" method="post" enctype="multipart/form-data">



            <input type="button" id="btn_change_track" value="Change Track" />

            <div style="color:white">

                <input type="file" style="display:none" name="track_file" id="track_file" accept=".mp3,.wav" />

            </div>



            <div class="infobox">
                <label>Track Title</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].TrackTitle)
            </div>

            <div class="infobox" id="infobox_related_artists">
                <label>Artist Name</label>

                @foreach (var style in Model.Music.Tracks[loopvar].RelatedArtists)
                {
                    <select class="select_related" name=@Global.RelatedArtistsDDLName>
                        @foreach (var option in Model.AllArtists)
                        {
                            if (style.Id == option.Id)
                            {
                                @Html.Raw($"<option selected value='{option.Id}'>{option.ArtistName}</option>")
                            }
                            else
                            {
                                @Html.Raw($"<option value='{option.Id}'>{option.ArtistName}</option>")
                            }
                        }

                    </select>
                }


                <select style="display:none" class="hidden_select">
                    @foreach (var option in Model.AllArtists)
                    {
                        @Html.Raw($"<option value='{option.Id}'>{option.ArtistName}</option>")
                    }

                </select>


            </div>
            <label id="add_another_related_artist" class="add_another">Add Another Artist</label>


            <div class="infobox">
                <label>Catagory Number</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].CatalogNumber)
            </div>

            <div class="infobox">
                <label>ISRC</label>
                @*<input type="text" id="meta_title" value="@Model.NewArtist.MetaTitle" />*@
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].ISRC)
            </div>

            <div class="infobox">
                <label>Main Genre</label>
                @*<input type="text" id="meta_description" value="@Model.NewArtist.MetaDescription" />*@
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].MainGenre)
            </div>

            <div class="infobox">
                <label>Sub-genre</label>
                @*<input type="text" id="meta_description" value="@Model.NewArtist.MetaDescription" />*@
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].SubGenre)
            </div>

            <div class="infobox">
                <label>BPM</label>
                @*<input type="text" id="meta_description" value="@Model.NewArtist.MetaDescription" />*@
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].BPM)
            </div>

            <div class="infobox" style="width: 300px">
                <label>Publish Date</label>
                @*<input type="date" value="@Model.NewArtist.PublishedDate" id="published_date" style="color-scheme: dark;" />*@
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].PublishDate, new { type = "datetime-local", style = "color-scheme: dark;" })

                @{ selected_track_publish_date = Model.Music.Tracks[loopvar].PublishDate;
                    datetime_element_id = $"Music_Tracks_{loopvar}__PublishDate"; }
            </div>

            <input type="hidden" name="loop_var" value="@loopvar" />
            <input type="hidden" name="track_id_" value="@Model.Music.Tracks[loopvar].Id" />



            <div id="pic_vertical" style="margin-top: 15px;background-image: url( @Url.Content("~/Uploads/"+Model.Music.Tracks[loopvar].ArtWorkSquare) )" onclick="pic_vertical_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Artwork Square</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_vertical_upload' name="pic_vertical_upload" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_horizontal" style="background-image: url(@Url.Content("~/Uploads/"+Model.Music.Tracks[loopvar].ArtWorkHoriontal))" onclick="pic_horizontal_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Artwork Horizontal</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_horizontal_upload' name="pic_horizontal_upload" type='file' value="" style='display: none' accept='.jpg' />

            <div style="display:inline-block">
                <div>
                    @Html.CheckBoxFor(m => m.Music.Tracks[loopvar].StartCountDown) <span style="color:white">Start count down clock </span>
                </div>

                <div>
                    @Html.CheckBoxFor(m => m.Music.Tracks[loopvar].TopPromo) <span style="color:white">Top promo</span>
                </div>
            </div>

            <div class="infobox" id="infobox_buy_links">

                <label>Buy Link</label>


                @foreach (var buyLink in Model.Music.Tracks[loopvar].BuyLinks)
                {
                    <input type="text" value="@buyLink.Link" name="@Global.BuyLinkTXTName" />
                }



            </div>
            <label id="add_another_buy_link" class="add_another">Add another buy link</label>



            <div class="infobox">
                <label>Spotify Stream Link</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].SpotifyLink)
            </div>

            <div class="infobox">
                <label>Apple Stream Link</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].AppleLink)
            </div>

            <div class="infobox">
                <label>Soundclould Stream Link</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].SoundCloudLink)
            </div>

            <div class="infobox">
                <label>Youtube Music Stream Link</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].YoutubeMusicStreamLink)
            </div>


            <span style="color:white">Offer free download for this track</span> @Html.CheckBoxFor(m => m.Music.Tracks[loopvar].OfferFreeDownload)


            <div class="infobox">
                <label>Youtube Video Link</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].YoutubeLink)
            </div>

            <div class="infobox">
                <label>Meta Title</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].MetaTitle)
            </div>

            <div class="infobox">
                <label>Meta Description</label>
                @Html.TextBoxFor(m => m.Music.Tracks[loopvar].MetaDescription)
            </div>

            <div class="infobox" style="height: 160px">
                <label>Description</label>
                @Html.TextAreaFor(m => m.Music.Tracks[loopvar].Description)
            </div>


            <div class="infobox" id="infobox_related_videos">

                <label>Related Video</label>


                @foreach (var video in Model.Music.Tracks[loopvar].RelatedVideos)
                {
                    <select class="select_related" name=@Global.RelatedVideosDDLName>
                        @foreach (var option in Model.AllVideos)
                        {
                            if (video.Id == option.Id)
                            {
                                @Html.Raw($"<option selected value='{option.Id}'>{option.VideoTitle}</option>")
                            }
                            else
                            {
                                @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                            }
                        }

                    </select>
                }


                <select style="display:none" class="hidden_select">
                    @foreach (var option in Model.AllVideos)
                    {
                        @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                    }
                </select>


            </div>
            <label id="add_another_related_video" class="add_another">Add another related video</label>



            <div class="infobox" id="infobox_related_styles">
                <label>Related Style</label>

                @foreach (var style in Model.Music.Tracks[loopvar].RelatedStyles)
                {
                    <select class="select_related" name=@Global.RelatedStylesDDLName>
                        @foreach (var option in Model.AllStyles)
                        {
                            if (style.Id == option.Id)
                            {
                                @Html.Raw($"<option selected value='{option.Id}'>{option.StyleTitle}</option>")
                            }
                            else
                            {
                                @Html.Raw($"<option value='{option.Id}'>{option.StyleTitle}</option>")
                            }
                        }

                    </select>
                }


                <select style="display:none" class="hidden_select">
                    @foreach (var option in Model.AllStyles)
                    {
                        @Html.Raw($"<option value='{option.Id}'>{option.StyleTitle}</option>")
                    }

                </select>


            </div>
            <label id="add_another_related_styles" class="add_another">Add another related style</label>




            <div class="infobox" id="infobox_related_journal_entry">
                <label>Related Journal Entries</label>

                @foreach (var style in Model.Music.Tracks[loopvar].RelatedJournals)
                {
                    <select class="select_related" name=@Global.RelatedJournalsDDLName>
                        @foreach (var option in Model.AllJournals)
                        {
                            if (style.Id == option.Id)
                            {
                                @Html.Raw($"<option selected value='{option.Id}'>{option.Headline}</option>")
                            }
                            else

                                @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                            }
                        }

                    </select>
                }


                <select style="display:none" class="hidden_select">
                    @foreach (var option in Model.AllJournals)
                    {
                        @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                    }

                </select>


            </div>
            <label id="add_another_related_journal_entry" class="add_another">Add another related Journal Entry</label>







            <div class="button_container">
                <input type="submit" formaction="@Url.Action("EditMusicEditTrackPublish","AdminMusic")" value="PUBLISH TRACK" class="btn1" />
                <input type="submit" formaction="@Url.Action("EditMusicEditTrackSave","AdminMusic")" name="save" id="btn_save" value="SAVE" class="btn1" />
                <input type="submit" id="preview_button_track" onclick="open_preview(false, @Model.Music.Tracks[loopvar].Id);" value="PREVIEW" class="btn1" />

            </div>

        </form>

    }

    loopvar++;
}


<script src="~/js/chosen.jquery.min.js"></script>


<script>

    var drag_elem_id = "";
    var drag_elem_name = "";
    var drop_elem_id = "";
    var drop_elem_name = "";
    var drag_elem_tagname = "";
    var drop_elem_tagname = "";
    var drag_elem = null;
    var drop_elem = null;
    var m_id = "";



    function allowDrop(ev) {

        ev.preventDefault();
    }

    function drag(ev) {

        //drag_elem_name = ev.target.name;
        //drag_elem_tagname = ev.target.tagName;
        drag_elem = ev.target;
        drag_elem_id = ev.target.id;


    }

    function drop(ev) {
        ev.preventDefault();
        //drop_elem_name = ev.target.name;
        //drop_elem_tagname = ev.target.tagName;
        drop_elem = ev.target
        drop_elem_id = ev.target.id;


        m_id = @Model.Music.Id;
        var redirectUrl = "/AdminMusic/ReOrderTracks?dragid=" + drag_elem_id + "&dropid=" + drop_elem_id + "&music_id=" + m_id;




        location.href = redirectUrl;



        ////only a track profile can be drop into another track or a music profile
        //if (drag_elem_name == 'track' && drop_elem_name == "music") {

        //    var redirectUrl = "/AdminMusic/AddTrackToMusic?trackId=" + drag_elem_id + "&musicId=" + drop_elem_id;


        //    console.log("redirectUrl: " + redirectUrl);

        //    location.href = redirectUrl;
        //}
        //else if (drag_elem_name == 'track' && drop_elem_name == "track") {


        //    var redirectUrl = "/AdminMusic/Combine2Tracks?track1Id=" + drag_elem_id + "&track2Id=" + drop_elem_id;


        //    console.log("redirectUrl: " + redirectUrl);

        //    location.href = redirectUrl;

        //}
        //else {

        //    //do nothing.
        //}

    }


</script>


<script>


    function confirmMusicDelete(id) {
        if (confirm("Are you sure you want to delete this?")) {

            window.location.href = "/AdminMusic/DeleteMusic?music_id=" + id;
        }
    }


    function confirmTrackDelete(track_id, music_id) {
        if (confirm("Are you sure you want to delete this?")) {
            window.location.href = "/AdminMusic/DeleteTrack?track_id=" + track_id + "&music_id=" + music_id;
        }
    }

    document.getElementById("add_another_buy_link").addEventListener("click", function () {


        let input = document.createElement("input");
        input.type = "text";
        input.name = "input_text_buylink";
        input.style.backgroundColor = "white";

        document.getElementById("infobox_buy_links").appendChild(input);
    });


    function ver_image_upload() {

        var fileInput = document.getElementById('pic_vertical_upload');
        var file = fileInput.files[0];
        _ver_image = file;
        var fileUrl = URL.createObjectURL(file);

        var div = document.getElementById('pic_vertical');
        div.style.backgroundImage = 'url(' + fileUrl + ')';
    }

    function hor_image_upload() {

        var fileInput = document.getElementById('pic_horizontal_upload');
        var file = fileInput.files[0];
        _hor_image = file;
        var fileUrl = URL.createObjectURL(file);

        var div = document.getElementById('pic_horizontal');
        div.style.backgroundImage = 'url(' + fileUrl + ')';

    }

    document.getElementById("pic_horizontal_upload").addEventListener("change", hor_image_upload, false);
    document.getElementById("pic_vertical_upload").addEventListener("change", ver_image_upload, false);





</script>


<script type="text/javascript">

    var mouse_focused_related_select_element_id = null;

    window.onload = function () {


        $('select:not(.hidden_select)').each(function () {

            var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
            var uniqid = randLetter + Date.now();


            $(this).attr('id', uniqid);

            $(this).chosen();

            var generated_element_id = uniqid + "_chosen";

            //$(this).on('mouseover', function () {
            //    mouse_focused_related_select_element_id = $(this).attr('id');
            //    console.log('mouse over');
            //    console.log(mouseFocusedRelatedSelectElementId);
            //});


            //$(this).on("mouseout", function () {

            //    mouse_focused_related_select_element_id = null;
            //    console.log("mouse out");
            //});

            document.getElementById(generated_element_id).addEventListener("mouseover", function () {

                mouse_focused_related_select_element_id = generated_element_id;
                console.log("mouse over");
                console.log(mouse_focused_related_select_element_id);

            });


            document.getElementById(generated_element_id).addEventListener("mouseout", function () {

                mouse_focused_related_select_element_id = null;
                console.log("mouse out");
            });


        });
    }

</script>


<script>

    var _hor_image = null;
    var _ver_image = null;

    var test1 = [];
    var videoss = "";
    var e = "";





    var LastRelatedTrackSelectElementPostFix = 0;
    var LastRelatedVidoeSelectElementPostFix = 0;
    var LastRelatedStyleSelectElementPostFix = 0;
    var LastRelatedJournalSelectElementPostFix = 0;


    document.getElementById('btn_change_track').addEventListener('click', function () {

        document.getElementById('track_file').style.display = 'inline';

    });

    document.onkeyup = function (e) {
        e = e || window.event;
        if (e.keyCode == "8") {


            if (mouse_focused_related_select_element_id != null) {
                document.getElementById(mouse_focused_related_select_element_id).remove();
                document.getElementById(mouse_focused_related_select_element_id.slice(0, -7)).remove();
            }

        }
    };



    function f3(infoboxElementId, selectElementName, clickingElementId) { //"clickingElement" could be a button

        document.getElementById(clickingElementId).addEventListener("click", function () {

            var select = document.createElement("select");
            select.name = selectElementName;
            var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
            var uniqid = randLetter + Date.now();
            select.id = uniqid;
            select.style.display = "inline";





            var hiddenselect = document.getElementById(infoboxElementId).querySelector(".hidden_select");
            for (var i = 0; i < hiddenselect.options.length; i++) {
                var option = hiddenselect.options[i];
                select.add(new Option(option.text, option.value));
            }

            document.getElementById(infoboxElementId).appendChild(select);

            $("#" + uniqid).chosen();


            //id of the generated element for the hidden select element

            var generated_element_id = uniqid + "_chosen";

            var generated_element = document.getElementById(generated_element_id);


            generated_element.addEventListener("mouseover", function () {

                mouse_focused_related_select_element_id = generated_element.id;
                console.log("mouse over");
                console.log(mouse_focused_related_select_element_id);

            });


            generated_element.addEventListener("mouseout", function () {

                mouse_focused_related_select_element_id = null;
                console.log("mouse out");
            });


        });



    }

    f3("infobox_related_videos", "select_related_videos", "add_another_related_video");
    f3("infobox_related_styles", "select_related_styles", "add_another_related_styles");
    f3("infobox_related_journal_entry", "select_related_journal_entry", "add_another_related_journal_entry");
    f3("infobox_related_artists", "select_related_artists", "add_another_related_artist");



    document.getElementById("add_another_buy_link").addEventListener("click", function () {


        let input = document.createElement("input");
        input.type = "text";
        input.name = "input_text_buylink";
        input.style.border = "solid";
        input.style.borderColor = "white";
        input.style.borderWidth = "1px";

        document.getElementById("infobox_buy_links").appendChild(input);
    });





    //when we input an id of a .infobox
    //this returns a collection of objects
    //containing id and text properties
    function f2(infobox_id) {

        const infobox = document.getElementById(infobox_id);
        const selectElements = infobox.querySelectorAll('select');


        let retobjs = [];

        for (const select of selectElements) {
            const selectedIndex = select.selectedIndex;
            const selectedOption = select.options[selectedIndex];
            const selectedText = selectedOption.text;
            const selectedValue = selectedOption.value;

            var obj = Object.assign({}, { id: selectedValue, text: selectedText });
            retobjs.push(obj);

        }

        return retobjs;

    }







    document.getElementById("pic_horizontal_upload").addEventListener("change", hor_image_upload, false);
    document.getElementById("pic_vertical_upload").addEventListener("change", ver_image_upload, false);
    /*document.getElementById("btn_save").addEventListener("click", btn_save_click);*/
    //document.getElementById("btn_add_artist").addEventListener("click", function () {

    //    document.getElementById("add_artist").style.display = "block";

    //});

    document.getElementById("add_entity_close_btn").addEventListener("click", function () {


        document.getElementById("add_entity").style.display = "none";

        //make element vlaues defualt

    });



    function refresh_profile_container() {

        //clear the inside of profile_container
        //get profile data from backend using ajax
    }



    function ver_image_upload() {

        var fileInput = document.getElementById('pic_vertical_upload');
        var file = fileInput.files[0];
        _ver_image = file;
        var fileUrl = URL.createObjectURL(file);

        var div = document.getElementById('pic_vertical');
        div.style.backgroundImage = 'url(' + fileUrl + ')';
    }

    function hor_image_upload() {

        var fileInput = document.getElementById('pic_horizontal_upload');
        var file = fileInput.files[0];
        _hor_image = file;
        var fileUrl = URL.createObjectURL(file);

        var div = document.getElementById('pic_horizontal');
        div.style.backgroundImage = 'url(' + fileUrl + ')';

    }

    function SaveImageAndReturnGuid(file) {

        var guid = null;


        var formData = new FormData();
        formData.append("file", file);

        $.ajax({
            type: "POST",
            url: "SaveImage.ashx",
            data: formData,
            contentType: false,
            processData: false,
            async: false,
            success: function (response) {
                guid = response;
            },
            error: function (ex) {
                test1 = ex;
            }
        });


        return guid;
    }


</script>

@section Scripts{

    <script type="text/javascript" src="/js/EditMusic.js"></script>

    <script>

    var publishDate = '@Model.Music.PublishDate.ToString("yyyy-MM-ddTHH:mm:ss")';
        document.getElementById('Music_PublishDate').value = publishDate;

    </script>


    <script>

        var publishDateTrack = '@selected_track_publish_date.ToString("yyyy-MM-ddTHH:mm:ss")';
        document.getElementById('@datetime_element_id').value = publishDateTrack;

    </script>
}




