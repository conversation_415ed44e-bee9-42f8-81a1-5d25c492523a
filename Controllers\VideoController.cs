﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Controllers
{
    public class VideoController : Controller
    {

        public const string RelatedArtistsSelectElementsIdentifire = "select_related_artists";
        public const string RelatedTracksselectElementsIdentifire = "select_related_tracks";
        public const string RelatedStylesSelectElementsIdentifire = "select_related_styles";
        public const string RelatedJournalsSelectElementsIdentifire = "select_related_journal_entry";

        private readonly IWebHostEnvironment _env;

        public VideoController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public IActionResult Index(int id)
        {
            VideoViewModel videoViewModel = new VideoViewModel();

            using (Data data = new Data())
            {

                videoViewModel.Video = data.Videos
                    .Where(x => x.Id == id)
                    .Include(x => x.RelatedJournals)
                    .Include(x => x.RelatedArtists)
                    .Include(x => x.RelatedStyle)
                    .First();



                return View(videoViewModel);


            }




        }

        [HttpPost]
        public ActionResult Preview(AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            VideoViewModel videoViewModel = new VideoViewModel();



            using (Data data = new Data())
            {
                var video = GenerateVideoFromAdminVideosViewModel(data, adminVideosViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);

                if (video.PublishDate == DateTime.MinValue) // means the publish day is not set
                {
                    video.PublishDate = DateTime.Now;
                }

                if (video.PublishDate >= DateTime.Now)
                {
                    video.State = State.ToBePublished;
                }
                else
                {
                    video.State = State.Published;
                }

                video.Id = 100000000;

                videoViewModel.Video = video;



                return View("Index", videoViewModel);


            }

        }


        Video GenerateVideoFromAdminVideosViewModel(Data data, AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, State state)
        {
            try
            {
                Video video = new Video();

                //control bounded data
                video.VideoTitle = adminVideosViewModel.Video.VideoTitle;
                video.Description = adminVideosViewModel.Video.Description;
                video.MetaTitle = adminVideosViewModel.Video.MetaTitle;
                video.MetaDescription = adminVideosViewModel.Video.MetaDescription;
                video.PublishDate = adminVideosViewModel.Video.PublishDate;
                video.YoutubeLink = adminVideosViewModel.Video.YoutubeLink;
                video.State = state;


                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    video.VerticalPosterUrl = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    video.HorizontalPosterLinkUrl = saveIFromFile(pic_horizontal_upload);
                }




                //request form data
                var relatedTracks = Request.Form[RelatedTracksselectElementsIdentifire];
                foreach (var relatedTrackIdString in relatedTracks)
                {
                    int trackId = int.Parse(relatedTrackIdString);
                    var relatedTrack = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                    video.RelatedTracks.Add(relatedTrack);

                }

                var relatedArtists = Request.Form[RelatedArtistsSelectElementsIdentifire];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.Where(t => t.Id == artistId).FirstOrDefault();
                    video.RelatedArtists.Add(relatedArtist);

                }

                var relatedStyles = Request.Form[RelatedStylesSelectElementsIdentifire];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    video.RelatedStyle.Add(relatedStyle);

                }

                var relatedJournals = Request.Form[RelatedJournalsSelectElementsIdentifire];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    video.RelatedJournals.Add(relatedJournal);

                }

                return video;

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }
    }
}
