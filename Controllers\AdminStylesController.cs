﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using stockhom_soundrom_mvc.ViewModels;
using stockhom_soundrom_mvc.Models;
using Microsoft.AspNetCore.Http;
using System.IO;
using Microsoft.AspNetCore.Hosting;
using System.Data.Entity;

namespace stockhom_soundrom_mvc.Controllers
{
    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminStylesController : Controller
    {
        private readonly IWebHostEnvironment _env;

        public AdminStylesController(IWebHostEnvironment env)
        {
            _env = env;
        }


        public IActionResult Index()
        {
            return View(new AdminStylesViewModel());
        }

        public IActionResult NewAddSection()
        {
            AdminStylesViewModel adminArtistViewModel = new AdminStylesViewModel();

            using (Data data = new Data())
            {
                adminArtistViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminArtistViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminArtistViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminArtistViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminArtistViewModel.Style = new Style();
                adminArtistViewModel.AddSectionVisible = true;
            }

            return View("index", adminArtistViewModel);
        }



        [HttpPost]
        public ActionResult Save(AdminStylesViewModel adminStylesViewModel, IFormFile picture1, IFormFile picture2, IFormFile picture3)
        {

            //do data validation
            using (var data = new Data())
            {
                var style = GenerateStyleFromAdminStyleViewModel(data, adminStylesViewModel, picture1, picture2, picture3, State.Saved);
                data.Styles.Add(style);
                data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));

        }

        [HttpPost]
        public ActionResult EditSave(int style_id, AdminStylesViewModel adminStylesViewModel, IFormFile picture1, IFormFile picture2, IFormFile picture3)
        {
            //do data validation



            using (Data data = new Data())
            {
                var style = GenerateStyleFromAdminStyleViewModel(data, adminStylesViewModel, picture1, picture2, picture3, State.Saved);

                var existingStyle = data.Styles.Where(a => a.Id == style_id)
                    .Include(t => t.Tracks)
                    .Include(t => t.Journals)
                    .Include(t => t.Artists)
                    .Include(t => t.Videos)
                    .Include(t => t.Colors)
                    .Include(t => t.Catagories)
                    .Include(t => t.Sizes)
                    .Single();

                if (string.IsNullOrEmpty(style.Picture1))
                {
                    style.Picture1 = existingStyle.Picture1;
                }
                if (string.IsNullOrEmpty(style.Picture2))
                {
                    style.Picture2 = existingStyle.Picture2;
                }
                if (string.IsNullOrEmpty(style.Picture3))
                {
                    style.Picture3 = existingStyle.Picture3;
                }


                //all the nesated objects that has to be removed
                existingStyle.Tracks.Clear();
                existingStyle.Videos.Clear();
                existingStyle.Artists.Clear();
                existingStyle.Journals.Clear();
                existingStyle.Colors.Clear();
                existingStyle.Sizes.Clear();
                existingStyle.Catagories.Clear();

                data.SaveChanges();

                existingStyle.Artists = style.Artists;
                existingStyle.Catagories = style.Catagories;
                existingStyle.Colors = style.Colors;
                existingStyle.Description = style.Description;
                existingStyle.ExternalLink = style.ExternalLink;
                existingStyle.FanPrice = style.FanPrice;
                existingStyle.Journals = style.Journals;
                existingStyle.Picture1 = style.Picture1;
                existingStyle.Picture2 = style.Picture2;
                existingStyle.Picture3 = style.Picture3;
                existingStyle.Price = style.Price;
                existingStyle.ReleaseDate = style.ReleaseDate;
                existingStyle.Sizes = style.Sizes;
                existingStyle.State = State.Saved;
                existingStyle.StyleTitle = style.StyleTitle;
                existingStyle.Tracks = style.Tracks;
                existingStyle.Videos = style.Videos;

                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }

        [HttpPost]
        public ActionResult Publish(AdminStylesViewModel adminStylesViewModel, IFormFile picture1, IFormFile picture2, IFormFile picture3)
        {
            //do data validation



            using (Data data = new Data())
            {
                var style = GenerateStyleFromAdminStyleViewModel(data, adminStylesViewModel, picture1, picture2, picture3, State.Published);

                if (style.ReleaseDate == DateTime.MinValue) // means the publish day is not set
                {
                    style.ReleaseDate = DateTime.Now;
                }

                if (style.ReleaseDate >= DateTime.Now)
                {
                    style.State = State.ToBePublished;
                }
                else
                {
                    style.State = State.Published;
                }

                data.Styles.Add(style);
                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }


        public ActionResult EditPublish(int style_id, AdminStylesViewModel adminStylesViewModel, IFormFile picture1, IFormFile picture2, IFormFile picture3)
        {
            //do data validation



            using (Data data = new Data())
            {
                var style = GenerateStyleFromAdminStyleViewModel(data, adminStylesViewModel, picture1, picture2, picture3, State.Saved);

                var existingStyle = data.Styles.Where(a => a.Id == style_id)
                    .Include(t => t.Tracks)
                    .Include(t => t.Journals)
                    .Include(t => t.Artists)
                    .Include(t => t.Videos)
                    .Include(t => t.Colors)
                    .Include(t => t.Catagories)
                    .Include(t => t.Sizes)
                    .Single();

                if (string.IsNullOrEmpty(style.Picture1))
                {
                    style.Picture1 = existingStyle.Picture1;
                }
                if (string.IsNullOrEmpty(style.Picture2))
                {
                    style.Picture2 = existingStyle.Picture2;
                }
                if (string.IsNullOrEmpty(style.Picture3))
                {
                    style.Picture3 = existingStyle.Picture3;
                }


                //all the nesated objects that has to be removed
                existingStyle.Tracks.Clear();
                existingStyle.Videos.Clear();
                existingStyle.Artists.Clear();
                existingStyle.Journals.Clear();
                existingStyle.Colors.Clear();
                existingStyle.Sizes.Clear();
                existingStyle.Catagories.Clear();

                data.SaveChanges();

                existingStyle.Artists = style.Artists;
                existingStyle.Catagories = style.Catagories;
                existingStyle.Colors = style.Colors;
                existingStyle.Description = style.Description;
                existingStyle.ExternalLink = style.ExternalLink;
                existingStyle.FanPrice = style.FanPrice;
                existingStyle.Journals = style.Journals;
                existingStyle.Picture1 = style.Picture1;
                existingStyle.Picture2 = style.Picture2;
                existingStyle.Picture3 = style.Picture3;
                existingStyle.Price = style.Price;
                existingStyle.ReleaseDate = style.ReleaseDate;
                existingStyle.Sizes = style.Sizes;
                existingStyle.State = State.Published;
                existingStyle.StyleTitle = style.StyleTitle;
                existingStyle.Tracks = style.Tracks;
                existingStyle.Videos = style.Videos;

                data.SaveChanges();
            }

            return RedirectToAction("Index");




        }


        public ActionResult ViewForUpdate(AdminStylesViewModel adminStylesViewModel, int id)
        {
            using (Data data = new Data())
            {


                adminStylesViewModel.Style = data.Styles.Where(s => s.Id == id)
                    .Include(x => x.Videos)
                    .Include(x => x.Journals)
                    .Include(x => x.Artists)
                    .Include(x => x.Tracks)
                    .First();
                adminStylesViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminStylesViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminStylesViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminStylesViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminStylesViewModel.ViewForUpdate = true;
                adminStylesViewModel.AddSectionVisible = true;

                return View("index", adminStylesViewModel);
            }


        }

        public IActionResult DeleteStyle(int style_id)
        {
            using (Data data = new Data())
            {
                var styleToBeRemoved = data.Styles.Where(a => a.Id == style_id).Include(s => s.Sizes).Include(s => s.Colors).Include(s => s.Catagories).Single();
                styleToBeRemoved.Sizes.Clear();
                styleToBeRemoved.Colors.Clear();
                styleToBeRemoved.Catagories.Clear();
                data.Styles.Remove(styleToBeRemoved);
                data.SaveChanges();
            }

            return View("Index", new AdminStylesViewModel());
        }




        private Style GenerateStyleFromAdminStyleViewModel(Data data, AdminStylesViewModel adminStylesViewModel, IFormFile picture1, IFormFile picture2, IFormFile picture3, State state)
        {
            try
            {
                Style style = new Style();

                //control bounded data
                style.ExternalLink = adminStylesViewModel.Style.ExternalLink;
                style.ReleaseDate = adminStylesViewModel.Style.ReleaseDate;
                style.StyleTitle = adminStylesViewModel.Style.StyleTitle;
                style.State = state;
                style.Description = adminStylesViewModel.Style.Description;
                style.Price = adminStylesViewModel.Style.Price;
                style.FanPrice = adminStylesViewModel.Style.FanPrice;




                //3 images
                if (picture1 != null && picture1.Length > 0)
                {
                    style.Picture1 = saveIFromFile(picture1);
                }
                if (picture2 != null && picture2.Length > 0)
                {
                    style.Picture2 = saveIFromFile(picture2);
                }
                if (picture3 != null && picture3.Length > 0)
                {
                    style.Picture3 = saveIFromFile(picture3);
                }




                //request form data

                var colors = Request.Form["colors"];
                foreach (var colorString in colors)
                {
                    style.Colors.Add(new Color() { ColorString = colorString });
                }

                var sizes = Request.Form["sizes"];
                foreach (var sizesString in sizes)
                {
                    style.Sizes.Add(new Size() { SizeString = sizesString });
                }

                var catagories = Request.Form["catagories"];
                foreach (var catagoryString in catagories)
                {
                    style.Catagories.Add(new Catagory() { CatagoryString = catagoryString });
                }




                var relatedTracks = Request.Form[Global.RelatedTracksDDLName];
                foreach (var relatedTrackIdString in relatedTracks)
                {
                    int trackId = int.Parse(relatedTrackIdString);
                    var relatedTrack = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                    style.Tracks.Add(relatedTrack);

                }

                var relatedVideos = Request.Form[Global.RelatedVideosDDLName];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    style.Videos.Add(relatedVideo);

                }



                var relatedJournals = Request.Form[Global.RelatedJournalsDDLName];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    style.Journals.Add(relatedJournal);

                }

                return style;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }

    }
}
