﻿using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Data.Entity;

namespace stockhom_soundrom_mvc.Controllers
{

    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminSupportController : Controller
    {
        public IActionResult Index()
        {
            using(Data data = new Data())
            {
                var support = data.Supports.FirstOrDefault();

                if(support!=null)
                {
                    return View(new AdminSupportViewModel() { Headline = support.Headline, BodyText = support.BodyText });
                }
                else
                {
                    return View(new AdminSupportViewModel());
                }

            }


           
        }


        public IActionResult Update(AdminSupportViewModel model)
        {
            if (!string.IsNullOrEmpty(model.BodyText) && !string.IsNullOrEmpty(model.Headline))
            {
                using (Data data = new Data())
                {
                    var existing = data.Supports.ToList();
                    foreach (var exis in existing)
                    {
                        data.Supports.Remove(exis);
                    }
                    data.Supports.Add(new Support() { Headline = model.Headline, BodyText = model.BodyText });
                    data.SaveChanges();
                }

            }

            return View("Index");
        }

    }
}
