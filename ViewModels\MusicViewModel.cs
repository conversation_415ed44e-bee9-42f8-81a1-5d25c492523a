﻿using stockhom_soundrom_mvc.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.ViewModels
{
    public class MusicViewModel
    {
        public Music Music { get; set; }
        public List<Track> TracksOrdred { get; set; } = new List<Track>();
        public List<Style> RelatedStyles { get; set; } = new List<Style>();
        public List<Journal> RelatedJournals { get; set; } = new List<Journal>();

    }
}
