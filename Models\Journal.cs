﻿using System;
using System.Collections.Generic;

namespace stockhom_soundrom_mvc.Models
{
    public class Journal
    {
        public int Id { get; set; }
        public string DesktopImageUrl { get; set; }
        public string MobileImageUrl { get; set; }
        public string Headline { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public string BodyText { get; set; }
        public DateTime PublishDate { get; set; }
        public bool StartPageMaterial { get; set; }
        public List<Track> RelatedTracks { get; set; } = new List<Track>();
        public List<Style> RelatedStyle { get; set; } = new List<Style>();
        public List<Video> RelatedVideos { get; set; } = new List<Video>();
        public List<Artist> RelatedArtists { get; set; } = new List<Artist>();
        public string Description { get; set; }
        public State State { get; set; }
    }
}
