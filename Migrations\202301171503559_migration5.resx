﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>