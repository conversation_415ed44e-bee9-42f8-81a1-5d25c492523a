﻿@model stockhom_soundrom_mvc.ViewModels.AdminPartyViewModel
@using stockhom_soundrom_mvc.Controllers
@using System.IO
@using System.Linq;
@using System.Data.Entity;

@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}


@section Styles{


    <link rel="stylesheet" href="~/css/chosen.min.css" />

    <style>
        body {
            background-color: black;
        }

        form {
            position: relative;
        }

        #add_entity {
            position: relative;
            padding: 50px;
        }


            #add_entity .close-button {
                position: absolute;
                top: 0;
                right: 50px;
                cursor: pointer;
                background-color: black;
                border: solid;
                color: white;
            }

        #lbl_entity {
            color: white;
        }


        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }



        .profile_container {
            width: 800px;
        }

        .profile_box {
            width: 220px;
            height: 170px;
            display: inline-block;
            text-align: center;
            margin: 14px;
        }

            .profile_box img {
                height: 170px;
                width: 220px;
                cursor: pointer;
                transition: all 0.5s ease;
            }

                .profile_box img:hover {
                    transform: scale(1.1);
                }

            .profile_box .entity_name {
                width: 150px;
                text-align: center;
                color: white;
                margin-bottom: 3px;
                display: block;
            }

            .profile_box .state {
                border: solid;
                color: white;
                display: block;
            }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }


        .button_container {
            /*margin-left: 50px;*/
            margin-top: 20px;
        }






        .plus {
            font-size: 73px;
            text-align: center;
            margin: auto;
            color: white;
            font-weight: 900;
        }


        .add_another {
            display: inline;
            text-align: right;
            color: white;
            position: absolute;
            right: 35px;
            font-size: x-small;
        }

            .add_another:hover {
                color: blue;
            }

        .select_related {
            background-color: black;
            color: white;
            margin: 3px;
            position: relative;
        }

        /*.select_related .close-button {
        position: absolute;
        top: 0;
        right: 0;
        display: none;
        }

        .select_related:hover .close-button {
        display: block;
        }

        .close-button {*/
        /* style the close button, such as color, size, etc. */
        /*color:red;
        }*/


        .parent {
            z-index: 1;
            background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
        }

        .child {
            position: absolute;
            z-index: 1;
        }



        .remove {
            position: absolute;
            top: 0px;
            right: 0px;
            display: block;
            box-sizing: border-box;
            width: 20px;
            height: 20px;
            border-width: 3px;
            border-style: solid;
            border-color: red;
            border-radius: 100%;
            background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
            background-color: red;
            box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
            transition: all 0.3s ease;
        }


        .tag {
            padding: 5px;
            background: aliceblue;
            border: black 2px solid;
            border-radius: 0px 15px 15px 0px;
            display: inline-block;
            cursor: pointer;
            color: black;
        }

        #tagContainer {
        }

        #taginputText {
            border: none;
            background: none;
        }

        #container {
            background: white;
            padding: 10px;
        }


        .pic_square {
            width: 170px;
            height: 170px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
        }

        .pic_horizontal {
            width: 250px;
            height: 170px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
        }
    </style>



}


@{
    var add_artist_style = (Model.AddSectionVisible == true) ? "display:block" : "display:none";
}


<div class="container">
    <div style="/*margin-left: 50px; */ margin-bottom: 20px">
        <label id="lbl_entity">Party</label>
        <input type="button" onclick="location.href='@Url.Action("NewAddSection", "AdminParty")'" class="btn1" value="Add Party" id="btn_add_artist" />

    </div>

    <div style=@add_artist_style id="add_entity">
        <button id="add_entity_close_btn" class="close-button">X</button>

        <div style="/*margin-left: 50px; */ margin-bottom: 20px; font-size: large; color: white;">
            Party
        </div>

        <form id="form" method="post" enctype="multipart/form-data">

            <div class="infobox">
                <label>Top title</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Party.TopTitle)
            </div>

            <div class="infobox">
                <label>Main title</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Party.MainTitle)
            </div>

            <div class="infobox">
                <label>Sub title (italic)</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Party.SubTitle)
            </div>


            @Html.HiddenFor(m => m.Party.HorizontalPoster)
            @Html.HiddenFor(m => m.Party.SquarePoster)


            <div id="pic_1" class="pic_square" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Party.SquarePoster));</text> } }" onclick="picture1.click();">
                <label style="position: relative; top: 5px; left: 5px;">Poster</label>
                <div class="plus">+</div>
            </div>
            <input id='picture1' name="picture1" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_2" class="pic_horizontal" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Party.HorizontalPoster));</text> } }" onclick="picture2.click();">
                <label style="position: relative; top: 5px; left: 5px;">Poster</label>
                <div class="plus">+</div>
            </div>
            <input id='picture2' name="picture2" type='file' value="" style='display: none' accept='.jpg' />




            <div class="infobox" style="width: 300px">
                <label>Start Date</label>
                @*<input type="date" value="@Model.NewArtist.PublishedDate" id="published_date" style="color-scheme: dark;" />*@
                @Html.TextBoxFor(m => m.Party.StartDate, new { type = "datetime-local", style = "color-scheme: dark;" })

            </div>


            <div class="infobox">
                <label>Purchase Link</label>
                @Html.TextBoxFor(m => m.Party.PurchaseLink)
            </div>










            <div class="button_container">

                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditPublish","AdminParty" , new { party_id = Model.Party.Id})" value="Publish" class="btn1" />
                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Publish","AdminParty")" value="Publish" class="btn1" />
                }

                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditSave","AdminParty" , new { party_id = Model.Party.Id})" name="Save" id="btn_save" value="save" class="btn1" />

                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Save","AdminParty")" name="save" id="btn_save" value="Save" class="btn1" />

                }



                <input type="submit" id="preview_button" value="Previw" class="btn1" />

                @if (Model.Party != null)
                {
                    <input type="button" class="btn1" onclick="confirmDelete(@Model.Party.Id)" value="Delete" />
                }
                else
                {
                    <input type="button" class="btn1" value="Delete" />
                }

            </div>

        </form>


    </div>


    <div class="profile_container">
        @using (Data data = new Data())
        {


            foreach (var party in data.Parties.ToList())
            {
                <div class="profile_box">

                    @{var imageSource = Url.Content($"~/Uploads/{party.HorizontalPoster}"); }
                    <a href="@Url.Action(@"ViewForUpdate",@"AdminParty",new { id = party.Id })">
                        <img src=@imageSource />
                    </a>

                    @{ if (party.State == State.Saved)
                        {
                            <label class="state">Saved</label>
                        }
                        else
                        {
                            if (party.StartDate > DateTime.Now)
                            {
                                <label draggable="false" class="state">@party.StartDate</label>
                            }
                            else
                            {
                                <label style="visibility:hidden" class="state">Pl</label>
                            }
                        }
                    }
                </div>
            }
        }
    </div>
</div>


@section Scripts{


    <script>



















        document.getElementById("add_entity_close_btn").addEventListener("click", function () {


            document.getElementById("add_entity").style.display = "none";

            //make element vlaues defualt

        });






        function image_upload(input_file_elem_id, pic_display_div_id) {

            console.log("660");



            var fileInput = document.getElementById(input_file_elem_id);
            var file = fileInput.files[0];

            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById(pic_display_div_id);
            div.style.backgroundImage = 'url(' + fileUrl + ')';

            console.log("673");

        }

        document.getElementById("picture1").addEventListener("change", function () {
            console.log("505");
            image_upload('picture1', 'pic_1');
        }, false);

        document.getElementById("picture2").addEventListener("change", function () {
            image_upload('picture2', 'pic_2');
        }, false);



        function SaveImageAndReturnGuid(file) {

            var guid = null;


            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                type: "POST",
                url: "SaveImage.ashx",
                data: formData,
                contentType: false,
                processData: false,
                async: false,
                success: function (response) {
                    guid = response;
                },
                error: function (ex) {
                    test1 = ex;
                }
            });


            return guid;
        }









        function confirmDelete(id) {
            if (confirm("Are you sure you want to delete this?")) {

                window.location.href = "/AdminParty/DeleteParty?party_id=" + id;

            }
        }



        var newTab = null;

    $(document).ready(function () {
        $("#preview_button").click(function (e) {
            e.preventDefault();

            var form = $("#form");
            var formData = new FormData(form[0]);

            $.ajax({
                type: "POST",
                url: "@Url.Action("PreviewParty","StartPage")",
                data: formData,
                async: false,
                contentType: false,
                processData: false,
                success: function (result) {

                    console.log(result);

                     newTab = window.open();
                    newTab.document.write(result);
                }
            });
        });
    });


    </script>


    @if (Model.Party != null)
    {
        <script>

        var publishDate = '@Model.Party.StartDate.ToString("yyyy-MM-ddTHH:mm:ss")';
            document.getElementById('Party_StartDate').value = publishDate;

        </script>
    }



}




