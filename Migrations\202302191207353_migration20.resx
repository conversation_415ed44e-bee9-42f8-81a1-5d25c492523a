﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>