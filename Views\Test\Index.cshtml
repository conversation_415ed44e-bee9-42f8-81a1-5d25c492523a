﻿@model stockhom_soundrom_mvc.ViewModels.TestViewModel


@{
    ViewData["Title"] = "Index";
}


<button id="addVideoBtn" type="button">Add Video</button>
<div id="videoSelectContainer"></div>



@*<script>
    var videoCount = 0;
    document.getElementById("addVideoBtn").addEventListener("click", function () {
        var select = document.createElement("select");
        select.name = "vid" + videoCount;
        videoCount++;
        document.getElementById("videoSelectContainer").appendChild(select);
    });
</script>

<script>
    var videoCount = 0;
    document.getElementById("addVideoBtn").addEventListener("click", function () {
        var select = document.createElement("select");
        select.name = "vid" + videoCount;
        videoCount++;
        @foreach (var video in Model.Videos)
        {
            var selected = video.Id == Model.SelectedVideoId ? "selected" : "";
            <text>
                var option = document.createElement("option");
                option.value = "@video.Id";
                option.innerHTML = "@video.VideoTitle";
                option.setAttribute("@selected","");
                select.appendChild(option);
            </text>
        }
        document.getElementById("videoSelectContainer").appendChild(select);
    });
</script>*@


