﻿
@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <style>

        .container1 {
            text-align: center;
            max-width: 800px;
            padding: 5%;
            padding-top: 1%;
        }

        #p_introduction {
            /* height: 90px; */
            font-size: 16px;
            /* min-width: 100%; */
            margin: 44px;
        }

        #img_1 {
            display: block;
            width: 100%;
        }

        Body {
            Text-Align: Justify;
            Margin: 0 Auto;
        }

        .Text {
            Font-Size: 24px;
        }

        .MoreText {
            Display: None;
        }

        .Read-More-Btn {
            Padding: 15px 60px;
            Background-Color: Rgb(149, 170, 197);
            Color: Rgb(53, 49, 49);
            Border: None;
            Outline: None;
            Font-Size: 20px;
            Cursor: Pointer;
            border-radius: 10px;
        }

        .Text.Show-More .MoreText {
            Display: Inline;
        }

        .Text.Show-More .Dots {
            Display: None;
        }

        .div_description {
            max-height: 5.6em;
            overflow: hidden;
            white-space: pre-wrap;
        }

            .div_description span:nth-of-type(2) {
                display: inline-block;
                background-color: #ccc;
            }


        button.read-more-button {
            background: none;
            border: none;
            color: blue;
            cursor: pointer;
            text-decoration: underline;
        }

        .img1 {
            width: 100%;
        }

        .journal {
            margin-bottom: 5%;
        }

        .DescriptionSection {
            display: block;
        }

        .DescriptionTruncated {
            width: 90%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: black;
            font-size: 16px;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
        }


        .DescriptionExtended {
            width: 90%;
            display: none;
            font-size: 16px;
            width: 90%;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
        }


        .ReadMoreButton {
            width: 90%;
            display: block;
            margin: auto;
            background-color: white;
            border-radius: 6px;
        }





        @@media screen and (max-width: 400px) {

            .desktopImage {
                display: none;
            }


            .mobileImage {
                display: block;
            }
        }


        @@media screen and (min-width: 401px) {

            .desktopImage {
                display: block;
            }


            .mobileImage {
                display: none;
            }
        }
    </style>


}


<div class="container1">
    <header></header>
    <p id="p_introduction">
        <b>The Journal </b>is the official news channel for stockholm soundorm. iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.
    </p>

    <div class="profile_container"></div>
    @{
        using (Data data = new Data())
        {
            var journals = data.Journals.ToList();

            foreach (var journal in journals)
            {
                @*@journal.BodyText.ToString();*@

                <div class="journal">


                    @{
                        var DescriptionTruncated_id = $"DescriptionTruncated_{journal.Id}";
                        var DescriptionExtended_id = $"DescriptionExtended_{journal.Id}";

                        var button_id = $"btn_{journal.Id}";

                    }

                    <img src="@Url.Content($"~/Uploads/{journal.DesktopImageUrl}")" class="img1 desktopImage" />
                    <img src=" @Url.Content($"~/Uploads/{journal.MobileImageUrl } ") " class="img1 mobileImage" />
                    <div class="DescriptionSection">
                        <p id="@DescriptionTruncated_id" class="DescriptionTruncated">@journal.BodyText.ToString()</p>
                        <button id="@button_id" onclick="window.location='@Url.Action("Index", "Journal", new { id = journal.Id })'" class="ReadMoreButton">READ MORE</button>
                    </div>
                </div>
            }


        }
    }

</div>


@section Scripts{

    <script src="~/js/JournalStartPage.js"></script>


}



