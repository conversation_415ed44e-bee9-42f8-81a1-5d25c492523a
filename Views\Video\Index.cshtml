﻿@model stockhom_soundrom_mvc.ViewModels.VideoViewModel
@using System.Data.Entity;

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <style>

        .container1 {
            text-align: center;
            max-width: 800px;
            padding: 5%;
            padding-top: 1%;
        }


        .video-container {
            position: relative;
            padding-bottom: 56.25%;
            /* 16:9 */
            height: 0;
            margin-bottom: 5vw;
        }

            .video-container iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }


        .button1 {
            width: 100%;
            display: block;
            margin: auto;
            background-color: white;
            text-size-adjust: auto;
        }

            .button1:hover {
                background-color: lightgray;
            }


        .section {
            margin-bottom: 3vw;
        }

        .streaming_services {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 4vw;
            gap: 4vw;
            justify-content: flex-start;
        }

        .DescriptionTruncated {
            width: 100%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: black;
            font-size: 1vw;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
            text-align: left;
        }

        .DescriptionExtended {
            width: 100%;
            display: none;
            font-size: 1vw;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
            text-align: left;
        }

        .button1 {
            width: 100%;
            display: block;
            margin: auto;
            background-color: white;
            text-size-adjust: auto;
        }

        .streaming_services .straming_service_icon {
            width: 35px;
            height: 35px;
        }



        .album_image {
            height: 32vw;
        }

        .related_styles {
            height: 32vw;
            text-align: left;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
        }


        .style_image {
            display: inline;
            height: 100%;
        }


        /*video title*/
        @@media screen and (max-width: 584px) {

            .video-title {
                font-size: large;
                font-size: 4.3vw;
            }
        }

        @@media screen and (min-width: 585px) {

            .video-title {
                font-size: large;
                font-size: 1.3vw;
            }
        }

        /*description*/

        @@media screen and (min-width: 585px) {

            .DescriptionTruncated {
                width: 100%;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                color: black;
                font-size: 1vw;
                margin: auto;
                margin-top: 1vw;
                margin-bottom: 1vw;
                text-align: left;
            }

            .DescriptionExtended {
                width: 100%;
                display: none;
                font-size: 1vw;
                margin: auto;
                margin-top: 1vw;
                margin-bottom: 1vw;
                text-align: left;
            }
        }

        @@media screen and (max-width: 584px) {

            .DescriptionTruncated {
                width: 100%;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                color: black;
                font-size: 3vw;
                margin: auto;
                margin-top: 1vw;
                margin-bottom: 1vw;
                text-align: left;
            }

            .DescriptionExtended {
                width: 100%;
                display: none;
                font-size: 1vw;
                margin: auto;
                margin-top: 1vw;
                margin-bottom: 1vw;
                text-align: left;
            }
        }



        /*section titile*/

        @@media screen and (min-width: 585px) {
            .section_title {
                text-align: left;
                margin-top: 2vw;
                margin-bottom: 2vw;
                font-weight: 900;
                font-size: 1.3vw;
            }
        }

        @@media screen and (max-width: 584px) {
            .section_title {
                text-align: left;
                margin-top: 2vw;
                margin-bottom: 2vw;
                font-weight: 900;
                font-size: 3.5vw;
            }
        }




        /*social media icons*/
        @@media screen and (max-width: 584px) {

            .div_social_media_icons {
                display: flex;
                justify-content: left;
                align-items: center;
                flex-wrap: wrap;
            }

            .div_single_icon {
                width: 40px;
                height: 40px;
                padding: 3px;
                margin: 5vw;
            }

                .div_single_icon img {
                    width: 100%;
                    height: 100%;
                }
        }

        @@media screen and (min-width: 585px) {

            .div_social_media_icons {
                display: flex;
                justify-content: left;
                align-items: center;
                flex-wrap: wrap;
            }

            .div_single_icon {
                width: 50px;
                height: 50px;
                padding: 3px;
                margin: 3vw;
            }

                .div_single_icon img {
                    width: 100%;
                    height: 100%;
                }
        }




        /*youtube sub button*/
        @@media screen and (min-width: 585px) {

            .youtube_subs_button {
                width: 100%;
                height: 4vw;
                font-size: 1.5vw;
                font-weight: 600;
                color: white;
                background-color: black;
                border-radius: 10px;
            }


            .youtube_subs_button_div {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .youtube_subs_button_img {
                width: 4vw;
                height: 4vw;
            }
        }




        @@media screen and (max-width: 584px) {


            .youtube_subs_button {
                width: 100%;
                height: 8vw;
                font-size: 3.5vw;
                font-weight: 600;
                color: white;
                background-color: black;
                border-radius: 10px;
            }


            .youtube_subs_button_div {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .youtube_subs_button_img {
                width: 4vw;
                height: 4vw;
            }
        }
    </style>





}




<div class="container1">

    <div class="video-title">
        @Model.Video.VideoTitle
    </div>
    <hr />
    <div class="section">
        <div class="video-container">
            <iframe width="560" height="315" src="https://www.youtube.com/embed/@Model.Video.YoutubeLink?controls=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe>
        </div>
        <button onclick="window.location.href='https://www.youtube.com/@@sthlmsndrm?sub_confirmation=1'" class="youtube_subs_button">
            <div class="youtube_subs_button_div">
                <div style="display: inline-block;">SUBSCRIBE</div>
                <img src="/icons/YouTube ikon svart.png" class="youtube_subs_button_img">
                <div style="display: inline-block;">CHANNEL</div>
            </div>
        </button>
    </div>



    @using (Data data = new Data())
    {
        Video video = new Video();


        try
        {

            video = data.Videos.Where(v => v.Id == Model.Video.Id).Include(v => v.RelatedTracks).First();
        }
        catch (Exception ex)
        {
            video = Model.Video;
        }

        @if (video.RelatedTracks.Count > 0)
        {

            var track = video.RelatedTracks.First();

            <div class="section">
                <div class="div_social_media_icons">
                    @if (string.IsNullOrEmpty(track.SpotifyLink) == false)
                    {
                        <div class="div_single_icon">
                            <a href="@track.SpotifyLink">
                                <img src="~/icons/Spotify ikon svart.png" />
                            </a>
                        </div>
                    }


                    @if (string.IsNullOrEmpty(track.AppleLink) == false)
                    {
                        <div class="div_single_icon">
                            <a href="@track.AppleLink">
                                <img src="~/icons/Apple music ikon svart.png" />
                            </a>
                        </div>
                    }

                    @if (string.IsNullOrEmpty(track.YoutubeMusicStreamLink) == false)
                    {
                        <div class="div_single_icon">
                            <a href="@track.YoutubeMusicStreamLink">
                                <img src="~/icons/YouTube music ikon svart.png" />
                            </a>
                        </div>
                    }



                    @if (track.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).Count() > 0)
                    {
                        <div class="div_single_icon">
                            <a href="@track.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).First().Link">
                                <img src="~/icons/Beatport music ikon svart.png" />
                            </a>
                        </div>
                    }
                    @if (track.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).Count() > 0)
                    {
                        <div class="div_single_icon">
                            <a href="@track.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).First().Link">
                                <img src="~/icons/Bandcamp music ikon svart.png" />
                            </a>
                        </div>
                    }
                    @if (track.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).Count() > 0)
                    {
                        <div class="div_single_icon">
                            <a href="@track.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).First().Link">
                                <img src="~/icons/Juno Download ikon svart.png" />
                            </a>
                        </div>
                    }

                </div>
            </div>
        }


        <div class="section">
            <div class="section_title">ABOUT THIS VIDEO</div>
            <p id="DescriptionTruncated" class="DescriptionTruncated">@Model.Video.Description</p>
            <p id="DescriptionExtended" class="DescriptionExtended">@Model.Video.Description</p>
            <button id="ReadMoreButton" class="button1" onclick="ReadMoreButtonClicked();">READ MORE</button>
        </div>



        @if (Model.Video.RelatedStyle != null && Model.Video.RelatedStyle.Any())
        {
            <div class="section">
                <div class="section_title">STYLE SEEN IN THIS VIDEO</div>

                <div class="related_styles">
                    @foreach (var style in Model.Video.RelatedStyle)
                    {
                        <img class="style_image" src=@Url.Content($"~/Uploads/{style.Picture1}") />
                    }

                </div>
            </div>
        }

        if (video.RelatedTracks.Count > 0)
        {

            var track = video.RelatedTracks.First();

            if (data.Musics.Any(m => m.Tracks.Any(tr => tr.Id == track.Id)))
            {
                var music = data.Musics.Where(m => m.Tracks.Any(tr => tr.Id == track.Id)).First();

                <div class="section">
                    <div class="section_title">TRACK IS FROM THIS [MAXI SINGLE / ALBUM / EP]</div>

                    <div style="text-align:left">
                        <a href="@Url.Action("Index","Music",new { id = music.Id})">
                            <img class="album_image" src=@Url.Content($"~/Uploads/{music.ArtWorkSquare}") />
                        </a>
                    </div>
                </div>
            }

        }


    }





</div>

@section Scripts{

    <script>
        function ReadMoreButtonClicked() {



            var DescriptionTruncated = document.getElementById("DescriptionTruncated");
            var DescriptionExtended = document.getElementById("DescriptionExtended");
            var ReadMoreButton = document.getElementById("ReadMoreButton");


            if (ReadMoreButton.textContent == 'READ MORE') {
                DescriptionTruncated.style.display = 'none';
                DescriptionExtended.style.display = 'block';
                ReadMoreButton.textContent = 'READ LESS';
            } else {
                DescriptionTruncated.style.display = '-webkit-box';
                DescriptionExtended.style.display = 'none';
                ReadMoreButton.textContent = 'READ MORE';
            }
        }
    </script>


}