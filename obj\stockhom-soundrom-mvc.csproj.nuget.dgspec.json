{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\stockhom-soundrom-mvc-master\\stockhom-soundrom-mvc\\stockhom-soundrom-mvc.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\stockhom-soundrom-mvc-master\\stockhom-soundrom-mvc\\stockhom-soundrom-mvc.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\stockhom-soundrom-mvc-master\\stockhom-soundrom-mvc\\stockhom-soundrom-mvc.csproj", "projectName": "stockhom-soundrom-mvc", "projectPath": "C:\\Users\\<USER>\\Downloads\\stockhom-soundrom-mvc-master\\stockhom-soundrom-mvc\\stockhom-soundrom-mvc.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\stockhom-soundrom-mvc-master\\stockhom-soundrom-mvc\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "EntityFramework": {"target": "Package", "version": "[6.4.4, )"}, "Google.Apis": {"target": "Package", "version": "[1.59.0, )"}, "Google.Apis.Auth": {"target": "Package", "version": "[1.59.0, )"}, "Google.Apis.Core": {"target": "Package", "version": "[1.59.0, )"}, "Google.Apis.YouTube.v3": {"target": "Package", "version": "[1.58.0.2874, )"}, "Google.Apis.YouTubeAnalytics.v1": {"target": "Package", "version": "[1.49.0.2197, )"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[3.1.15, )"}, "Microsoft.AspNetCore.Session": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[3.1.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[3.1.10, 3.1.10]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[3.1.32, 3.1.32]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[3.1.0, 3.1.0]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[3.1.0, 3.1.0]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.403\\RuntimeIdentifierGraph.json"}}}}}