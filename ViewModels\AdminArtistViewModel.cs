﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using stockhom_soundrom_mvc.Models;

namespace stockhom_soundrom_mvc.ViewModels
{
    public class AdminArtistViewModel
    {
        //private readonly IWebHostEnvironment _env;

        //public AdminArtistViewModel(IWebHostEnvironment env)
        //{
        //    _env = env;
        //}


        public List<Video> AllVideos { get; set; } = new List<Video>();
        public List<Journal> AllJournals { get; set; } = new List<Journal>();
        public List<Track> AllTracks { get; set; } = new List<Track>();
        public List<Style> AllStyles { get; set; } = new List<Style>();
        public Artist Artist { get; set; }
        public string RootPath
        {

            get;set;
            //get
            //{

            //    return _env.WebRootPath;
            //}
        }
        public bool ViewForUpdate { get; set; }
        public bool AddSectionVisible { get; set; }

    }
    public enum AdminArtistPageState
    {

    }
}
