﻿@using System.Data.Entity;
@using System.Globalization;
@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <style>

        @@media screen and (max-width: 650px) {

            .profile_container {
                text-align: center;
                margin: auto;
            }
        }


        @@media screen and (min-width: 651px) {

            .profile_container {
                display: grid;
                grid-template-columns: 50% 50%;
                width: 80%;
                margin: auto;
            }
        }

        #p_introduction {
            /* height: 90px; */
            font-size: 16px;
            /* min-width: 100%; */
            margin: 44px;
        }


        li {
            padding: 10px;
            margin: 10px;
            cursor: pointer;
        }

        #side_nav_bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 200px;
            height: 100%;
            background-color: #2b2e2f;
            color: #FFF;
            padding: 1em;
        }


        body {
        }


        form {
            position: relative;
        }

        #add_entity {
            position: relative;
            padding: 50px;
        }


            #add_entity .close-button {
                position: absolute;
                top: 0;
                right: 50px;
                cursor: pointer;
                background-color: black;
                border: solid;
                color: white;
            }

        #lbl_entity {
            color: white;
        }


        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }





        .profile_box {
            height: 250px;
            width: 250px;
            display: inline-block;
            text-align: justify;
            margin: 14px;
        }

            .profile_box .cover_art_with_vinyls {
                cursor: pointer;
                transition: all 0.5s ease;
            }

                .profile_box .cover_art_with_vinyls:hover {
                    transform: scale(1.1);
                }

                .profile_box .cover_art_with_vinyls img {
                    height: 200px;
                }



            .profile_box .entity_name {
                /*text-align: right;*/
                color: black;
                margin-bottom: 6px;
                margin-top: 3px;
                font-size: medium;
                font-weight: 600;
                display: inline-block;
                width: 150px;
                white-space: nowrap;
                overflow: hidden !important;
                text-overflow: ellipsis;
                text-align: center;
            }

            .profile_box .related_entity {
                width: 150px;
                text-align: center;
                color: black;
                margin-bottom: 3px;
                display: block;
                font-size: small;
            }

            .profile_box .state {
                border: solid;
                color: white;
                display: block;
                width: 150px;
            }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }


        .button_container {
            /*margin-left: 50px;*/
            margin-top: 20px;
        }

        #pic_vertical {
            width: 180px;
            height: 180px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
            margin-top: 15px;
        }

            #pic_vertical:hover {
                background-color: #5d6952;
            }

        #pic_horizontal {
            width: 200px;
            height: 130px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            display: inline-block;
            background-size: 100% 100%;
        }

            #pic_horizontal:hover {
                background-color: #5d6952;
            }




        .plus {
            font-size: 73px;
            text-align: center;
            margin: auto;
            color: white;
            font-weight: 900;
        }


        .add_another {
            display: inline;
            text-align: right;
            color: white;
            position: absolute;
            right: 0px;
            font-size: x-small;
        }

            .add_another:hover {
                color: blue;
            }

        .select_related {
            background-color: black;
            color: white;
            margin: 3px;
            position: relative;
        }

        /*.select_related .close-button {
                position: absolute;
                top: 0;
                right: 0;
                display: none;
            }

            .select_related:hover .close-button {
                display: block;
            }

        .close-button {*/
        /* style the close button, such as color, size, etc. */
        /*color:red;
        }*/


        .parent {
            z-index: 1;
            background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
        }

        .child {
            position: absolute;
            z-index: 1;
        }



        .remove {
            position: absolute;
            top: 0px;
            right: 0px;
            display: block;
            box-sizing: border-box;
            width: 20px;
            height: 20px;
            border-width: 3px;
            border-style: solid;
            border-color: red;
            border-radius: 100%;
            background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
            background-color: red;
            box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
            transition: all 0.3s ease;
        }
    </style>


}

<div class="container">
    <header></header>
    <p id="p_introduction">
        <b>STOCKHOLM SOUNDROME </b>is the official news channel for stockholm soundorm. iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.
    </p>

    <div class="profile_container">
        @using (Data data = new Data())
        {

            //get the music (albums) , not saved
            foreach (var music in data.Musics.Where(m => m.State != State.Saved).Include("Tracks.RelatedArtists").ToList())
            {

                if (music.PublishDate > DateTime.Now.Date)
                {
                    continue;
                }


                var noOfVinylRecords = music.Tracks.Count();

                string VinylRecordImagePath = "";

                if (noOfVinylRecords >= 4)
                {
                    VinylRecordImagePath = Url.Content($"~/vinyls/4.png");
                }
                else
                {
                    VinylRecordImagePath = Url.Content($"~/vinyls/{noOfVinylRecords}.png");
                }

                List<Artist> relatedArtists = new List<Artist>();


                foreach (var track in music.Tracks)
                {
                    foreach (var artist in track.RelatedArtists)
                    {
                        if (relatedArtists.Contains(artist) == false)
                        {
                            relatedArtists.Add(artist);
                        }

                    }

                }





                <div id="@music.Id" class="profile_box" ondrop="drop(event)" ondragover="allowDrop(event)" ondragstart="drag(event)">

                    <a href=@Url.Action(@"Index",@"Music",new { id = music.Id })>
                        <div style="height: 150px;" class="cover_art_with_vinyls">
                            <img id="@music.Id" name="music" style="display:inline;height: 100%; margin-right:-4px;" src="@Url.Content($"~/Uploads/{music.ArtWorkSquare}")">
                            <img id="@music.Id" name="music" style="display:inline;height: 100%;" src="@VinylRecordImagePath">
                        </div>
                    </a>

                    <a href=@Url.Action(@"Index",@"Music",new { id = music.Id })>
                        <label class="entity_name">@music.Title</label>
                    </a>

                    @if (relatedArtists.Count() >= 2)
                    {
                        <a href="@Url.Action("Index","Artist",new { id=relatedArtists[0].Id })">
                            <label class="related_entity">@relatedArtists[0].ArtistName</label>
                        </a>
                        <a href="@Url.Action("Index","Artist",new { id=relatedArtists[1].Id })">
                            <label class="related_entity">@relatedArtists[1].ArtistName</label>
                        </a>
                    }
                    else if (relatedArtists.Count() == 1)
                    {
                        <a href="@Url.Action("Index","Artist",new { id=relatedArtists[0].Id })">
                            <label class="related_entity">@relatedArtists[0].ArtistName</label>
                        </a>
                    }

                </div>


            }



            //how to get tracks which are not part of music , not saved



            var tracks = data.Tracks.Where(t => t.State != State.Saved).Include(t => t.RelatedArtists).Where(t => !data.Musics.Any(m => m.Tracks.Any(mt => mt.Id == t.Id)));


            foreach (var track in tracks)
            {

                if (track.PublishDate.Date> DateTime.Now.Date)
                {
                    continue;
                }


                List<Artist> relatedArtists = new List<Artist>();



                foreach (var artist in track.RelatedArtists)
                {
                    if (relatedArtists.Contains(artist) == false)
                    {
                        relatedArtists.Add(artist);
                    }

                }





                string VinylRecordImagePath = Url.Content($"~/vinyls/1.png");


                <div class="profile_box" id="@track.Id" ondragover="allowDrop(event)" ondrop="drop(event)" ondragstart="drag(event)">
                    <a href="@Url.Action(@"Index",@"Track",new { id = track.Id })">
                        <div style="height: 150px;" class="cover_art_with_vinyls">
                            <img id="@track.Id" name="track" style="display:inline;height: 100%; margin-right:-4px;" src="@Url.Content($"~/Uploads/{track.ArtWorkSquare}")">
                            <img id="@track.Id" name="track" style="display:inline;height: 100%;" src="@VinylRecordImagePath">
                        </div>
                    </a>

                    <a href="@Url.Action(@"Index",@"Track",new { id = track.Id })">
                        <label class="entity_name">@track.TrackTitle</label>
                    </a>

                    @if (relatedArtists.Count() >= 2)
                    {
                        <a href="@Url.Action("Index","Artist",new { id=relatedArtists[0].Id })">
                            <label class="related_entity">@relatedArtists[0].ArtistName</label>
                        </a>
                        <a href="@Url.Action("Index","Artist",new { id=relatedArtists[1].Id })">
                            <label class="related_entity">@relatedArtists[1].ArtistName</label>
                        </a>
                    }
                    else if (relatedArtists.Count() == 1)
                    {
                        <a href="@Url.Action("Index","Artist",new { id=relatedArtists[0].Id })">
                            <label class="related_entity">@relatedArtists[0].ArtistName</label>
                        </a>
                    }




                </div>
            }




        }
    </div>

</div>