﻿using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Models
{
    public class Data : DbContext
    {
        public DbSet<Artist> Artists { get; set; }
        public DbSet<Track> Tracks { get; set; }
        public DbSet<Video> Videos { get; set; }
        public DbSet<Style> Styles { get; set; }
        public DbSet<Journal> Journals { get; set; }
        public DbSet<VideoViews>  VideoViews { get; set; }
        public DbSet<VideoRanking10>  VideoRankings10 { get; set; }

        public DbSet<VideoRanking30> VideoRankings30 { get; set; }
        public DbSet<Music> Musics { get; set; }
        public DbSet<MusicBuyLink> MusicBuyLinks { get; set; }
        public DbSet<TrackBuyLink> TrackBuyLinks { get; set; }

        public DbSet<Admin>  Admins { get; set; }

        public DbSet<Party> Parties { get; set; }


        public DbSet<Support> Supports { get; set; }

        public DbSet<Presentation>  Presentations { get; set; }

        public DbSet<Email> Emails { get; set; }

        public DbSet<TrackDownloadRequest> TrackDownloadRequests { get; set; }


        public Data() : base(@"Data Source=(localdb)\mssqllocaldb;Initial Catalog=stock_sound1;Integrated Security=True")
        {

        }


    }
}
