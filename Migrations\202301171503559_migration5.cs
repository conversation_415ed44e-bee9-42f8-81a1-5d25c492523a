﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration5 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.VideoJournals", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.VideoJournals", "Journal_Id", "dbo.Journals");
            DropIndex("dbo.VideoJournals", new[] { "Video_Id" });
            DropIndex("dbo.VideoJournals", new[] { "Journal_Id" });
            AddColumn("dbo.Journals", "Video_Id", c => c.Int());
            AddColumn("dbo.Journals", "Video_Id1", c => c.Int());
            AddColumn("dbo.Videos", "Journal_Id", c => c.Int());
            CreateIndex("dbo.Journals", "Video_Id");
            CreateIndex("dbo.Journals", "Video_Id1");
            CreateIndex("dbo.Videos", "Journal_Id");
            AddForeignKey("dbo.Journals", "Video_Id", "dbo.Videos", "Id");
            AddForeignKey("dbo.Journals", "Video_Id1", "dbo.Videos", "Id");
            AddForeignKey("dbo.Videos", "Journal_Id", "dbo.Journals", "Id");
            DropTable("dbo.VideoJournals");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.VideoJournals",
                c => new
                    {
                        Video_Id = c.Int(nullable: false),
                        Journal_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Video_Id, t.Journal_Id });
            
            DropForeignKey("dbo.Videos", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.Journals", "Video_Id1", "dbo.Videos");
            DropForeignKey("dbo.Journals", "Video_Id", "dbo.Videos");
            DropIndex("dbo.Videos", new[] { "Journal_Id" });
            DropIndex("dbo.Journals", new[] { "Video_Id1" });
            DropIndex("dbo.Journals", new[] { "Video_Id" });
            DropColumn("dbo.Videos", "Journal_Id");
            DropColumn("dbo.Journals", "Video_Id1");
            DropColumn("dbo.Journals", "Video_Id");
            CreateIndex("dbo.VideoJournals", "Journal_Id");
            CreateIndex("dbo.VideoJournals", "Video_Id");
            AddForeignKey("dbo.VideoJournals", "Journal_Id", "dbo.Journals", "Id", cascadeDelete: true);
            AddForeignKey("dbo.VideoJournals", "Video_Id", "dbo.Videos", "Id", cascadeDelete: true);
        }
    }
}
