﻿using stockhom_soundrom_mvc.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.ViewModels
{
    public class AdminMusicEditMusicViewModel
    {
        public Music Music { get; set; }
        public string state { get; set; }
        public int editTrackId { get; set; }

        public List<Journal> AllJournals { get; internal set; } = new List<Journal>();
        public List<Style> AllStyles { get; internal set; } = new List<Style>();
        public List<Track> AllTracks { get; internal set; } = new List<Track>();
        public List<Video> AllVideos { get; internal set; } = new List<Video>();
        public List<Artist> AllArtists { get; set; } = new List<Artist>();
    }
}
