﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration14 : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Tracks", "State", c => c.Int(nullable: false));
            AddColumn("dbo.Musics", "Title", c => c.String());
            AddColumn("dbo.Musics", "SpotifyAlbumLink", c => c.String());
            AddColumn("dbo.Musics", "AppleAlbumLink", c => c.String());
            AddColumn("dbo.Musics", "SoundCloudAlbumLink", c => c.String());
            AddColumn("dbo.Musics", "State", c => c.Int(nullable: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.Musics", "State");
            DropColumn("dbo.Musics", "SoundCloudAlbumLink");
            DropColumn("dbo.Musics", "AppleAlbumLink");
            DropColumn("dbo.Musics", "SpotifyAlbumLink");
            DropColumn("dbo.Musics", "Title");
            DropColumn("dbo.Tracks", "State");
        }
    }
}
