﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using stockhom_soundrom_mvc.ViewModels;
using stockhom_soundrom_mvc.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.Data.Entity;

namespace stockhom_soundrom_mvc.Controllers
{
    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminVideosController : Controller
    {

        public const string RelatedArtistsSelectElementsIdentifire = "select_related_artists";
        public const string RelatedTracksselectElementsIdentifire = "select_related_tracks";
        public const string RelatedStylesSelectElementsIdentifire = "select_related_styles";
        public const string RelatedJournalsSelectElementsIdentifire = "select_related_journal_entry";

        private readonly IWebHostEnvironment _env;

        public AdminVideosController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public IActionResult Index()
        {

            AdminVideosViewModel adminVideosViewModel = new AdminVideosViewModel();

            using (Data data = new Data())
            {
                adminVideosViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminVideosViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                // adminVideosViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminVideosViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminVideosViewModel.Video = new Video();
            }

            return View(adminVideosViewModel);


        }


        public ActionResult ViewForUpdate(AdminVideosViewModel adminVideosViewModel, int id)
        {
            using (Data data = new Data())
            {


                adminVideosViewModel.Video = data.Videos.Where(v => v.Id == id)
                    .Include(x => x.RelatedJournals)
                    .Include(x => x.RelatedStyle)
                    .Include(x => x.RelatedArtists)
                    .Include(x => x.RelatedTracks)
                    .First();
                adminVideosViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminVideosViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminVideosViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminVideosViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminVideosViewModel.ViewForUpdate = true;
                adminVideosViewModel.AddSectionVisible = true;

                return View("index", adminVideosViewModel);
            }


        }


        public IActionResult NewAddSection()
        {
            AdminVideosViewModel adminArtistViewModel = new AdminVideosViewModel();

            using (Data data = new Data())
            {
                adminArtistViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminArtistViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminArtistViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminArtistViewModel.AllArtists = data.Artists.AsEnumerable().Select(artist => new Artist() { Id = artist.Id, ArtistName = artist.ArtistName }).ToList();
                adminArtistViewModel.Video = new Video();
                adminArtistViewModel.AddSectionVisible = true;
            }

            return View("index", adminArtistViewModel);
        }


        [HttpPost]
        public ActionResult Save(AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            //do data validation



            using (var data = new Data())
            {
                var video = GenerateVideoFromAdminVideosViewModel(data, adminVideosViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);

                data.Videos.Add(video);
                data.SaveChanges();
            }

            return RedirectToAction("Index");

        }


        public ActionResult DeleteVideo(int video_id)
        {
            using (Data data = new Data())
            {
                var videoToBeDeleted = data.Videos.Where(v => v.Id == video_id).FirstOrDefault();

                data.Videos.Remove(videoToBeDeleted);
                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }

        [HttpPost]
        public ActionResult EditSave(int video_id, AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            //do data validation


            using (Data data = new Data())
            {
                var video = GenerateVideoFromAdminVideosViewModel(data, adminVideosViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);


                var existingVideo = data.Videos.Where(j => j.Id == video_id)
                    .Include(t => t.RelatedTracks)
                    .Include(t => t.RelatedStyle)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Single();


                if (string.IsNullOrEmpty(video.VerticalPosterUrl))
                {
                    video.VerticalPosterUrl = existingVideo.VerticalPosterUrl;
                }
                if (string.IsNullOrEmpty(video.HorizontalPosterLinkUrl))
                {
                    video.HorizontalPosterLinkUrl = existingVideo.HorizontalPosterLinkUrl;
                }


                //all the nesated objects that has to be removed
                existingVideo.RelatedTracks.Clear();
                existingVideo.RelatedStyle.Clear();
                existingVideo.RelatedArtists.Clear();
                existingVideo.RelatedJournals.Clear();
                data.SaveChanges();

                existingVideo.Description = video.Description;
                existingVideo.HorizontalPosterLinkUrl = video.HorizontalPosterLinkUrl;
                existingVideo.MetaDescription = video.MetaDescription;
                existingVideo.MetaTitle = video.MetaTitle;
                existingVideo.PublishDate = video.PublishDate;
                existingVideo.RelatedArtists = video.RelatedArtists;
                existingVideo.RelatedJournals = video.RelatedJournals;
                existingVideo.RelatedStyle = video.RelatedStyle;
                existingVideo.RelatedTracks = video.RelatedTracks;
                existingVideo.State = State.Saved;
                existingVideo.VerticalPosterUrl = video.VerticalPosterUrl;
                existingVideo.VideoTitle = video.VideoTitle;
                existingVideo.YoutubeLink = video.YoutubeLink;



                data.SaveChanges();
            }

            return RedirectToAction("Index");

        }

        [HttpPost]
        public ActionResult Publish(AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            //do data validation


            using (Data data = new Data())
            {
                var video = GenerateVideoFromAdminVideosViewModel(data, adminVideosViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);

                if (video.PublishDate == DateTime.MinValue) // means the publish day is not set
                {
                    video.PublishDate = DateTime.Now;
                }

                if (video.PublishDate >= DateTime.Now)
                {
                    video.State = State.ToBePublished;
                }
                else
                {
                    video.State = State.Published;
                }


                data.Videos.Add(video);
                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }


        [HttpPost]
        public ActionResult EditPublish(int video_id, AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            //do data validation


            using (Data data = new Data())
            {
                var video = GenerateVideoFromAdminVideosViewModel(data, adminVideosViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);


                var existingVideo = data.Videos.Where(j => j.Id == video_id)
                    .Include(t => t.RelatedTracks)
                    .Include(t => t.RelatedStyle)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Single();


                if (string.IsNullOrEmpty(video.VerticalPosterUrl))
                {
                    video.VerticalPosterUrl = existingVideo.VerticalPosterUrl;
                }
                if (string.IsNullOrEmpty(video.HorizontalPosterLinkUrl))
                {
                    video.HorizontalPosterLinkUrl = existingVideo.HorizontalPosterLinkUrl;
                }


                //all the nesated objects that has to be removed
                existingVideo.RelatedTracks.Clear();
                existingVideo.RelatedStyle.Clear();
                existingVideo.RelatedArtists.Clear();
                existingVideo.RelatedJournals.Clear();
                data.SaveChanges();

                existingVideo.Description = video.Description;
                existingVideo.HorizontalPosterLinkUrl = video.HorizontalPosterLinkUrl;
                existingVideo.MetaDescription = video.MetaDescription;
                existingVideo.MetaTitle = video.MetaTitle;
                existingVideo.PublishDate = video.PublishDate;
                existingVideo.RelatedArtists = video.RelatedArtists;
                existingVideo.RelatedJournals = video.RelatedJournals;
                existingVideo.RelatedStyle = video.RelatedStyle;
                existingVideo.RelatedTracks = video.RelatedTracks;
                existingVideo.State = State.Published;
                existingVideo.VerticalPosterUrl = video.VerticalPosterUrl;
                existingVideo.VideoTitle = video.VideoTitle;
                existingVideo.YoutubeLink = video.YoutubeLink;








                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }


        [HttpPost]
        public ActionResult Preview(AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            VideoViewModel videoViewModel = new VideoViewModel();
            using (Data data = new Data())
            {
                videoViewModel.Video = GenerateVideoFromAdminVideosViewModel(data, adminVideosViewModel, pic_vertical_upload, pic_horizontal_upload, State.ToBePublished);
            }
            return View(@"~/Views/Artist/Index.cshtml", videoViewModel);
        }


        Video GenerateVideoFromAdminVideosViewModel(Data data, AdminVideosViewModel adminVideosViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, State state)
        {
            try
            {
                Video video = new Video();

                //control bounded data
                video.VideoTitle = adminVideosViewModel.Video.VideoTitle;
                video.Description = adminVideosViewModel.Video.Description;
                video.MetaTitle = adminVideosViewModel.Video.MetaTitle;
                video.MetaDescription = adminVideosViewModel.Video.MetaDescription;
                video.PublishDate = adminVideosViewModel.Video.PublishDate;
                video.YoutubeLink = adminVideosViewModel.Video.YoutubeLink;
                video.State = state;


                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    video.VerticalPosterUrl = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    video.HorizontalPosterLinkUrl = saveIFromFile(pic_horizontal_upload);
                }




                //request form data
                var relatedTracks = Request.Form[RelatedTracksselectElementsIdentifire];
                foreach (var relatedTrackIdString in relatedTracks)
                {
                    int trackId = int.Parse(relatedTrackIdString);
                    var relatedTrack = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                    video.RelatedTracks.Add(relatedTrack);

                }

                var relatedArtists = Request.Form[RelatedArtistsSelectElementsIdentifire];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.Where(t => t.Id == artistId).FirstOrDefault();
                    video.RelatedArtists.Add(relatedArtist);

                }

                var relatedStyles = Request.Form[RelatedStylesSelectElementsIdentifire];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    video.RelatedStyle.Add(relatedStyle);

                }

                var relatedJournals = Request.Form[RelatedJournalsSelectElementsIdentifire];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    video.RelatedJournals.Add(relatedJournal);

                }

                return video;

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }
    }
}
