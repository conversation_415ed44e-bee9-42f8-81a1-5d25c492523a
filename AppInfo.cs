﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc
{
    public class AppInfo
    {
        private static readonly string _filePath = "data.txt";

        public static DateTime GetValue(string key)
        {
            var data = File.ReadAllText(_filePath);
            var values = JsonConvert.DeserializeObject<Dictionary<string, DateTime>>(data);
            if (values == null)
            {
                return DateTime.MinValue;
            }
            else
            {
                return values.ContainsKey(key) ? values[key] : DateTime.MinValue;
            }
        }

        public static void SetValue(string key, DateTime value)
        {
            var data = File.ReadAllText(_filePath);
            var values = JsonConvert.DeserializeObject<Dictionary<string, DateTime>>(data) ?? new Dictionary<string, DateTime>();
            values[key] = value;
            data = JsonConvert.SerializeObject(values);
            File.WriteAllText(_filePath, data);
        }
    }
}
