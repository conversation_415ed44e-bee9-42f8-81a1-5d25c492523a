﻿function handleClick(id) {
    // do something with the id
    console.log(id);

    //hide
    var elements = document.querySelectorAll('[id^="jnld_"]');
    for (var i = 0; i < elements.length; i++) {
        elements[i].style.display = "none";
    }

    //show

    var parts = id.split("_");
    var lastPart = parts[parts.length - 1];

    document.getElementById("jnld_" + lastPart).style.display = "block";

}



var result = null;
var tempvar = null;


const promptOverlay = document.getElementById('email-prompt-overlay');
const prompt = document.getElementById('email-prompt');
const promptClose = document.getElementById('cross_button');


function submitButton1Clicked(intput_elem_name) {

    var input = document.getElementById(intput_elem_name);

    if (input.checkValidity()) {
        // Get the value of the input field
        var value = input.value;

        EmailAlreadyExist(value)

        if (result == true) {

            //here we know that the email already exist.
            //so no action is taken. clear the txt element and return

            input.value = "";

            return;
        }



        //when we come to this stage we know that intput email
        //is valid and also it does not already exist.
        //so we should copy the txt to txt2,
        //hide #input_div and






        //set the value of txt to txt2
        document.getElementById("txt2").value = value;

        displayPrompt();


        SaveData(input.value);


        input.value = "";

    }
    else { // when we come to here we know the input is not valid in #txt

        input.value = "";

        return;
    }
}


function submitButton2Clicked() {

    var emailToSave = "";
    var countryToSave = "";

    emailToSave = document.getElementById("txt2").value;


    var selectedIndex = document.getElementById("country").selectedIndex;

    if (selectedIndex == 0) {

        countryToSave = "NA";

    }
    else {

        countryToSave = document.getElementById("country").options[selectedIndex].text;
    }


    SaveData(emailToSave, countryToSave);

    promptOverlay.style.display = 'none';
    prompt.style.display = 'none';
    document.body.style.overflow = 'auto';

    showPopup();
}

function EmailAlreadyExist(emailString) {

    result = null;

    $.ajax({
        type: "POST",
        url: "/StartPage/EmailExists",
        data: { param: emailString },
        async: false,
        success: function (response) {

            tempvar = response;


            if (response == true) {

                result = true;
            }
            else
                result = false;

        }
    }).fail(function (error) {

        result = error;

    });


}



function SaveData(email, country) {

    result = null;

    var data = {
        email: email,
        country: country
    };


    $.ajax({
        type: "POST",
        url: "/StartPage/SaveData",
        data: JSON.stringify(data),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (response) {
            result = response

        }
    }).fail(function (error) {

        result = error;
    });
}



//this function is to display the prompt

function displayPrompt() {
    promptOverlay.style.display = 'block';
    prompt.style.display = 'block';
    document.body.style.overflow = 'hidden';

}



promptClose.addEventListener('click', () => {
    promptOverlay.style.display = 'none';
    prompt.style.display = 'none';
    document.body.style.overflow = 'auto';
});

promptOverlay.addEventListener('click', () => {
    promptOverlay.style.display = 'none';
    prompt.style.display = 'none';
    document.body.style.overflow = 'auto';
});

window.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
        promptOverlay.style.display = 'none';
        prompt.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
});

function showPopup() {
    // Create the popup element
    var popup = document.createElement("div");
    popup.innerHTML = "Saved..";
    popup.style.position = "fixed";
    popup.style.left = "50%";
    popup.style.top = "50%";
    popup.style.transform = "translate(-50%, -50%)";
    popup.style.backgroundColor = "white";
    popup.style.border = "1px solid black";
    popup.style.padding = "10px";
    popup.style.color = "blue";
    popup.style.fontWeight = "900";

    // Add the popup to the page
    document.body.appendChild(popup);

    // Hide the popup after 1 second
    setTimeout(function () {
        popup.style.display = "none";
    }, 1000);
}






