﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Controllers
{
    public class TrackController : Controller
    {

        private readonly IWebHostEnvironment _env;

        public TrackController(IWebHostEnvironment env)
        {
            _env = env;
        }



        public IActionResult Index(int id = 2)
        {
            TrackViewModel trackViewModel = new TrackViewModel();

            using (Data data = new Data())
            {

                trackViewModel.Track = data.Tracks
                    .Where(x => x.Id == id)
                    .Include(x => x.RelatedJournals)
                    .Include(x => x.RelatedStyles)
                    .Include(x => x.RelatedVideos)
                    .Include(x => x.RelatedArtists)
                    .Include(x => x.BuyLinks)
                    .First();

                trackViewModel.RelatedAlbum = data.Musics.Where(m => m.Tracks.Any(t => t.Id == id)).Include(m => m.Tracks.Select(t => t.RelatedArtists)).FirstOrDefault();

                if (trackViewModel.RelatedAlbum != null)
                {
                    foreach (var track in trackViewModel.RelatedAlbum.Tracks)
                    {
                        if (track.Id != id)
                        {
                            trackViewModel.OtherTracks.Add(track);
                        }
                    }
                }


            }

            return View(trackViewModel);
        }

        [HttpPost]
        public IActionResult Preview(AdminMusicViewModel adminMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {
            TrackViewModel trackViewModel = new TrackViewModel();

            using (Data data = new Data())
            {

                trackViewModel.Track = GenerateTrackFromAdminMusicViewModel(data, adminMusicViewModel, pic_vertical_upload, pic_horizontal_upload, track_file, State.ToBePublished);

            }

            return View("Index", trackViewModel);
        }



        public IActionResult PreviewCollectionsTrack(AdminMusicEditMusicViewModel adminMusicEditMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file)
        {


            TrackViewModel trackViewModel = new TrackViewModel();



            //existing track

            int existing_track_id = int.Parse(Request.Form["track_id_"]);

            using (Data data = new Data())
            {


                trackViewModel.RelatedAlbum = data.Musics.Where(m => m.Tracks.Any(t => t.Id == existing_track_id)).Include(m => m.Tracks.Select(t => t.RelatedArtists)).FirstOrDefault();

                if (trackViewModel.RelatedAlbum != null)
                {
                    foreach (var track in trackViewModel.RelatedAlbum.Tracks)
                    {
                        if (track.Id != existing_track_id)
                        {
                            trackViewModel.OtherTracks.Add(track);
                        }
                    }
                }



                var exsisting_track = data.Tracks.Where(t => t.Id == existing_track_id)
                    .Include(t => t.RelatedArtists)
                    .Include(t => t.RelatedJournals)
                    .Include(t => t.RelatedStyles)
                    .Include(t => t.RelatedVideos)
                    .Include(t => t.BuyLinks)
                    .FirstOrDefault();


                int loopvar = int.Parse(Request.Form["loop_var"]);



                var slsdkfjdslf = 0;

                var NewTrack = new Track();
                NewTrack.AppleLink = Request.Form[$"Music.Tracks[{loopvar}].AppleLink"];
                NewTrack.ArtWorkHoriontal = Request.Form["pic_horizontal_upload"];
                NewTrack.ArtWorkSquare = Request.Form["pic_vertical_upload"];
                NewTrack.BPM = Request.Form[$"Music.Tracks[{loopvar}].BPM"];
                //NewTrack.BuyLinks = Request.Form[""];
                NewTrack.CatalogNumber = Request.Form[$"Music.Tracks[{loopvar}].CatalogNumber"];
                NewTrack.Description = Request.Form[$"Music.Tracks[{loopvar}].Description"];
                NewTrack.ISRC = Request.Form[$"Music.Tracks[{loopvar}].ISRC"];
                NewTrack.MainGenre = Request.Form[$"Music.Tracks[{loopvar}].MainGenre"];
                NewTrack.MetaDescription = Request.Form[$"Music.Tracks[{loopvar}].MetaDescription"];
                NewTrack.MetaTitle = Request.Form[$"Music.Tracks[{loopvar}].MetaTitle"];
                NewTrack.OfferFreeDownload = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].OfferFreeDownload : exsisting_track.OfferFreeDownload;
                NewTrack.PublishDate = DateTime.Parse(Request.Form[$"Music.Tracks[{loopvar}].PublishDate"]);
                NewTrack.SoundCloudLink = Request.Form[$"Music.Tracks[{loopvar}].SoundCloudLink"];

                NewTrack.SpotifyLink = Request.Form[$"Music.Tracks[{loopvar}].SpotifyLink"];
                NewTrack.StartCountDown = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].StartCountDown : exsisting_track.StartCountDown;
                NewTrack.SubGenre = Request.Form[$"Music.Tracks[{loopvar}].SubGenre"];
                NewTrack.TopPromo = (adminMusicEditMusicViewModel.Music.Tracks.Count > 0) ? adminMusicEditMusicViewModel.Music.Tracks[0].TopPromo : exsisting_track.TopPromo;
                NewTrack.TrackTitle = Request.Form[$"Music.Tracks[{loopvar}].TrackTitle"];
                NewTrack.YoutubeLink = Request.Form[$"Music.Tracks[{loopvar}].YoutubeLink"];
                NewTrack.YoutubeMusicStreamLink = Request.Form[$"Music.Tracks[{loopvar}].YoutubeMusicStreamLink"];





                //related artists
                NewTrack.RelatedArtists = new List<Artist>();
                var relatedArtistIdArray = Request.Form["select_related_artists"];
                if (relatedArtistIdArray.Count > 0)
                {
                    foreach (string id in relatedArtistIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Artists.Where(a => a.Id == intid).First();
                        NewTrack.RelatedArtists.Add(entity);
                    }
                }

                //select_related_videos
                NewTrack.RelatedVideos = new List<Video>();
                var relatedVideoIdArray = Request.Form["select_related_videos"];
                if (relatedVideoIdArray.Count > 0)
                {
                    foreach (string id in relatedVideoIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Videos.Where(a => a.Id == intid).First();
                        NewTrack.RelatedVideos.Add(entity);
                    }
                }


                //select_related_styles
                NewTrack.RelatedStyles = new List<Style>();
                var relatedStyleIdArray = Request.Form["select_related_styles"];
                if (relatedStyleIdArray.Count > 0)
                {
                    foreach (string id in relatedStyleIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Styles.Where(a => a.Id == intid).First();
                        NewTrack.RelatedStyles.Add(entity);
                    }
                }


                //select_related_journal_entry
                NewTrack.RelatedJournals = new List<Journal>();
                var relatedJournalIdArray = Request.Form["select_related_journal_entry"];
                if (relatedJournalIdArray.Count > 0)
                {
                    foreach (string id in relatedJournalIdArray)
                    {
                        int intid = int.Parse(id);
                        var entity = data.Journals.Where(a => a.Id == intid).First();
                        NewTrack.RelatedJournals.Add(entity);
                    }
                }


                //buylinks
                NewTrack.BuyLinks = new List<TrackBuyLink>();
                var input_text_array = Request.Form["input_text_buylink"];
                if (input_text_array.Count > 0)
                {
                    foreach (string link in input_text_array)
                    {
                        if (link != "")
                        {
                            NewTrack.BuyLinks.Add(new TrackBuyLink() { Link = link });

                        }
                    }
                }




                if (pic_vertical_upload == null || pic_vertical_upload.Length == 0)
                {

                    NewTrack.ArtWorkSquare = exsisting_track.ArtWorkSquare;
                }
                else
                {
                    NewTrack.ArtWorkSquare = saveIFromFile(pic_vertical_upload);

                }



                if (pic_horizontal_upload == null || pic_horizontal_upload.Length == 0)
                {

                    NewTrack.ArtWorkHoriontal = exsisting_track.ArtWorkHoriontal;
                }
                else
                {
                    NewTrack.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);

                }

                trackViewModel.Track = NewTrack;




                return View("Index", trackViewModel);






            }









        }

        private Track GenerateTrackFromAdminMusicViewModel(Data data, AdminMusicViewModel adminMusicViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile track_file, State state)
        {
            try
            {
                Track track = new Track()
                {
                    AppleLink = adminMusicViewModel.Track.AppleLink,
                    PublishDate = adminMusicViewModel.Track.PublishDate,
                    BPM = adminMusicViewModel.Track.BPM,
                    BuyLinks = adminMusicViewModel.Track.BuyLinks,
                    ArtWorkHoriontal = "",
                    ArtWorkSquare = "",
                    CatalogNumber = adminMusicViewModel.Track.CatalogNumber,
                    Description = adminMusicViewModel.Track.Description,
                    ISRC = adminMusicViewModel.Track.ISRC,
                    MainGenre = adminMusicViewModel.Track.MainGenre,
                    MetaDescription = adminMusicViewModel.Track.MetaDescription,
                    MetaTitle = adminMusicViewModel.Track.MetaTitle,
                    OfferFreeDownload = adminMusicViewModel.Track.OfferFreeDownload,
                    SoundCloudLink = adminMusicViewModel.Track.SoundCloudLink,
                    SpotifyLink = adminMusicViewModel.Track.SpotifyLink,
                    StartCountDown = adminMusicViewModel.Track.StartCountDown,
                    SubGenre = adminMusicViewModel.Track.SubGenre,
                    TopPromo = adminMusicViewModel.Track.TopPromo,
                    TrackNoInMusicCollection = adminMusicViewModel.Track.TrackNoInMusicCollection,
                    TrackTitle = adminMusicViewModel.Track.TrackTitle,
                    YoutubeLink = adminMusicViewModel.Track.YoutubeLink,
                    YoutubeMusicStreamLink = adminMusicViewModel.Track.YoutubeMusicStreamLink,
                    State = state
                };


                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    track.ArtWorkSquare = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    track.ArtWorkHoriontal = saveIFromFile(pic_horizontal_upload);
                }
                if (track_file != null && track_file.Length > 0)
                {
                    track.TrackPath = saveIFromFile(track_file);
                }



                //request form data


                var buylinks = Request.Form[Global.BuyLinkTXTName];
                foreach (var buylinksIdString in buylinks)
                {
                    track.BuyLinks.Add(new TrackBuyLink() { Link = buylinksIdString });

                }



                var relatedVideos = Request.Form[Global.RelatedVideosDDLName];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    track.RelatedVideos.Add(relatedVideo);

                }

                var relatedStyles = Request.Form[Global.RelatedStylesDDLName];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    track.RelatedStyles.Add(relatedStyle);

                }

                var relatedJournals = Request.Form[Global.RelatedJournalsDDLName];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    track.RelatedJournals.Add(relatedJournal);



                }

                var relatedArtists = Request.Form[Global.RelatedArtistsDDLName];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.Where(t => t.Id == artistId).FirstOrDefault();
                    track.RelatedArtists.Add(relatedArtist);



                }

                return track;
            }
            catch (Exception ex)
            {
                return null;
            }
        }


        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }


        public bool ManageTrackRequst(string email, int track_id)
        {

            string code = "";

            //add a track request record
            using (Data data = new Data())
            {
                code = GenerateRandomString(16);

                data.TrackDownloadRequests.Add(new TrackDownloadRequest()
                {
                    requestedTime = DateTime.Now,
                    Email = email,
                    trackId = track_id,
                    GeneratedCode = code
                });
                data.SaveChanges();
            }

            string downloadlink = "";

            if (_env.EnvironmentName == "Development")
            {
                downloadlink = $"https://localhost:44356/dwnld/{code}";
            }
            else
            {
                downloadlink = $"{Global.DomainLink}dwnld/{code}";
            }

            string body = $"Follow the link to download the track \n {downloadlink}";

            //send email 
            EmailSend.SendMail("Download Track", body, email);

            return false;
        }


        public string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
              .Select(s => s[random.Next(s.Length)]).ToArray());
        }

    }




}

