﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>H4sIAAAAAAAACuwd227cuvG9QP9B2Ke2SLy+vLTG+hwkdtL6NE6M7CZonwJ5RdtCtNJW0rp2i35ZH/pJ/YVSEiXxMqRIibrsNvDLmpfh3DjDIUfkf//9n8XPz5vAeUJx4kfhxezk6HjmoHAdeX74cDHbpfevfz/7+adf/2rxzts8O1/LdmdZO9wzTC5mj2m6PZ/Pk/Uj2rjJ0cZfx1ES3adH62gzd71ofnp8/If5yckcYRAzDMtxFp93YepvUP4P/vcyCtdom+7c4CbyUJCQclyzzKE6H90NSrbuGl3MkjRaf3+MNt+SaBd6Mf6xeVofFf1mzpvAdzFOSxTczxw3DKPUTTHG518StEzjKHxYbnGBG6xetgi3u3eDBBFKzuvmukQdn2ZEzeuOJaj1DqO5MQR4cka4NOe7t+J1wWrCx3eY3+lLRnXOy4vZmzj1k3Tm8EOdXwZx1kzN6KOi+yunbPS6bPQaN3pVKQrWp+zvlXO5C9JdjC5CtEtjN3jl3O7uAn/9Z/Syir6j8CLcBQGNMEYZ1zEFuOg2jrYoTl8+o3tCxrU3c+ZsvznfsepG9SlIvA7Ts9OZ8xEP7t4FqNIHih3LNIrRH1GIYjdF3q2bpijG4rz2UM5RYXRuLMyI1F+7wfXGfUBf4qAcGSsjnmEz58Z9/oDCh/TxYoZ/zpz3/jPyyhKCzZfQxxMSd0rjHWoa8E9R7P8jCtMBhyx0Ifvd+1BXKFnH/raYEz2PdYNSd+WnARpkpCEpy+de8oi8K6zTvY+2TOlRsGE+IiXCtOPgfHSf/Id8FnIQf4l2cYi7zJzPKMgbJI/+lgAnld/yKuQVyombvsfG6XOUjQa3+LZy4weELeIqUjZb4uK1AbLL9CVAMKp51TcAQ6ZCQIytNcVnFbvr7zA+eRWED1Mh4MPWmuLz1fdQBOOTVykECdUL2IGNICQX89pJKl0nUYzWvpP0/+E8Naz99zTaDubIbqI7P0DDuWrkeoEfDuNfDtOTvY28lxV6TodymbTDzH6vcBDT7MhEhxint1jJbjCEGAcjJcS3EbZYbmgMcEiO2/fmvH236dNLO9/g00ufYYhy7ogVnr1ep/CuvayR+Paq2tSZEsQafTyEGVsj8fJdMdP19hCCYAO1v1eiq+3wiZxbuvu89w9n32xYMJuG8VK3/jrbjDgZaiC8xTDMQGe9D4TnFXITZMUPvnvO9lLc4IMfft9Dv6VyWJqRHe+e4LjPSlis7ZFgnARzay3cLONj3hEV5RI3RCr7ijWXhcGXuJ68Vu13iiadnE5OaGunk/f+4XSarEI/m5atTIbm5gs/PeGtGSsmQ3upCOPU2mTkk6eDyYDRIZWmyOiajNLOSWxGUa02GqRNJ6uRA2xtNfLeP6xGk9X4a7RLd3dokHVLLpJhlsXlWdVtlOCF2bCHVcWYGUeHGNfmbs6PI6m93VZqdb7AexflIYQhskp33HJ/RIlvax/duANmvJ5W4skuue3thrVw4Uo8OTff3oV/dsPvue3r4skJkPEdeoaIeZ5G0au3TA31kk+uLoSr30gzTluYWlhZ2CbddeWrj/6edNOUHMT4ejL1bB4rC4aM15eYv2kT3pb0NZetRFupOlhX6QZmmrrbMDvoOeeuk/eB+1Dn3bXeUcfQLGsrXq54KA5e8MSkpcKK4AZt7lDMZ9HMnK9usMMlx4LMmA5L94lqfKJuvIreInGEU5H7BZ/pwjdJEq39nKFsmM8vddjh34Weo3eYVi/SqlSIG8xjf4u5inXhYvY7gbZG4FVkXAMvUxSVsBdzilw1F4pFRBPxTCuIZrKY0aeYhTgYodUSUY2XuFDsTKp4NmeqMQbEsvtmMsQkm2g1XmS7VZ9USU5Uf1LltuPUeKmk2pLUEaRKNvrUePG7fp0JXXInD2bzwYBIMP6U4aYORmsciaPXJ1qdRdefPsPxrBaWKu3uRv+gSg7EyFpI5k3t016AHVjtSdCthSAfgdsinN+cN7MgppSz8aMSQUkwydFd7RUYkg/HoU1cPT46OunGADogUSIIRicceiT4NSQdCmssEl7ERJfZzjbO7yyX9DhidIGY50uCSNiTkL1SHvUM2BKl/CFiHXlxFnquBlDvHQoQKhPXAKJwkQAAYioaupc7bUJ3Mt8aupdnckJ3Ijed7kT5pVCqSaUDjCghDIlUcnAozREkI2xJU43VGZH8Fot2TFeRRasHi7JJFEeBq4jgN3ZYDmhwp/BPCqawDeTIM+0gFpTqPdeFMQDdtVhkhMNrKJ2Qrw3pwkpJQ4VaEM+lRYi0K+I/jQiQQro0SgrK4ZivF6HzyRcywjWELokI25A+qNBLVZSRTuqbcCbhXBeyuYiwcbq0IBk+ixRJb44Z9aNGio7SpSpYoYwTe5kDkhPPBqZozAh1MNmBLQPND+hwtYErQHipG2B24AcbUvY+b8oJ3cALKObUjjo7cIOLMxstUVt2cCeUEm4o4lDdSJTnRb2obmIJHHs28rYtR5gzMAk/pGGpXmDK4w4u+/Ui0S5sKA/kquCzqlvMi8s0SMFiLrl1Y3HjbreZFOuepMRZFldwXL5emt9IsSlgzNfMxOND5WokfFDrPiCuFg+NMX3vx0maBdN3bnaIeOlthGZ5qC0J3Moh2GhaFFMZy5Xts99FH42bMXh4NRPfY7o2+Lg5JxEJflPsiLsucVaeGwMH35dRsNuEssNzVW/xYgoallirDxm6gYKGDdXrQ6cvm6Ch0uX60K7oLydpcEyFPjzqS1saGlVsBkuKn1CpD5e7/YGGylXpwyTn+zQsUiTCWMy5mcBPPSqlgLTkjB8/lbUmumJZ2HKml5tl5lNd2rOfuS58R89pOltpoKHc9/KMgnJ1Bvaj+iyesRpV6WHNxvoLdhpgXWo8r6WzusWc5j9T5+Y3Xz2e3d0D+yPdrWhpfcCATsP2SPr1Y3nywYApSJcbaHj13S6j3lWpMaRTENJpC0hnIKQzE0jMp7U0MKZCHx77eS0NkK05qFkmDe1bzrLiQMp8lkn69TPLxNWwbB08klRIFGtNKmCQriEVSb9+pMJ8JUaDYSoMIjbqSzAmVqPKzeM/6mMvKACkqttEgNxnXXAYyDUab8Xz/xgN7oFNZ7cW7RqR8pi/pS2Rdpc6+fybFsa7A9/GjM3sYu/SLqdBmLp8lnTuKVwWZoSpIaE+8WAtdVU8vLzZHWNwb0Q4fjTbCBG6S7Y94I3ObGMcvp1AcnwpclBLF0qgkE5kPKyQaI0f2cZviV8BxRA9/kDAWAO43BqTGLTuBYaa2tIGs3JaMrGAZUHEYJrPHkq2Pnw2Em3dDZStZB9Tykf+qH0a0v2FO7Qf1LB0li+XIGUS18pnbl6tPXPB1KqWXCxgWZAtmKu1VzOXTwEzEm3dDZQtqdYWbjVHJiXdQ5i5ZSaMkXTLTqBspak+IA/Z9LhpyJXNtxvQS3SWKZzGZxInNa6i81batlmVANiSsQVIC8JWZRTulaWWJCq2EbvcbufN9O22MsdxUpLfbysO5WK2ETzpCkpd16LLkzgnJXAmK3RvrXuZs9lG2GVfUNrg2VETT9ks1UmJm017HXCFYUfeXDZti93lsqvJFrKMqWDi7CTEDaYIa2Kmuy2uxAvnf3p+/rHpdZJdplNdM6NDsx1dYfKMjXfHlXoi25MHZQFkFU9CR4DE6ZZ4GWPUs3YIedd8k2p/n5RU/1d51yTnufkJRCEJumgyczDtT5gCnAC9fMFnqpujrMHR8m/BZeDjE4W6wY0b+vcoSYsLkGanxyen3NuJ03nHcJ4kXqD1mOHgN475GU8b7xQzvR9MyL0uBguf3Hj96Ma/2bjPv6WhdnsWsBNY8em/TuCYM2wL8ISHjzpDs40h+BRfJ4jMTaq5gir1z/jVs8OYZFfw02Ld9AN8PqzbtOWeCDvw2cA/52VjatETy8O/U6tPdt35TROsfyvX14yHbiTa1/lOJxzb0C3uBSEbwKpXgmwAO7MCDHjtp/UUgl77maTaA9sve6v2LVdmhs9NHAavgFcdOuknnZdrA5yYkWs1LgBfYJiMA7btM6e/FurLvMk3HYe6y72fqLnauaEGEe5auw499Hwx+2fe6dy5/gvZi7r2XjmfYrwxcu4cO/+yaRyBbbu9tZBWZrJw/Xmzbu+FtEl83H4rSnl+KTaXnnQ3qwk9khn/6p4yDmqJj8LdbPyqo8HwZvFOe/kpTiStSq8ex4x3Zb9DlVyHDSoj0cnn6cRlN/C0Nwu42s87xcmw1XlXj2PGvLLfAc47eSppD6LrMu9Gld2k513rTT4j2ckM7MQltxzOXJtFFe2tpeI836q17GuFvsfWUp7A2YPouljLUWU3VWspS8TsQXa12dkryU3WWrbe0TeSmswhTlxqAzpYmdR0HywDU8bES+l1Hm05ETLpPoVX+LQrRc6bdZHpdOkma9cT+ZclmTVgUD7EIOKh9YgOBonVCsXZNp0b4IyyJI1xTpnwRfht7Idrf+sGHP1cO83txYy0CiJfc4W2CKtAmIKE6gyoTs+v4HO8buKDwdsjEp2C0iMpiQKihGV4kBplIN/BFUr5qc8A+lS4LU3TVCwcGh9V6kmJ8rEAFWLKe1EgYMHUj/7QpOgMJ1tjjaI8E7dB46nPcPbHVH+mYX2U3/VNy/xAizO24gAMkMkKbBIWSPnt5aSW1mMq0YALa2M1Gn1d3Xz7B/+eTvMTiz3pEH3SJODQqyOT3SNqXX8UN7cY7VqMojwTXwetRlOf4dZBNC37sA7K8dVeB03A/EAujK04AANk4sAKCU5FgSa/DhpTiQZcBxmr0UTWQfn6bR/skBgS0sUHYIP0I79JWCCiOA32Z9xAfizFGSyIN1SckUN47bu9+Efnmh8770mB6OQVAYdeV9CyJwOsq5DiejZwONUFG2Pp0MTjsPG0aLg4zFSNxo7D9K+bm5YxglbSbMUBmCOTdfSU7NH+RGVj6tKAUZmxNo0elWleiTgtoyQuseniAzBI+kvsKZmjQn0mHaGNpT6DRWiG6jOlCG0lv6JzWuYnR1REgRQfgPkpKNk380P0p8H+iMID5XZg+qMv0WH1J285sv6wt9B2tDvHR0fCS6s8lPJ2CQFYVXEANkT+ctw0rQh9w+wAOlDceyGAIsUHIH/Ze3YjS5+7hZagINwYK6gAuWyY2U2aOfX3Gtw+X3Hj7MXMu8v0pfjio3qxYq4GXkXDAvSqBgJfv4zQAJ+scwXopByCXRy+NEImHkyATMohyIXTboRM5pkAubwRGoCcV2lCrgwvPEBVLR2nen5Tazgyy+HBSKV0KFKvp0RSReXqFQqlq7ZMtjOsXKrRqAaaY8nnCVstHU17xuRKKiWNqZXquC5peWM5aWy1dDQz0mQWga6UDqVpG5jDB1jzVTykGmiOJechWy0dTZuH9O4BPJiCh3W95kAyI0tXSgeSmFv29natjwwdqg9rU/ReU4K2p8W3UIE1gfTbL+lLqkKAxpLbnhUk1NRnBfzWAHB0SNECEzESI8CPugD6mz/+gjYF+fczlUQD/kZ4VdU2wXKBN3+wZEHOQ5LMn1FKaW54O8uGlCEDwVbYJrpJ0A1vclkwbkMRXuzANM9osJ0cfcZL1W8vKkkGllnCa6u2CZYLGmxne0YPSbLGjF7pPGZqQ8qQYrMVtoluEnT/M3pYwtlEWynZinxcO5IWjT5dbJfYJhkrckg7O6ohiFUlNAJEa+c/Qpuc/PtaSuKB4E54i7Un8uUy107ds2DHR2CAhjnXTzqzowGQcWMremKBphL0b+NHYQOTttPEA3mOjx0dEE0gXdwH4ZrCl2endLb9gxOeexXtmc+27kHm4uKALu6FdE2hs63tLm8GI517JlVCsuIY246UuUMS8YlWS+Qyb31KiJWe1tohlTmg4d8Y1SdTePSyqlvMiy1aUoD/FR63xIecO9x7U1wFhw9ZE/+hBpG91xmiPBOlBlq2uQ7vo/KclcOobAK80OHhs89sqXLvrlNcvUZJkkv1qxvscJN3mzvkXYefdul2l2KS0eYueKGZkZ3WqsZfzAWcF5/yB0ESGyRgNP3s1YVP4dudH3gV3u+Bi/YkILJjYPIeRCZLfKqcogdMI4H0McpemtMBRNhXnV6v0Gab2aPkU7h0nzAQc9y+JOgDenDXL7g8f6NUDqRZECzbF1e++xC7m4TAqPvjf7EOe5vnn/4HAAD//wMA9+pQyAznAAA=</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>