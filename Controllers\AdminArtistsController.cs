﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using stockhom_soundrom_mvc.ViewModels;
using stockhom_soundrom_mvc.Models;
using Microsoft.AspNetCore.Http;
using System.IO;
using Microsoft.AspNetCore.Hosting;
using System.Data.Entity;

namespace stockhom_soundrom_mvc.Controllers
{
    [TypeFilter(typeof(SessionAuthorizationFilter))]
    public class AdminArtistsController : Controller
    {


        private readonly IWebHostEnvironment _env;

        public AdminArtistsController(IWebHostEnvironment env)
        {
            _env = env;
        }

        public async Task<IActionResult> Index()
        {
            //temp
        


            //



            return View(new AdminArtistViewModel());
        }


        public IActionResult DeleteArtist(int artist_id)
        {
            using (Data data = new Data())
            {
                var artistToBeRemoved = data.Artists.Where(a => a.Id == artist_id).Single();
                data.Artists.Remove(artistToBeRemoved);
                data.SaveChanges();
            }

            return View("Index", new AdminArtistViewModel());
        }





        public IActionResult NewAddSection()
        {
            AdminArtistViewModel adminArtistViewModel = new AdminArtistViewModel();

            using (Data data = new Data())
            {
                adminArtistViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminArtistViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminArtistViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminArtistViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminArtistViewModel.Artist = new Artist();
                adminArtistViewModel.AddSectionVisible = true;
            }

            return View("index", adminArtistViewModel);
        }




        [HttpPost]
        public ActionResult Save(AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            //do data validation



            using (var data = new Data())
            {
                var artist = GenerateArtistFromAdminArtistViewModel(data, adminArtistViewModel, pic_vertical_upload, pic_horizontal_upload, State.Saved);
                data.Artists.Add(artist);
                data.SaveChanges();
            }

            return RedirectToAction(nameof(Index));




        }

        [HttpPost]
        public ActionResult Preview(AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {

            ArtistViewModel artistViewModel = new ArtistViewModel();
            using (Data data = new Data())
            {
                artistViewModel.Artist = GenerateArtistFromAdminArtistViewModel(data, adminArtistViewModel, pic_vertical_upload, pic_horizontal_upload, State.ToBePublished);
            }
            return View(@"~/Views/Artist/Index.cshtml", artistViewModel);
        }


        [HttpPost]
        public ActionResult Publish(AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            //do data validation



            using (Data data = new Data())
            {
                var Artist = GenerateArtistFromAdminArtistViewModel(data, adminArtistViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);

                if (Artist.PublishedDate == DateTime.MinValue) // means the publish day is not set
                {
                    Artist.PublishedDate = DateTime.Now;
                }

                if (Artist.PublishedDate >= DateTime.Now.Date)
                {
                    Artist.State = State.ToBePublished;
                }
                else
                {
                    Artist.State = State.Published;
                }

                data.Artists.Add(Artist);
                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }

        [HttpPost]
        public ActionResult EditPublish(int artist_id, AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            //do data validation



            using (Data data = new Data())
            {
                var Artist = GenerateArtistFromAdminArtistViewModel(data, adminArtistViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);

                var existingArtist = data.Artists.Where(a => a.Id == artist_id)
                    .Include(t => t.Tracks)
                    .Include(t => t.Journals)
                    .Include(t => t.Styles)
                    .Include(t => t.Videos)
                    .Single();

                if (string.IsNullOrEmpty(Artist.VerticalImageUrl))
                {
                    Artist.VerticalImageUrl = existingArtist.VerticalImageUrl;
                }
                if (string.IsNullOrEmpty(Artist.HorizontalImageUrl))
                {
                    Artist.HorizontalImageUrl = existingArtist.HorizontalImageUrl;
                }

                //all the nesated objects that has to be removed
                existingArtist.Tracks.Clear();
                existingArtist.Videos.Clear();
                existingArtist.Styles.Clear();
                existingArtist.Journals.Clear();

                data.SaveChanges();

                existingArtist.ArtistName = Artist.ArtistName;
                existingArtist.Description = Artist.Description;
                existingArtist.HorizontalImageUrl = Artist.HorizontalImageUrl;
                existingArtist.VerticalImageUrl = Artist.VerticalImageUrl;
                existingArtist.Journals = Artist.Journals;
                existingArtist.MetaDescription = Artist.MetaDescription;
                existingArtist.MetaTitle = Artist.MetaTitle;
                existingArtist.PublishedDate = Artist.PublishedDate;
                existingArtist.State = State.Published;
                existingArtist.Styles = Artist.Styles;
                existingArtist.Tracks = Artist.Tracks;
                existingArtist.Videos = Artist.Videos;

                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }


        [HttpPost]
        public ActionResult EditSave(int artist_id, AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload)
        {
            //do data validation



            using (Data data = new Data())
            {
                var Artist = GenerateArtistFromAdminArtistViewModel(data, adminArtistViewModel, pic_vertical_upload, pic_horizontal_upload, State.Published);

                var existingArtist = data.Artists.Where(a => a.Id == artist_id)
                    .Include(t => t.Tracks)
                    .Include(t => t.Journals)
                    .Include(t => t.Styles)
                    .Include(t => t.Videos)
                    .Single();

                if (string.IsNullOrEmpty(Artist.VerticalImageUrl))
                {
                    Artist.VerticalImageUrl = existingArtist.VerticalImageUrl;
                }
                if (string.IsNullOrEmpty(Artist.HorizontalImageUrl))
                {
                    Artist.HorizontalImageUrl = existingArtist.HorizontalImageUrl;
                }

                //all the nesated objects that has to be removed
                existingArtist.Tracks.Clear();
                existingArtist.Videos.Clear();
                existingArtist.Styles.Clear();
                existingArtist.Journals.Clear();

                data.SaveChanges();

                existingArtist.ArtistName = Artist.ArtistName;
                existingArtist.Description = Artist.Description;
                existingArtist.HorizontalImageUrl = Artist.HorizontalImageUrl;
                existingArtist.VerticalImageUrl = Artist.VerticalImageUrl;
                existingArtist.Journals = Artist.Journals;
                existingArtist.MetaDescription = Artist.MetaDescription;
                existingArtist.MetaTitle = Artist.MetaTitle;
                existingArtist.PublishedDate = Artist.PublishedDate;
                existingArtist.State = State.Saved;
                existingArtist.Styles = Artist.Styles;
                existingArtist.Tracks = Artist.Tracks;
                existingArtist.Videos = Artist.Videos;

                data.SaveChanges();
            }

            return RedirectToAction("Index");
        }

        public ActionResult ViewForUpdate(AdminArtistViewModel adminArtistViewModel, int id)
        {
            using (Data data = new Data())
            {


                adminArtistViewModel.Artist = data.Artists.Where(a => a.Id == id)
                    .Include(x => x.Journals)
                    .Include(x => x.Styles)
                    .Include(x => x.Videos)
                    .Include(x => x.Tracks)
                    .First();
                adminArtistViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminArtistViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminArtistViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminArtistViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminArtistViewModel.ViewForUpdate = true;
                adminArtistViewModel.AddSectionVisible = true;

                return View("index", adminArtistViewModel);
            }


        }


        public ActionResult ViewForUpdate2(int id)
        {
            using (Data data = new Data())
            {
                AdminArtistViewModel adminArtistViewModel = new AdminArtistViewModel();

                adminArtistViewModel.Artist = data.Artists.Where(a => a.Id == id)
                    .Include(x => x.Journals)
                    .Include(x => x.Styles)
                    .Include(x => x.Videos)
                    .Include(x => x.Tracks)
                    .First();
                adminArtistViewModel.AllJournals = data.Journals.AsEnumerable().Select(jrnl => new Journal() { Id = jrnl.Id, Headline = jrnl.Headline }).ToList();
                adminArtistViewModel.AllStyles = data.Styles.AsEnumerable().Select(style => new Style() { Id = style.Id, StyleTitle = style.StyleTitle }).ToList();
                adminArtistViewModel.AllTracks = data.Tracks.AsEnumerable().Select(trak => new Track() { Id = trak.Id, TrackTitle = trak.TrackTitle }).ToList();
                adminArtistViewModel.AllVideos = data.Videos.AsEnumerable().Select(video => new Video() { Id = video.Id, VideoTitle = video.VideoTitle }).ToList();
                adminArtistViewModel.ViewForUpdate = true;
                adminArtistViewModel.AddSectionVisible = true;

                return View("index", adminArtistViewModel);
            }


        }

        private Artist GenerateArtistFromAdminArtistViewModel(Data data, AdminArtistViewModel adminArtistViewModel, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, State state)
        {
            try
            {
                Artist artist = new Artist();

                //control bounded data
                artist.ArtistName = adminArtistViewModel.Artist.ArtistName;
                artist.Description = adminArtistViewModel.Artist.Description;
                artist.MetaTitle = adminArtistViewModel.Artist.MetaTitle;
                artist.MetaDescription = adminArtistViewModel.Artist.MetaDescription;
                artist.PublishedDate = adminArtistViewModel.Artist.PublishedDate;
                artist.State = state;


                //2 images
                if (pic_vertical_upload != null && pic_vertical_upload.Length > 0)
                {
                    artist.VerticalImageUrl = saveIFromFile(pic_vertical_upload);
                }
                if (pic_horizontal_upload != null && pic_horizontal_upload.Length > 0)
                {
                    artist.HorizontalImageUrl = saveIFromFile(pic_horizontal_upload);
                }




                //request form data
                var relatedTracks = Request.Form[Global.RelatedTracksDDLName];
                foreach (var relatedTrackIdString in relatedTracks)
                {
                    int trackId = int.Parse(relatedTrackIdString);
                    var relatedTrack = data.Tracks.Where(t => t.Id == trackId).FirstOrDefault();
                    artist.Tracks.Add(relatedTrack);

                }

                var relatedVideos = Request.Form[Global.RelatedVideosDDLName];
                foreach (var relatedVideoIdString in relatedVideos)
                {
                    int videoId = int.Parse(relatedVideoIdString);
                    var relatedVideo = data.Videos.Where(t => t.Id == videoId).FirstOrDefault();
                    artist.Videos.Add(relatedVideo);

                }

                var relatedStyles = Request.Form[Global.RelatedStylesDDLName];
                foreach (var relatedStypeIdString in relatedStyles)
                {
                    int styleId = int.Parse(relatedStypeIdString);
                    var relatedStyle = data.Styles.Where(t => t.Id == styleId).FirstOrDefault();
                    artist.Styles.Add(relatedStyle);

                }

                var relatedJournals = Request.Form[Global.RelatedJournalsDDLName];
                foreach (var relatedJournalIdString in relatedJournals)
                {
                    int journalId = int.Parse(relatedJournalIdString);
                    var relatedJournal = data.Journals.Where(t => t.Id == journalId).FirstOrDefault();
                    artist.Journals.Add(relatedJournal);

                }

                return artist;
            }
            catch (Exception ex)
            {
                return null;
            }
        }



        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }
    }


}