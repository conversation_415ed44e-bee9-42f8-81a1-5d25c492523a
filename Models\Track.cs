﻿using System;
using System.Collections.Generic;

namespace stockhom_soundrom_mvc.Models
{
    public class Track
    {
        public int Id { get; set; }
        public string TrackPath { get; set; }
        public string TrackTitle { get; set; }
        public List<Artist> RelatedArtists { get; set; } = new List<Artist>();
        public string CatalogNumber { get; set; }
        public string ISRC { get; set; }
        public string MainGenre { get; set; }
        public string SubGenre { get; set; }
        public string BPM { get; set; }
        public DateTime PublishDate { get; set; }
        public string ArtWorkSquare { get; set; }
        public string ArtWorkHoriontal { get; set; }
        public bool StartCountDown { get; set; }
        public bool TopPromo { get; set; }
        public List<TrackBuyLink> BuyLinks { get; set; } = new List<TrackBuyLink>();
        public string SpotifyLink { get; set; }
        public string AppleLink { get; set; }
        public string SoundCloudLink { get; set; }
        public string YoutubeMusicStreamLink { get; set; }
        public bool OfferFreeDownload { get; set; }
        public string YoutubeLink { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public string Description { get; set; }
        public List<Style> RelatedStyles { get; set; } = new List<Style>();
        public List<Journal> RelatedJournals { get; set; } = new List<Journal>();
        public List<Video> RelatedVideos { get; set; } = new List<Video>();
        public State State { get; set; }
        public int TrackNoInMusicCollection { get; set; }


    }
}
