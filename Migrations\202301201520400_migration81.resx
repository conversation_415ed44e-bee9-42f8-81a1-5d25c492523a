﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>