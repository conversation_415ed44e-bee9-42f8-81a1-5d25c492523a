﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>