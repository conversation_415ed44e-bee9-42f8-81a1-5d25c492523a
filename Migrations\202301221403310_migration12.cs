﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration12 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.BuyLinks", "Track_Id", "dbo.Tracks");
            DropForeignKey("dbo.BuyLinks", "Music_Id", "dbo.Musics");
            DropForeignKey("dbo.Tracks", "Music_Id", "dbo.Musics");
            DropIndex("dbo.Tracks", new[] { "Music_Id" });
            DropIndex("dbo.BuyLinks", new[] { "Track_Id" });
            DropIndex("dbo.BuyLinks", new[] { "Music_Id" });
            DropColumn("dbo.Tracks", "Music_Id");
            DropTable("dbo.BuyLinks");
            DropTable("dbo.Musics");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.Musics",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        MusicCollectionType = c.Int(nullable: false),
                        PublishDate = c.DateTime(nullable: false),
                        StartCountDown = c.<PERSON>(nullable: false),
                        TopPromo = c.Boolean(nullable: false),
                        ArtWorkSquare = c.String(),
                        ArtWorkHoriontal = c.String(),
                        YoutubeMusicAlbumLink = c.String(),
                        YoutubeAlbumLink = c.String(),
                        Description = c.String(),
                        MetaTitle = c.String(),
                        MetaDescription = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.BuyLinks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Link = c.String(),
                        Track_Id = c.Int(),
                        Music_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id);
            
            AddColumn("dbo.Tracks", "Music_Id", c => c.Int());
            CreateIndex("dbo.BuyLinks", "Music_Id");
            CreateIndex("dbo.BuyLinks", "Track_Id");
            CreateIndex("dbo.Tracks", "Music_Id");
            AddForeignKey("dbo.Tracks", "Music_Id", "dbo.Musics", "Id");
            AddForeignKey("dbo.BuyLinks", "Music_Id", "dbo.Musics", "Id");
            AddForeignKey("dbo.BuyLinks", "Track_Id", "dbo.Tracks", "Id");
        }
    }
}
