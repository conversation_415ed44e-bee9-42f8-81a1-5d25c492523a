﻿using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using stockhom_soundrom_mvc.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Data.Entity;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using System.IO;
using Microsoft.AspNetCore.Hosting;

namespace stockhom_soundrom_mvc.Controllers
{
    public class StartPageController : Controller
    {
        private readonly IWebHostEnvironment _env;

        public StartPageController(IWebHostEnvironment env)
        {
            _env = env;
        }


        public IActionResult Index()
        {
            //find whether there is a track with start count down
            StartPageViewModel startPageViewModel = new StartPageViewModel();



            using (Data data = new Data())
            {
                var topPromoTrack = data.Tracks.Where(t => t.TopPromo == true && t.State != State.Saved).Include(t => t.RelatedArtists).FirstOrDefault();

                startPageViewModel.TopPromoTrack = topPromoTrack;

                if (topPromoTrack != null)
                {
                    if (topPromoTrack.StartCountDown == true && topPromoTrack.PublishDate > DateTime.Now)
                    {
                        startPageViewModel.IsCountDown = true;

                        startPageViewModel.CountdownStartTime = topPromoTrack.PublishDate - DateTime.Now;
                    }

                    startPageViewModel.IsTrackPromo = true;


                }


                //var presentation = data.Presentations.FirstOrDefault();
                //if (presentation != null)
                //{
                //    startPageViewModel.Merch_hor_image = presentation.HorizontalMerchImage;
                //    startPageViewModel.Merch_ver_image = presentation.SquareMerchImage;
                //}


                var styles = data.Styles.ToList();

                foreach (var style in styles)
                {
                    startPageViewModel.Styles = styles;
                }


                startPageViewModel.Journals = data.Journals.Where(j => j.StartPageMaterial == true
                && j.State != State.Saved).Take(6).ToList();

            }


            //temp

            //startPageViewModel.IsCountDown = false;

            //temp
            return View(startPageViewModel);
        }

        [HttpPost]
        public  bool EmailExists(string param)
        {
            using (Data data = new Data())
            {
                if (data.Emails.Any(e => e.EmailAddress == param.Trim()))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }



        public class MyDataModel
        {
            public string email { get; set; }
            public string country { get; set; }
        }


        [HttpPost]
        public  bool SaveData([FromBody] MyDataModel datamodel)
        {
            try
            {
                using (Data data = new Data())
                {
                    //if datamodel.country is null of empty
                    //its a submission from the normal page ( not the prompt)
                    if (string.IsNullOrEmpty(datamodel.country))
                    {
                        data.Emails.Add(new Email() { EmailAddress = datamodel.email.Trim() , SavedTime = DateTime.Now });
                        data.SaveChanges();
                    }
                    else //we come to here only if datamode.country 
                         //is not not null or empty which can only happen when its
                         //submitted from the prompt
                    {
                        if (EmailExists(datamodel.email))
                        {
                            var existingEmail = data.Emails.Where(e => e.EmailAddress == datamodel.email.Trim()).FirstOrDefault();
                            existingEmail.Country = datamodel.country.Trim();
                            data.SaveChanges();
                        }
                        else
                        {
                            data.Emails.Add(new Email() { EmailAddress = datamodel.email.Trim(), Country = datamodel.country , SavedTime = DateTime.Now });
                            data.SaveChanges();
                        }
                    }
                    return true;
                }
            }
            catch(Exception ex)
            {
                return false;
            }
        }


        [HttpPost]
        public IActionResult PreviewParty(IFormCollection form , IFormFile picture1, IFormFile picture2 , AdminPartyViewModel adminPartyViewModel)
        {

            StartPageViewModel startPageViewModel = new StartPageViewModel();
            startPageViewModel.IsCountDown = false;
            startPageViewModel.previewParty = true;

            using (Data data = new Data())
            {
                var topPromoTrack = data.Tracks.Where(t => t.TopPromo == true && t.State != State.Saved).Include(t => t.RelatedArtists).FirstOrDefault();

                startPageViewModel.TopPromoTrack = topPromoTrack;

                if (topPromoTrack != null)
                {
                    if (topPromoTrack.StartCountDown == true && topPromoTrack.PublishDate > DateTime.Now)
                    {
                        startPageViewModel.IsCountDown = true;

                        startPageViewModel.CountdownStartTime = topPromoTrack.PublishDate - DateTime.Now;
                    }

                    startPageViewModel.IsTrackPromo = true;


                }




                var styles = data.Styles.ToList();

                foreach (var style in styles)
                {
                    startPageViewModel.Styles = styles;
                }


                startPageViewModel.Journals = data.Journals.Where(j => j.StartPageMaterial == true
                && j.State != State.Saved).Take(6).ToList();

            }


            if (picture1 != null && picture1.Length > 0) // square
            {
                startPageViewModel.Party_sqr_image = saveIFromFile(picture1);
            }
            else
            {
                startPageViewModel.Party_sqr_image = adminPartyViewModel.Party.SquarePoster;

            }

            if (picture2 != null && picture2.Length > 0) // hor
            {
                startPageViewModel.party_hor_image = saveIFromFile(picture2);

            }
            else
            {
                startPageViewModel.party_hor_image = adminPartyViewModel.Party.HorizontalPoster;

            }




            return View("Index",startPageViewModel);
        }


        string saveIFromFile(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);

                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }
        }

    }
}
