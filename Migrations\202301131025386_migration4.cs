﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration4 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.Journals", "Artist_Id", "dbo.Artists");
            DropIndex("dbo.Journals", new[] { "Artist_Id" });
            CreateTable(
                "dbo.JournalArtists",
                c => new
                    {
                        Journal_Id = c.Int(nullable: false),
                        Artist_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Journal_Id, t.Artist_Id })
                .ForeignKey("dbo.Journals", t => t.Journal_Id, cascadeDelete: true)
                .ForeignKey("dbo.Artists", t => t.Artist_Id, cascadeDelete: true)
                .Index(t => t.Journal_Id)
                .Index(t => t.Artist_Id);
            
            DropColumn("dbo.Journals", "Artist_Id");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Journals", "Artist_Id", c => c.Int());
            DropForeignKey("dbo.JournalArtists", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.JournalArtists", "Journal_Id", "dbo.Journals");
            DropIndex("dbo.JournalArtists", new[] { "Artist_Id" });
            DropIndex("dbo.JournalArtists", new[] { "Journal_Id" });
            DropTable("dbo.JournalArtists");
            CreateIndex("dbo.Journals", "Artist_Id");
            AddForeignKey("dbo.Journals", "Artist_Id", "dbo.Artists", "Id");
        }
    }
}
