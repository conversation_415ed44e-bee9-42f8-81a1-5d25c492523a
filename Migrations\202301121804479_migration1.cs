﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration1 : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Artists",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        VerticalImageUrl = c.String(),
                        HorizontalImageUrl = c.String(),
                        ArtistName = c.String(),
                        Description = c.String(),
                        MetaTitle = c.String(),
                        MetaDescription = c.String(),
                        PublishedDate = c.String(),
                        State = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.Journals",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DesktopImageUrl = c.String(),
                        MobileImageUrl = c.String(),
                        Headline = c.String(),
                        MetaTitle = c.String(),
                        MetaDescription = c.String(),
                        BodyText = c.String(),
                        PublishDate = c.DateTime(nullable: false),
                        StartPageMaterial = c.Bo<PERSON>an(nullable: false),
                        State = c.Int(nullable: false),
                        Artist_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Artists", t => t.Artist_Id)
                .Index(t => t.Artist_Id);
            
            CreateTable(
                "dbo.Styles",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        StyleTitle = c.String(),
                        Picture1 = c.String(),
                        Picture2 = c.String(),
                        Picture3 = c.String(),
                        ReleaseDate = c.DateTime(nullable: false),
                        ExternalLink = c.String(),
                        State = c.Int(nullable: false),
                        Journal_Id = c.Int(),
                        Video_Id = c.Int(),
                        Artist_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Journals", t => t.Journal_Id)
                .ForeignKey("dbo.Videos", t => t.Video_Id)
                .ForeignKey("dbo.Artists", t => t.Artist_Id)
                .Index(t => t.Journal_Id)
                .Index(t => t.Video_Id)
                .Index(t => t.Artist_Id);
            
            CreateTable(
                "dbo.Tracks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Journal_Id = c.Int(),
                        Video_Id = c.Int(),
                        Artist_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Journals", t => t.Journal_Id)
                .ForeignKey("dbo.Videos", t => t.Video_Id)
                .ForeignKey("dbo.Artists", t => t.Artist_Id)
                .Index(t => t.Journal_Id)
                .Index(t => t.Video_Id)
                .Index(t => t.Artist_Id);
            
            CreateTable(
                "dbo.Videos",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        YoutubeLink = c.String(),
                        VideoTitle = c.String(),
                        VerticalPosterUrl = c.String(),
                        HorizontalPosterLinkUrl = c.String(),
                        PublishDate = c.DateTime(nullable: false),
                        Description = c.String(),
                        MetaTitle = c.String(),
                        MetaDescription = c.String(),
                        State = c.Int(nullable: false),
                        Journal_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Journals", t => t.Journal_Id)
                .Index(t => t.Journal_Id);
            
            CreateTable(
                "dbo.VideoArtists",
                c => new
                    {
                        Video_Id = c.Int(nullable: false),
                        Artist_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Video_Id, t.Artist_Id })
                .ForeignKey("dbo.Videos", t => t.Video_Id, cascadeDelete: true)
                .ForeignKey("dbo.Artists", t => t.Artist_Id, cascadeDelete: true)
                .Index(t => t.Video_Id)
                .Index(t => t.Artist_Id);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Tracks", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.Styles", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.Journals", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.Videos", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.Tracks", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.Styles", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.VideoArtists", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.VideoArtists", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.Tracks", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.Styles", "Journal_Id", "dbo.Journals");
            DropIndex("dbo.VideoArtists", new[] { "Artist_Id" });
            DropIndex("dbo.VideoArtists", new[] { "Video_Id" });
            DropIndex("dbo.Videos", new[] { "Journal_Id" });
            DropIndex("dbo.Tracks", new[] { "Artist_Id" });
            DropIndex("dbo.Tracks", new[] { "Video_Id" });
            DropIndex("dbo.Tracks", new[] { "Journal_Id" });
            DropIndex("dbo.Styles", new[] { "Artist_Id" });
            DropIndex("dbo.Styles", new[] { "Video_Id" });
            DropIndex("dbo.Styles", new[] { "Journal_Id" });
            DropIndex("dbo.Journals", new[] { "Artist_Id" });
            DropTable("dbo.VideoArtists");
            DropTable("dbo.Videos");
            DropTable("dbo.Tracks");
            DropTable("dbo.Styles");
            DropTable("dbo.Journals");
            DropTable("dbo.Artists");
        }
    }
}
