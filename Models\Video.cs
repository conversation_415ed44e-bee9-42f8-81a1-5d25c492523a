﻿using System;
using System.Collections.Generic;

namespace stockhom_soundrom_mvc.Models
{
    public class Video
    {
        public int Id { get; set; }
        public string YoutubeLink { get; set; }
        public string VideoTitle { get; set; }
        public List<Artist> RelatedArtists { get; set; } = new List<Artist>();
        public string VerticalPosterUrl { get; set; }
        public string HorizontalPosterLinkUrl { get; set; }
        public DateTime PublishDate { get; set; }
        public List<Track> RelatedTracks { get; set; } = new List<Track>();
        public List<Style> RelatedStyle { get; set; } = new List<Style>();
        public List<Journal> RelatedJournals { get; set; } = new List<Journal>();
        public string Description { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public State State { get; set; }

    }
}
