﻿@model stockhom_soundrom_mvc.ViewModels.AdminStylesViewModel
@using stockhom_soundrom_mvc.Controllers
@using System.IO
@using System.Linq;
@using System.Data.Entity;

@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}



@section Styles{


    <link rel="stylesheet" href="~/css/chosen.min.css" />

    <style>
        body {
            background-color: black;
        }

        form {
            position: relative;
        }

        #add_entity {
            position: relative;
            padding: 50px;
        }


            #add_entity .close-button {
                position: absolute;
                top: 0;
                right: 50px;
                cursor: pointer;
                background-color: black;
                border: solid;
                color: white;
            }

        #lbl_entity {
            color: white;
        }


        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }



        .profile_container {
            width: 800px;
        }

        .profile_box {
            width: 150px;
            height: 280px;
            display: inline-block;
            text-align: center;
            margin: 14px;
        }

            .profile_box img {
                height: 200px;
                width: 150px;
                cursor: pointer;
                transition: all 0.5s ease;
            }

                .profile_box img:hover {
                    transform: scale(1.1);
                }

            .profile_box .entity_name {
                width: 150px;
                text-align: center;
                color: white;
                margin-bottom: 3px;
                display: block;
            }

            .profile_box .state {
                border: solid;
                color: white;
                display: block;
                font-size: smaller;
            }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }


        .button_container {
            /*margin-left: 50px;*/
            margin-top: 20px;
        }

        #pic_vertical {
            width: 130px;
            height: 200px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
        }

            #pic_vertical:hover {
                background-color: #5d6952;
            }

        #pic_horizontal {
            width: 200px;
            height: 130px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            display: inline-block;
            background-size: 100% 100%;
        }

            #pic_horizontal:hover {
                background-color: #5d6952;
            }




        .plus {
            font-size: 73px;
            text-align: center;
            margin: auto;
            color: white;
            font-weight: 900;
        }


        .add_another {
            display: inline;
            text-align: right;
            color: white;
            position: absolute;
            right: 35px;
            font-size: x-small;
        }

            .add_another:hover {
                color: blue;
            }

        .select_related {
            background-color: black;
            color: white;
            margin: 3px;
            position: relative;
        }

        /*.select_related .close-button {
        position: absolute;
        top: 0;
        right: 0;
        display: none;
        }

        .select_related:hover .close-button {
        display: block;
        }

        .close-button {*/
        /* style the close button, such as color, size, etc. */
        /*color:red;
        }*/


        .parent {
            z-index: 1;
            background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
        }

        .child {
            position: absolute;
            z-index: 1;
        }



        .remove {
            position: absolute;
            top: 0px;
            right: 0px;
            display: block;
            box-sizing: border-box;
            width: 20px;
            height: 20px;
            border-width: 3px;
            border-style: solid;
            border-color: red;
            border-radius: 100%;
            background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
            background-color: red;
            box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
            transition: all 0.3s ease;
        }


        .tag {
            padding: 5px;
            background: aliceblue;
            border: black 2px solid;
            border-radius: 0px 15px 15px 0px;
            display: inline-block;
            cursor: pointer;
            color: black;
        }

        #tagContainer {
        }

        #taginputText {
            border: none;
            background: none;
        }

        #container {
            background: white;
            padding: 10px;
        }


        .pic {
            width: 130px;
            height: 200px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
        }
    </style>



}


@{
    var add_artist_style = (Model.AddSectionVisible == true) ? "display:block" : "display:none";
}


<div class="container">
    <div style="/*margin-left: 50px; */ margin-bottom: 20px">
        <label id="lbl_entity">Styles</label>
        <input type="button" onclick="location.href='@Url.Action("NewAddSection", "AdminStyles")'" class="btn1" value="Add Style" id="btn_add_artist" />

    </div>

    <div style=@add_artist_style id="add_entity">
        <button id="add_entity_close_btn" class="close-button">X</button>

        <div style="/*margin-left: 50px; */ margin-bottom: 20px; font-size: large; color: white;">
            Style
        </div>

        <form id="form" method="post" enctype="multipart/form-data">

            <div class="infobox">
                <label>Style Title</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Style.StyleTitle, new { id = "style_title" })
            </div>
            <label id="style_title_url" style="color: white; display: block;"></label>


            <div style="display: inline-block; width: 300px;" class="infobox">
                <label>Price €</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Style.Price)
            </div>

            <div style="width: 300px; display: inline-block; margin-left: 30px;" class="infobox">
                <label>Fan's Price €</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Style.FanPrice)
            </div>



            <div class="infobox">
                <label>Colors</label>
                <span id="tagContainer1">
                    @if (Model.ViewForUpdate)
                    {
                        using (Data data = new Data())
                        {
                            var colors = data.Styles.Where(s => s.Id == Model.Style.Id).Include(s => s.Colors)
                                 .FirstOrDefault().Colors.ToList();

                            foreach (var color in colors)
                            {
                                <div class="tag"><input type="hidden" value="@color.ColorString" name="colors">@color.ColorString</div>
                            }
                        }
                    }

                </span>
                <input type="text" id="taginputText1" />

            </div>


            <div class="infobox">
                <label>Sizes</label>
                <span id="tagContainer2">
                    @if (Model.ViewForUpdate)
                    {
                        using (Data data = new Data())
                        {
                            var sizes = data.Styles.Where(s => s.Id == Model.Style.Id).Include(s => s.Sizes)
                                 .FirstOrDefault().Sizes.ToList();

                            foreach (var size in sizes)
                            {
                                <div class="tag"><input type="hidden" value="@size.SizeString" name="sizes">@size.SizeString</div>
                            }
                        }
                    }
                </span>
                <input type="text" id="taginputText2" />
            </div>


            <div style="margin-bottom:15px" class="infobox">
                <label>Catagory</label>
                <span id="tagContainer3">
                    @if (Model.ViewForUpdate)
                    {
                        using (Data data = new Data())
                        {
                            var catagories = data.Styles.Where(s => s.Id == Model.Style.Id).Include(s => s.Catagories)
                                 .FirstOrDefault().Catagories.ToList();

                            foreach (var catagory in catagories)
                            {
                                <div class="tag"><input type="hidden" value="@catagory.CatagoryString" name="catagories">@catagory.CatagoryString</div>
                            }
                        }
                    }
                </span>
                <input type="text" id="taginputText3" />
            </div>



            <div id="pic_1" class="pic" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Style.Picture1));</text> } }" onclick="picture1.click();">
                <label style="position: relative; top: 5px; left: 5px;">Picture</label>
                <div class="plus">+</div>
            </div>
            <input id='picture1' name="picture1" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_2" class="pic" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Style.Picture2));</text> } }" onclick="picture2.click();">
                <label style="position: relative; top: 5px; left: 5px;">Picture</label>
                <div class="plus">+</div>
            </div>
            <input id='picture2' name="picture2" type='file' value="" style='display: none' accept='.jpg' />

            <div id="pic_3" class="pic" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Style.Picture3));</text> } }" onclick="picture3.click();">
                <label style="position: relative; top: 5px; left: 5px;">Picture</label>
                <div class="plus">+</div>
            </div>
            <input id='picture3' name="picture3" type='file' value="" style='display: none' accept='.jpg' />


            <div class="infobox" style="width: 300px">
                <label>Release Date</label>
                @*<input type="date" value="@Model.NewArtist.PublishedDate" id="published_date" style="color-scheme: dark;" />*@
                @Html.TextBoxFor(m => m.Style.ReleaseDate, new { type = "datetime-local", style = "color-scheme: dark;" })

            </div>


            <div class="infobox">
                <label>External Link</label>
                @Html.TextBoxFor(m => m.Style.ExternalLink)
            </div>




            <div class="infobox" id="infobox_related_videos">

                <label>Related Youtube Video</label>

                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var video in Model.Style.Videos)
                        {
                            <select class="select_related" name=@Global.RelatedVideosDDLName>
                                @foreach (var option in Model.AllVideos)
                                {
                                    if (video.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.VideoTitle}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllVideos)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                        }
                    </select>
                }

            </div>
            <label id="add_another_related_video" class="add_another">Add another related video</label>







            <div class="infobox" id="infobox_related_journal_entry">
                <label>Related Journal Entry</label>

                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var journal in Model.Style.Journals)
                        {
                            <select class="select_related" name=@Global.RelatedJournalsDDLName>
                                @foreach (var option in Model.AllJournals)
                                {
                                    if (journal.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.Headline}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllJournals)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                        }
                    </select>
                }


            </div>
            <label id="add_another_related_journal_entry" class="add_another">Add another related journal entry</label>



            <div class="infobox" id="infobox_related_tracks">
                <label>Related Track</label>
                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var track in Model.Style.Tracks)
                        {
                            <select class="select_related" name=@Global.RelatedTracksDDLName>
                                @foreach (var option in Model.AllTracks)
                                {
                                    if (track.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.TrackTitle}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.TrackTitle}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllTracks)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.TrackTitle}</option>")
                        }

                    </select>
                }

            </div>
            <label type="submit" id="add_another_related_tracks" class="add_another">Add another related track</label>



            <div class="infobox" style="height: 160px">
                <label>Description</label>
                @Html.TextAreaFor(m => m.Style.Description)
            </div>



            <div class="button_container">

                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditPublish","AdminStyles" , new { style_id = Model.Style.Id})" value="Publish Style" class="btn1" />
                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Publish","AdminStyles")" value="Publish Style" class="btn1" />
                }

                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditSave","AdminStyles" , new { style_id = Model.Style.Id})" name="save" id="btn_save" value="save" class="btn1" />

                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Save","AdminStyles")" name="save" id="btn_save" value="save" class="btn1" />

                }

                

                @if (Model.Style != null)
                {
                    <input type="button" class="btn1" onclick="confirmDelete(@Model.Style.Id)" value="Delete" />
                }
               
            </div>

        </form>


    </div>


    <div class="profile_container">
        @using (Data data = new Data())
        {


            foreach (var style in data.Styles.ToList())
            {
                <div class="profile_box">

                    @{var imageSource = Url.Content($"~/Uploads/{style.Picture1}"); }
                    <a href="@Url.Action(@"ViewForUpdate",@"AdminStyles",new { id = style.Id })">
                        <img src=@imageSource />
                    </a>

                    @{ if (style.State == State.Saved)
                        {
                            <label class="state">Saved</label>
                        }
                        else
                        {
                            if (style.ReleaseDate > DateTime.Now)
                            {
                                <label draggable="false" class="state">@style.ReleaseDate</label>
                            }
                            else
                            {
                                <label style="visibility:hidden" class="state">Pl</label>
                            }
                        }
                    }
                </div>
            }
        }
    </div>
</div>



@section Scripts{



    <script src="~/js/chosen.jquery.min.js"></script>

    @if (Model.Style != null)
    {
        <script>


            var publishDate = '@Model.Style.ReleaseDate.ToString("yyyy-MM-ddTHH:mm:ss")';
            document.getElementById('Style_ReleaseDate').value = publishDate;

        </script>

    }



    <script type="text/javascript">

        var mouse_focused_related_select_element_id = null;

        window.onload = function () {


            $('select:not(.hidden_select)').each(function () {

                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();


                $(this).attr('id', uniqid);

                $(this).chosen();

                var generated_element_id = uniqid + "_chosen";

                //$(this).on('mouseover', function () {
                //    mouse_focused_related_select_element_id = $(this).attr('id');
                //    console.log('mouse over');
                //    console.log(mouseFocusedRelatedSelectElementId);
                //});


                //$(this).on("mouseout", function () {

                //    mouse_focused_related_select_element_id = null;
                //    console.log("mouse out");
                //});

                document.getElementById(generated_element_id).addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element_id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });


                document.getElementById(generated_element_id).addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });
        }

    </script>



    <script>
        var _hor_image = null;
        var _ver_image = null;

        var test1 = [];
        var videoss = "";
        var e = "";

        var mouse_focused_related_select_element_id = null;


        var LastRelatedTrackSelectElementPostFix = 0;
        var LastRelatedVidoeSelectElementPostFix = 0;
        var LastRelatedStyleSelectElementPostFix = 0;
        var LastRelatedJournalSelectElementPostFix = 0;


        document.onkeyup = function (e) {
            e = e || window.event;
            if (e.keyCode == "8") {


                if (mouse_focused_related_select_element_id != null) {
                    document.getElementById(mouse_focused_related_select_element_id).remove();
                    document.getElementById(mouse_focused_related_select_element_id.slice(0, -7)).remove();
                }

            }
        };


        function f3(infoboxElementId, selectElementName, clickingElementId) { //"clickingElement" could be a button

            document.getElementById(clickingElementId).addEventListener("click", function () {

                var select = document.createElement("select");
                select.name = selectElementName;
                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();
                select.id = uniqid;
                select.style.display = "inline";


                var hiddenselect = document.getElementById(infoboxElementId).querySelector(".hidden_select");
                for (var i = 0; i < hiddenselect.options.length; i++) {
                    var option = hiddenselect.options[i];
                    select.add(new Option(option.text, option.value));
                }

                document.getElementById(infoboxElementId).appendChild(select);

                $("#" + uniqid).chosen();


                //id of the generated element for the hidden select element

                var generated_element_id = uniqid + "_chosen";

                var generated_element = document.getElementById(generated_element_id);


                generated_element.addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element.id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });

                generated_element.addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });



        }

        f3("infobox_related_tracks", "select_related_tracks", "add_another_related_tracks");
        f3("infobox_related_videos", "select_related_videos", "add_another_related_video");
        f3("infobox_related_journal_entry", "select_related_journal_entry", "add_another_related_journal_entry");








        //when we input an id of a .infobox
        //this returns a collection of objects
        //containing id and text properties
        function f2(infobox_id) {

            const infobox = document.getElementById(infobox_id);
            const selectElements = infobox.querySelectorAll('select');


            let retobjs = [];

            for (const select of selectElements) {
                const selectedIndex = select.selectedIndex;
                const selectedOption = select.options[selectedIndex];
                const selectedText = selectedOption.text;
                const selectedValue = selectedOption.value;

                var obj = Object.assign({}, { id: selectedValue, text: selectedText });
                retobjs.push(obj);

            }

            return retobjs;

        }











        document.getElementById("add_entity_close_btn").addEventListener("click", function () {


            document.getElementById("add_entity").style.display = "none";

            //make element vlaues defualt

        });



        function refresh_profile_container() {

            //clear the inside of profile_container
            //get profile data from backend using ajax
        }



        function image_upload(input_file_elem_id, pic_display_div_id) {

            alert(input_file_elem_id);
            alert(pic_display_div_id);

            var fileInput = document.getElementById(input_file_elem_id);
            var file = fileInput.files[0];

            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById(pic_display_div_id);
            div.style.backgroundImage = 'url(' + fileUrl + ')';

        }

        document.getElementById("picture1").addEventListener("change", function () {
            image_upload('picture1', 'pic_1');
        }, false);

        document.getElementById("picture2").addEventListener("change", function () {
            image_upload('picture2', 'pic_2');
        }, false);

        document.getElementById("picture3").addEventListener("change", function () {
            image_upload('picture3', 'pic_3');
        }, false);


        function SaveImageAndReturnGuid(file) {

            var guid = null;


            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                type: "POST",
                url: "SaveImage.ashx",
                data: formData,
                contentType: false,
                processData: false,
                async: false,
                success: function (response) {
                    guid = response;
                },
                error: function (ex) {
                    test1 = ex;
                }
            });


            return guid;
        }






        //add .profile_boxes to .profile_container
        //for all the artists in the database







        function confirmDelete(id) {
            if (confirm("Are you sure you want to delete this?")) {

                window.location.href = "/AdminStyles/DeleteStyle?style_id=" + id;

            }
        }


    </script>

    


    <script>

        $(document).ready(function () {
            $("#taginputText1").keypress(function (event) {
                if (event.which == 13) {
                    event.preventDefault();
                } else if (event.which == 32) {
                    event.preventDefault();
                    $("#tagContainer1").append('<div class="tag" >' + '<input type="hidden" value="' + ($(this).val()) + '" name="colors" >' + ($(this).val()) + '</div>');
                    $(this).val("");
                }
            });
            $('#tagContainer1').on('click', '.tag', function () {
                $(this).remove();
            });
            $("#taginputText2").keypress(function (event) {
                if (event.which == 13) {
                    event.preventDefault();
                } else if (event.which == 32) {
                    event.preventDefault();
                    $("#tagContainer2").append('<div class="tag" >' + '<input type="hidden" value="' + ($(this).val()) + '" name="sizes" >' + ($(this).val()) + '</div>');
                    $(this).val("");
                }
            });
            $('#tagContainer2').on('click', '.tag', function () {
                $(this).remove();
            });
            $("#taginputText3").keypress(function (event) {
                if (event.which == 13) {
                    event.preventDefault();
                } else if (event.which == 32) {
                    event.preventDefault();
                    $("#tagContainer3").append('<div class="tag" >' + '<input type="hidden" value="' + ($(this).val()) + '" name="catagories" >' + ($(this).val()) + '</div>');
                    $(this).val("");
                }
            });
            $('#tagContainer3').on('click', '.tag', function () {
                $(this).remove();
            });
        });
    </script>

    <script>
        const styleTitleInput = document.querySelector("#style_title");

        styleTitleInput.addEventListener("keyup", function (event) {




            var raw = document.getElementById("style_title").value;

            console.log(raw);

            var slugged = raw.toLowerCase().replace(/ /g, '-')
                .replace(/[^\w-]+/g, '');

            console.log(slugged);



            document.getElementById("style_title_url").innerHTML = "stockholmsoundrome.com/style/" + slugged


        });


    </script>
}




