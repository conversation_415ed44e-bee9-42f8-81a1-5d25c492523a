<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <RootNamespace>stockhom_soundrom_mvc</RootNamespace>
    <CopyRefAssembliesToPublishDirectory>false</CopyRefAssembliesToPublishDirectory>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="EntityFramework" Version="6.4.4" />
    <PackageReference Include="Google.Apis" Version="1.59.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.59.0" />
    <PackageReference Include="Google.Apis.Core" Version="1.59.0" />
    <PackageReference Include="Google.Apis.YouTube.v3" Version="1.58.0.2874" />
    <PackageReference Include="Google.Apis.YouTubeAnalytics.v1" Version="1.49.0.2197" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="3.1.15" />
    <PackageReference Include="Microsoft.AspNetCore.Session" Version="2.2.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="3.1.5" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Credentials\" />
    <Folder Include="Scaffolders\" />
    <Folder Include="wwwroot\icons\" />
    <Folder Include="wwwroot\vinyls\" />
    <Folder Include="wwwroot\Uploads\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="wwwroot\Uploads\007296e7-a227-4121-8da2-e2c34795bb29.jpg" />
    <None Include="wwwroot\Uploads\0a2c4152-a526-496b-867b-e5f716b8984f.jpg" />
    <None Include="wwwroot\Uploads\1dadc512-f343-4f9a-ae6b-c4db570f3828.jpg" />
    <None Include="wwwroot\Uploads\4ecda01b-79c9-4005-943c-6614648e3883.jpg" />
    <None Include="wwwroot\Uploads\654ce616-19cb-418b-980c-0155b1b743ec.jpg" />
    <None Include="wwwroot\Uploads\84f20d55-10bd-473c-ae14-3fc03ee57cf5.jpg" />
    <None Include="wwwroot\Uploads\8d5a3981-4638-4bbd-8ef2-c84c25bf36df.jpg" />
    <None Include="wwwroot\Uploads\93a21717-481b-472a-bfa6-819ec1a46e6d.jpg" />
    <None Include="wwwroot\Uploads\94e32d97-b831-4a26-a57a-2d55bfe6b212.jpg" />
    <None Include="wwwroot\Uploads\b54146e8-fbc5-45fd-b9f0-1777c802111c.jpg" />
    <None Include="wwwroot\Uploads\c04165cb-c970-4bcf-ace2-875955ac2ee9.jpg" />
    <None Include="wwwroot\Uploads\cfaa4ca9-cfa4-43ef-934f-0411e6b833fb.jpg" />
    <None Include="wwwroot\Uploads\d05ba9d9-f5d8-475d-8d6b-a4e56d61997c.jpg" />
    <None Include="wwwroot\Uploads\d95ccd77-e7e9-4386-9d53-b8764db0fb7d.jpg" />
    <None Include="wwwroot\Uploads\e195b5c5-6e92-4b9d-bdd6-51cb0dbfdcca.jpg" />
    <None Include="wwwroot\Uploads\fa7f3bfc-9f3e-4395-af8b-704ccca27023.jpg" />
    <None Include="wwwroot\Uploads\HANS-ZIMMER-FEATURED.jpg" />
    <None Include="wwwroot\Uploads\Screenshot 2023-01-07 125401.jpg" />
  </ItemGroup>

</Project>
