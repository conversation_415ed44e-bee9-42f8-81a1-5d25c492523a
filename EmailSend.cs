﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc
{
    public class EmailSend
    {
        static string senderEmail = "<EMAIL>";
        static string senderPassword = "hyxwwyiluygtoteo";

        public static bool SendMail(string subject , string body, string receiverEmail)
        {
            try
            {
                // Create the email message
                MailMessage mail = new MailMessage();
                mail.From = new MailAddress(senderEmail);
                mail.To.Add(receiverEmail);
                mail.Subject = subject;
                mail.Body = body;

                // Setup the SMTP client
                SmtpClient smtp = new SmtpClient("smtp.gmail.com", 587);
                smtp.EnableSsl = true;
                smtp.DeliveryMethod = SmtpDeliveryMethod.Network;
                smtp.UseDefaultCredentials = false;
                smtp.Credentials = new NetworkCredential(senderEmail, senderPassword);

                // Send the email
                smtp.Send(mail);

                return true;
            }
            catch(Exception ex)
            {
                return false;
            }


        }

    }
}
