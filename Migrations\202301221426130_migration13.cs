﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration13 : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.TrackBuyLinks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Link = c.String(),
                        Track_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Tracks", t => t.Track_Id)
                .Index(t => t.Track_Id);
            
            CreateTable(
                "dbo.MusicBuyLinks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Link = c.String(),
                        Music_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Musics", t => t.Music_Id)
                .Index(t => t.Music_Id);
            
            CreateTable(
                "dbo.Musics",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        MusicCollectionType = c.Int(nullable: false),
                        PublishDate = c.DateTime(nullable: false),
                        StartCountDown = c<PERSON>(nullable: false),
                        TopPromo = c.<PERSON>(nullable: false),
                        ArtWorkSquare = c.String(),
                        ArtWorkHoriontal = c.String(),
                        YoutubeMusicAlbumLink = c.String(),
                        YoutubeAlbumLink = c.String(),
                        Description = c.String(),
                        MetaTitle = c.String(),
                        MetaDescription = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            AddColumn("dbo.Tracks", "Music_Id", c => c.Int());
            CreateIndex("dbo.Tracks", "Music_Id");
            AddForeignKey("dbo.Tracks", "Music_Id", "dbo.Musics", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Tracks", "Music_Id", "dbo.Musics");
            DropForeignKey("dbo.MusicBuyLinks", "Music_Id", "dbo.Musics");
            DropForeignKey("dbo.TrackBuyLinks", "Track_Id", "dbo.Tracks");
            DropIndex("dbo.MusicBuyLinks", new[] { "Music_Id" });
            DropIndex("dbo.TrackBuyLinks", new[] { "Track_Id" });
            DropIndex("dbo.Tracks", new[] { "Music_Id" });
            DropColumn("dbo.Tracks", "Music_Id");
            DropTable("dbo.Musics");
            DropTable("dbo.MusicBuyLinks");
            DropTable("dbo.TrackBuyLinks");
        }
    }
}
