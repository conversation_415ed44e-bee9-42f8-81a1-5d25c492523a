﻿namespace stockhom_soundrom_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration6 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.Journals", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.Journals", "Video_Id1", "dbo.Videos");
            DropForeignKey("dbo.Videos", "Journal_Id", "dbo.Journals");
            DropIndex("dbo.Journals", new[] { "Video_Id" });
            DropIndex("dbo.Journals", new[] { "Video_Id1" });
            DropIndex("dbo.Videos", new[] { "Journal_Id" });
            CreateTable(
                "dbo.VideoJournals",
                c => new
                    {
                        Video_Id = c.Int(nullable: false),
                        Journal_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Video_Id, t.Journal_Id })
                .ForeignKey("dbo.Videos", t => t.Video_Id, cascadeDelete: true)
                .ForeignKey("dbo.Journals", t => t.Journal_Id, cascadeDelete: true)
                .Index(t => t.Video_Id)
                .Index(t => t.Journal_Id);
            
            DropColumn("dbo.Journals", "Video_Id");
            DropColumn("dbo.Journals", "Video_Id1");
            DropColumn("dbo.Videos", "Journal_Id");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Videos", "Journal_Id", c => c.Int());
            AddColumn("dbo.Journals", "Video_Id1", c => c.Int());
            AddColumn("dbo.Journals", "Video_Id", c => c.Int());
            DropForeignKey("dbo.VideoJournals", "Journal_Id", "dbo.Journals");
            DropForeignKey("dbo.VideoJournals", "Video_Id", "dbo.Videos");
            DropIndex("dbo.VideoJournals", new[] { "Journal_Id" });
            DropIndex("dbo.VideoJournals", new[] { "Video_Id" });
            DropTable("dbo.VideoJournals");
            CreateIndex("dbo.Videos", "Journal_Id");
            CreateIndex("dbo.Journals", "Video_Id1");
            CreateIndex("dbo.Journals", "Video_Id");
            AddForeignKey("dbo.Videos", "Journal_Id", "dbo.Journals", "Id");
            AddForeignKey("dbo.Journals", "Video_Id1", "dbo.Videos", "Id");
            AddForeignKey("dbo.Journals", "Video_Id", "dbo.Videos", "Id");
        }
    }
}
