﻿using System;
using System.Collections.Generic;

namespace stockhom_soundrom_mvc.Models
{
    public class Style
    {
        public int Id { get; set; }
        public string StyleTitle { get; set; }
        public string Picture1 { get; set; }
        public string Picture2 { get; set; }
        public string Picture3 { get; set; }
        public DateTime ReleaseDate { get; set; }
        public string ExternalLink { get; set; }
        public State State { get; set; }
        public List<Video> Videos { get; set; } = new List<Video>();
        public List<Track> Tracks { get; set; } = new List<Track>();
        public List<Journal> Journals { get; set; } = new List<Journal>();
        public List<Artist> Artists { get; set; } = new List<Artist>();

        public List<Color> Colors { get; set; } = new List<Color>();
        public List<Size> Sizes { get; set; } = new List<Size>();

        public List<Catagory> Catagories { get; set; } = new List<Catagory>();

        public decimal Price { get; set; }
        public decimal FanPrice { get; set; }

        public string Description { get; set; }







    }

    public class Color
    {
        public int Id { get; set; }

        public string ColorString { get; set; }
    }

    public class Size
    {
        public int Id { get; set; }

        public string SizeString { get; set; }
    }

    public class Catagory
    {
        public int Id { get; set; }

        public string CatagoryString { get; set; }
    }
}
