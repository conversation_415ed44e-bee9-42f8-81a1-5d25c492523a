﻿@model stockhom_soundrom_mvc.ViewModels.AdminLoginViewModel
@{
    ViewData["Title"] = "Index";
}


@section Styles{

    <style>
        body{

            background-color:black;
        }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }

        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }
    </style>

}

@{
    var emailClass = Model.LoginFailed ? "form-control is-invalid" : "form-control";
    var passwordClass = Model.LoginFailed ? "form-control is-invalid" : "form-control";
}

<div style=" width: 400px; margin: auto; /* position: absolute; */ margin-top: 150px; ">



    <form method="post">
        <div class="infobox">
            <label>Email</label>
            @Html.TextBoxFor(m => m.Email, new { @class = emailClass })
        </div>
        <div class="infobox">
            <label>Password</label>
            @Html.TextBoxFor(m => m.Password, new { @class = passwordClass })
        </div>

        <div style="text-align:right">
            <input type="submit" style="margin-top:10px" formaction="@Url.Action("Login","AdminLogin")" value="Login" class="btn1" />
        </div>

        @if (Model.LoginFailed)
        {
            <div style="background-color:red;color:white;">
                Incorrect email or password.
            </div>
        }
    </form>

</div>

