﻿@model stockhom_soundrom_mvc.ViewModels.AdminArtistViewModel
@using stockhom_soundrom_mvc.Controllers
@using System.IO

@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}


@section Styles{


    <link rel="stylesheet" href="~/css/chosen.min.css" />

    <style>
        body {
            background-color: black;
        }

        form {
            position: relative;
        }

        #add_entity {
            position: relative;
            padding: 50px;
        }


            #add_entity .close-button {
                position: absolute;
                top: 0;
                right: 50px;
                cursor: pointer;
                background-color: black;
                border: solid;
                color: white;
            }

        #lbl_entity {
            color: white;
        }


        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }



        .profile_container {
            width: 800px;
        }

        .profile_box {
            width: 150px;
            height: 280px;
            display: inline-block;
            text-align: center;
            margin: 14px;
        }

            .profile_box img {
                height: 200px;
                width: 150px;
                cursor: pointer;
                transition: all 0.5s ease;
            }

                .profile_box img:hover {
                    transform: scale(1.1);
                }

            .profile_box .entity_name {
                width: 150px;
                text-align: center;
                color: white;
                margin-bottom: 3px;
                display: block;
            }

            .profile_box .state {
                border: solid;
                color: white;
                display: block;
            }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }


        .button_container {
            /*margin-left: 50px;*/
            margin-top: 20px;
        }

        #pic_vertical {
            width: 130px;
            height: 200px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
        }

            #pic_vertical:hover {
                background-color: #5d6952;
            }

        #pic_horizontal {
            width: 200px;
            height: 130px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            display: inline-block;
            background-size: 100% 100%;
        }

            #pic_horizontal:hover {
                background-color: #5d6952;
            }




        .plus {
            font-size: 73px;
            text-align: center;
            margin: auto;
            color: white;
            font-weight: 900;
        }


        .add_another {
            display: inline;
            text-align: right;
            color: white;
            position: absolute;
            right: 35px;
            font-size: x-small;
        }

            .add_another:hover {
                color: blue;
            }

        .select_related {
            background-color: black;
            color: white;
            margin: 3px;
            position: relative;
        }

        /*.select_related .close-button {
        position: absolute;
        top: 0;
        right: 0;
        display: none;
        }

        .select_related:hover .close-button {
        display: block;
        }

        .close-button {*/
        /* style the close button, such as color, size, etc. */
        /*color:red;
        }*/


        .parent {
            z-index: 1;
            background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
        }

        .child {
            position: absolute;
            z-index: 1;
        }



        .remove {
            position: absolute;
            top: 0px;
            right: 0px;
            display: block;
            box-sizing: border-box;
            width: 20px;
            height: 20px;
            border-width: 3px;
            border-style: solid;
            border-color: red;
            border-radius: 100%;
            background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
            background-color: red;
            box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
            transition: all 0.3s ease;
        }
    </style>



}


@{
    var add_artist_style = (Model.AddSectionVisible == true) ? "display:block" : "display:none";
}


<div class="container">
    <div style="/*margin-left: 50px; */ margin-bottom: 20px">
        <label id="lbl_entity">Artist</label>
        <input type="button" onclick="location.href='@Url.Action("NewAddSection", "AdminArtists")'" class="btn1" value="Add Artist" id="btn_add_artist" />

    </div>

    <div style=@add_artist_style id="add_entity">
        <button id="add_entity_close_btn" class="close-button">X</button>

        <div style="/*margin-left: 50px; */ margin-bottom: 20px; font-size: large; color: white;">
            Artist
        </div>

        <form id="form" method="post" enctype="multipart/form-data">


            <div id="pic_vertical" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Artist.VerticalImageUrl));</text> } }" onclick="pic_vertical_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Pic</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_vertical_upload' name="pic_vertical_upload" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_horizontal" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Artist.HorizontalImageUrl));</text> } }" onclick="pic_horizontal_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Pic</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_horizontal_upload' name="pic_horizontal_upload" type='file' value="" style='display: none' accept='.jpg' />



            <div class="infobox">
                <label>Artist Name</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Artist.ArtistName)
            </div>

            <div class="infobox" style="height: 160px">
                <label>Description</label>
                @Html.TextAreaFor(m => m.Artist.Description)
            </div>

            <div class="infobox">
                <label>Meta Title</label>
                @*<input type="text" id="meta_title" value="@Model.NewArtist.MetaTitle" />*@
                @Html.TextBoxFor(m => m.Artist.MetaTitle)
            </div>

            <div class="infobox">
                <label>Meta Description</label>
                @*<input type="text" id="meta_description" value="@Model.NewArtist.MetaDescription" />*@
                @Html.TextBoxFor(m => m.Artist.MetaDescription)
            </div>

            <div class="infobox" style="width: 300px">
                <label>Published Date</label>
                @*<input type="date" value="@Model.NewArtist.PublishedDate" id="published_date" style="color-scheme: dark;" />*@
                @Html.TextBoxFor(m => m.Artist.PublishedDate, new { type = "datetime-local", style = "color-scheme: dark;" })

            </div>


            <div class="infobox" id="infobox_related_tracks">
                <label>Related Track</label>
                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var track in Model.Artist.Tracks)
                        {
                            <select class="select_related" name=@Global.RelatedTracksDDLName>
                                @foreach (var option in Model.AllTracks)
                                {
                                    if (track.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.TrackTitle}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.TrackTitle}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllTracks)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.TrackTitle}</option>")
                        }

                    </select>
                }

            </div>
            <label type="submit" id="add_another_related_tracks" class="add_another">Add another related track</label>


            <div class="infobox" id="infobox_related_videos">

                <label>Related Video</label>

                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var video in Model.Artist.Videos)
                        {
                            <select class="select_related" name=@Global.RelatedVideosDDLName>
                                @foreach (var option in Model.AllVideos)
                                {
                                    if (video.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.VideoTitle}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllVideos)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                        }
                    </select>
                }

            </div>
            <label id="add_another_related_video" class="add_another">Add another related video</label>



            <div class="infobox" id="infobox_related_styles">
                <label>Related Style</label>
                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var style in Model.Artist.Styles)
                        {
                            <select class="select_related" name=@Global.RelatedStylesDDLName>
                                @foreach (var option in Model.AllStyles)
                                {
                                    if (style.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.StyleTitle}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.StyleTitle}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllStyles)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.StyleTitle}</option>")
                        }

                    </select>
                }

            </div>
            <label id="add_another_related_styles" class="add_another">Add another related style</label>



            <div class="infobox" id="infobox_related_journal_entry">
                <label>Related Journal Entry</label>

                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var journal in Model.Artist.Journals)
                        {
                            <select class="select_related" name=@Global.RelatedJournalsDDLName>
                                @foreach (var option in Model.AllJournals)
                                {
                                    if (journal.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.Headline}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllJournals)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                        }
                    </select>
                }


            </div>
            <label id="add_another_related_journal_entry" class="add_another">Add another related journal entry</label>



            <div class="button_container">

                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditPublish","AdminArtists" , new { artist_id = Model.Artist.Id})" value="Publish Artist" class="btn1" />
                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Publish","AdminArtists")" value="Publish Artist" class="btn1" />
                }

                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditSave","AdminArtists" , new { artist_id = Model.Artist.Id})" name="save" id="btn_save" value="save" class="btn1" />

                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Save","AdminArtists")" name="save" id="btn_save" value="save" class="btn1" />

                }

                <input type="submit" id="preview_button" value="Previw" class="btn1" />
                @if (Model.Artist != null)
                {
                    <input type="button" class="btn1" onclick="confirmDelete(@Model.Artist.Id)" value="Delete" />
                }
                else
                {
                    <input type="button" class="btn1" value="Delete" />
                }
            </div>

        </form>


    </div>


    <div class="profile_container">
        @using (Data data = new Data())
        {


            foreach (var artist in data.Artists.ToList())
            {
                <div class="profile_box">

                    @{var imageSource = Url.Content($"~/Uploads/{artist.VerticalImageUrl}"); }
                    <a href="@Url.Action(@"ViewForUpdate",@"AdminArtists",new { id = artist.Id })">
                        <img src=@imageSource />
                    </a>
                    <label class="entity_name">@artist.ArtistName</label>
                    @{ if (artist.State == State.Saved)
                        {
                            <label class="state">Saved</label>
                        }
                        else
                        {
                            if (artist.PublishedDate > DateTime.Now)
                            {
                                <label draggable="false" class="state">@artist.PublishedDate</label>
                            }
                            else
                            {
                                <label style="visibility:hidden" class="state">Pl</label>
                            }
                        }
                    }
                </div>
            }
        }
    </div>
</div>


//script
@{



    <script src="~/js/chosen.jquery.min.js"></script>

    <script type="text/javascript">

        var mouse_focused_related_select_element_id = null;

        window.onload = function () {


            $('select:not(.hidden_select)').each(function () {

                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();


                $(this).attr('id', uniqid);

                $(this).chosen();

                var generated_element_id = uniqid + "_chosen";

                //$(this).on('mouseover', function () {
                //    mouse_focused_related_select_element_id = $(this).attr('id');
                //    console.log('mouse over');
                //    console.log(mouseFocusedRelatedSelectElementId);
                //});


                //$(this).on("mouseout", function () {

                //    mouse_focused_related_select_element_id = null;
                //    console.log("mouse out");
                //});

                document.getElementById(generated_element_id).addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element_id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });


                document.getElementById(generated_element_id).addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });
        }

    </script>



    <script>
        var _hor_image = null;
        var _ver_image = null;

        var test1 = [];
        var videoss = "";
        var e = "";

        var mouse_focused_related_select_element_id = null;


        var LastRelatedTrackSelectElementPostFix = 0;
        var LastRelatedVidoeSelectElementPostFix = 0;
        var LastRelatedStyleSelectElementPostFix = 0;
        var LastRelatedJournalSelectElementPostFix = 0;


        document.onkeyup = function (e) {
            e = e || window.event;
            if (e.keyCode == "8") {


                if (mouse_focused_related_select_element_id != null) {
                    document.getElementById(mouse_focused_related_select_element_id).remove();
                    document.getElementById(mouse_focused_related_select_element_id.slice(0, -7)).remove();
                }

            }
        };


        function f3(infoboxElementId, selectElementName, clickingElementId) { //"clickingElement" could be a button

            document.getElementById(clickingElementId).addEventListener("click", function () {

                var select = document.createElement("select");
                select.name = selectElementName;
                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();
                select.id = uniqid;
                select.style.display = "inline";


                var hiddenselect = document.getElementById(infoboxElementId).querySelector(".hidden_select");
                for (var i = 0; i < hiddenselect.options.length; i++) {
                    var option = hiddenselect.options[i];
                    select.add(new Option(option.text, option.value));
                }

                document.getElementById(infoboxElementId).appendChild(select);

                $("#" + uniqid).chosen();


                //id of the generated element for the hidden select element

                var generated_element_id = uniqid + "_chosen";

                var generated_element = document.getElementById(generated_element_id);


                generated_element.addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element.id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });

                generated_element.addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });



        }

        f3("infobox_related_tracks", "select_related_tracks", "add_another_related_tracks");
        f3("infobox_related_videos", "select_related_videos", "add_another_related_video");
        f3("infobox_related_styles", "select_related_styles", "add_another_related_styles");
        f3("infobox_related_journal_entry", "select_related_journal_entry", "add_another_related_journal_entry");








        //when we input an id of a .infobox
        //this returns a collection of objects
        //containing id and text properties
        function f2(infobox_id) {

            const infobox = document.getElementById(infobox_id);
            const selectElements = infobox.querySelectorAll('select');


            let retobjs = [];

            for (const select of selectElements) {
                const selectedIndex = select.selectedIndex;
                const selectedOption = select.options[selectedIndex];
                const selectedText = selectedOption.text;
                const selectedValue = selectedOption.value;

                var obj = Object.assign({}, { id: selectedValue, text: selectedText });
                retobjs.push(obj);

            }

            return retobjs;

        }







        document.getElementById("pic_horizontal_upload").addEventListener("change", hor_image_upload, false);
        document.getElementById("pic_vertical_upload").addEventListener("change", ver_image_upload, false);
        /*document.getElementById("btn_save").addEventListener("click", btn_save_click);*/
        //document.getElementById("btn_add_artist").addEventListener("click", function () {

        //    document.getElementById("add_artist").style.display = "block";

        //});

        document.getElementById("add_entity_close_btn").addEventListener("click", function () {


            document.getElementById("add_entity").style.display = "none";

            //make element vlaues defualt

        });



        function refresh_profile_container() {

            //clear the inside of profile_container
            //get profile data from backend using ajax
        }



        function ver_image_upload() {

            var fileInput = document.getElementById('pic_vertical_upload');
            var file = fileInput.files[0];
            _ver_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_vertical');
            div.style.backgroundImage = 'url(' + fileUrl + ')';
        }

        function hor_image_upload() {

            var fileInput = document.getElementById('pic_horizontal_upload');
            var file = fileInput.files[0];
            _hor_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_horizontal');
            div.style.backgroundImage = 'url(' + fileUrl + ')';

        }

        function SaveImageAndReturnGuid(file) {

            var guid = null;


            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                type: "POST",
                url: "SaveImage.ashx",
                data: formData,
                contentType: false,
                processData: false,
                async: false,
                success: function (response) {
                    guid = response;
                },
                error: function (ex) {
                    test1 = ex;
                }
            });


            return guid;
        }

        function btn_save_click() {


            var ver_img_filename = SaveImageAndReturnGuid(_ver_image);
            var hor_img_filename = SaveImageAndReturnGuid(_hor_image);



            let artist = new Artist();


            artist.VerticalImageUrl = ver_img_filename;
            artist.HorizontalImageUrl = hor_img_filename;
            artist.ArtistName = document.getElementById("artist_name").value;
            artist.Description = document.getElementById("description").value;
            artist.MetaTitle = document.getElementById("meta_title").value;
            artist.MetaDescription = document.getElementById("meta_description").value;
            artist.PublishedDate = document.getElementById("published_date").value;
            artist.State = "1";


            //dont know about track/music till now , so will do the work related to that later

            var related_videos_data = f2("infobox_related_videos");

            var videos = [];

            for (let i = 0; i < related_videos_data.length; i++) {
                const obj = related_videos_data[i];


                var vid = new Video();
                vid.Id = obj.id;
                vid.VideoTitle = obj.text;

                videos.push(vid);
            }

            artist.Videos = videos;





            //var journalsObj = [];
            //journalsObj.push(new Journal(1, "test journal1"));
            //journalsObj.push(new Journal(2, "test journal2"));


            //artist.journals = journalsObj;
            //artist.metaTitle = document.getElementById("meta_title").value;


            $.ajax({
                type: "POST",
                url: "Artists.aspx/SaveArtist",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: "{artist: " + JSON.stringify(artist) + "}",
                cache: false,
                async: false,
                success: function (result) {

                    alert("soet");
                    onload_callback(result);
                },
                error: function (ex) {
                    console.log(ex.responseText);
                }
            });

            var somethig = "";



        }





        //add .profile_boxes to .profile_container
        //for all the artists in the database
        function getArtists() {
            $.ajax({
                type: "POST",
                url: "Artists.aspx/GetArtists",
                data: "{}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                success: function (response) {
                    var artists = [];
                    $.each(response.d, function (index, item) {
                        var artist = new Artist();
                        artist.Id = item.Id;
                        artist.VerticalImageUrl = item.VerticalImageUrl;
                        artist.HorizontalImageUrl = item.HorizontalImageUrl;
                        artist.ArtistName = item.ArtistName;
                        artist.Description = item.Description;
                        artist.MetaTitle = item.MetaTitle;
                        artist.MetaDescription = item.MetaDescription;
                        artist.PublishedDate = item.PublishedDate;
                        artist.Tracks = item.Tracks;
                        artist.Videos = item.Videos;
                        artist.Styles = item.Styles;
                        artist.Journals = item.Journals;
                        artist.State = item.State;
                        artists.push(artist);

                        // 1. Create a div element with class "profile_box" and id "pb1"
                        let div = document.createElement("div");
                        div.className = "profile_box";
                        div.tagName = artist.Id;

                        // 2. Add an img and 2 labels inside #pb1
                        let img = document.createElement("img");


                        img.src = uploadsfolderPath + artist.VerticalImageUrl;
                        let label1 = document.createElement("label");
                        label1.className = "artist_name";
                        label1.innerHTML = artist.ArtistName;
                        let label2 = document.createElement("label");
                        label2.className = "state";
                        if (artist.State == "0") {
                            label2.innerHTML = "";
                        }
                        else if (artist.State == "1") {
                            label2.innerHTML = "Saved";
                        }
                        else {

                            label2.innerHTML = artist.PublishedDate;
                        }

                        div.appendChild(img);
                        div.appendChild(label1);
                        div.appendChild(document.createElement("br"));
                        div.appendChild(label2);

                        // 3. Add #pb1 inside .profile_container
                        let container = document.querySelector(".profile_container");
                        container.appendChild(div);

                    });

                    test1 = artists;

                },
                error: function (xhr, status, error) {
                    console.log(error);
                }
            });
        }


        function validationForm() {




        }



        function confirmDelete(id) {
            if (confirm("Are you sure you want to delete this?")) {

                window.location.href = "/AdminArtists/DeleteArtist?artist_id=" + id;

            }
        }


    </script>

    <script>

        var newTab = null;

    $(document).ready(function () {
        $("#preview_button").click(function (e) {
            e.preventDefault();

            var form = $("#form");
            var formData = new FormData(form[0]);

            $.ajax({
                type: "POST",
                url: "@Url.Action("Preview","Artist")",
                data: formData,
                async: false,
                contentType: false,
                processData: false,
                success: function (result) {

                    console.log(result);

                     newTab = window.open();
                    newTab.document.write(result);
                }
            });
        });
    });
    </script>

    @if (Model.Artist != null)
    {
        <script>

        var publishDate = '@Model.Artist.PublishedDate.ToString("yyyy-MM-ddTHH:mm:ss")';
            document.getElementById('Artist_PublishedDate').value = publishDate;

        </script>
    }

}


