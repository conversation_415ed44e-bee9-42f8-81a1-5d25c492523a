﻿using Microsoft.AspNetCore.Mvc;
using stockhom_soundrom_mvc.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Controllers
{
    public class dwnldController : Controller
    {
        public IActionResult Index(string id)  //id is code
        {
            try
            {
                using (Data data = new Data())
                {
                    if (data.TrackDownloadRequests.Any(r => r.GeneratedCode == id))
                    {
                        var entitiy = data.TrackDownloadRequests.Where(r => r.GeneratedCode == id).FirstOrDefault();
                        var track = data.Tracks.Where(t => t.Id == entitiy.trackId).First();
                        var track_path = $"~/Uploads/{track.TrackPath}";



                        // Content type of the file
                        string contentType = "audio/mpeg";

                        // Return the file to be downloaded
                        return File(track_path, contentType, track.TrackTitle+".mp3");
                    }
                    else
                    {
                        return Content("Error!", "text/plain");

                    }
                }
            }
            catch(Exception ex)
            {
               return View("Error!", "text/plain");

            }

        }
    }
}
