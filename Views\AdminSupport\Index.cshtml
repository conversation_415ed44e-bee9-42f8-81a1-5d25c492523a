﻿@model stockhom_soundrom_mvc.ViewModels.AdminSupportViewModel


@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}



@section Styles{


    <link rel="stylesheet" href="~/css/chosen.min.css" />

    <style>

        body{

            background-color:black;
        }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 300px;
            }

        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
            margin-top:20px;
        }

    </style>



}


<div class="container">

    <form id="form" method="post">

        <div class="infobox">
            <label>Headline</label>
            @Html.TextBoxFor(m => m.Headline)
        </div>
        <label id="style_title_url" style="color: white; display: block;"></label>


        <div class="infobox" style="height: 320px">
            <label>Body Text</label>
            @Html.TextAreaFor(m => m.BodyText)
        </div>



        <input type="submit" formaction="@Url.Action("Update","AdminSupport")" value="PUBLISH SUPPORT" class="btn1" />

    </form>



</div>