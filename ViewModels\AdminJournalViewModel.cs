﻿using stockhom_soundrom_mvc.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.ViewModels
{
    public class AdminJournalViewModel
    {
        //private readonly IWebHostEnvironment _env;

        //public AdminArtistViewModel(IWebHostEnvironment env)
        //{
        //    _env = env;
        //}


        public List<Video> AllVideos { get; set; } = new List<Video>();
        public List<Artist> AllArtists { get; set; } = new List<Artist>();
        public List<Track> AllTracks { get; set; } = new List<Track>();
        public List<Style> AllStyles { get; set; } = new List<Style>();
        public Journal Journal { get; set; }
        public string RootPath
        {

            get; set;
            //get
            //{

            //    return _env.WebRootPath;
            //}
        }
        public bool ViewForUpdate { get; set; }
        public bool AddSectionVisible { get; set; }
    }
}
