﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>