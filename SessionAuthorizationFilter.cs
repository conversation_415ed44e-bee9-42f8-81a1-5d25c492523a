﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace stockhom_soundrom_mvc
{
    public class SessionAuthorizationFilter : IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            if (context.HttpContext.Session.GetInt32("UserId") == null)
            {
                // User is not logged in, redirect to login page
                context.Result = new RedirectToActionResult("Index", "AdminLogin", null);
            }
        }
    }
}
