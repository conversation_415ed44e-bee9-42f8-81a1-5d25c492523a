﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>