﻿@model stockhom_soundrom_mvc.ViewModels.AdminMusicViewModel
@using System.Data.Entity;
@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}




@section Styles{


    <link rel="stylesheet" href="~/css/chosen.min.css" />

    <style>
        li {
            padding: 10px;
            margin: 10px;
            cursor: pointer;
        }




        body {
            background-color: black;
        }


        form {
            position: relative;
        }

        #add_entity {
            position: relative;
            padding: 50px;
        }


            #add_entity .close-button {
                position: absolute;
                top: 0;
                right: 50px;
                cursor: pointer;
                background-color: black;
                border: solid;
                color: white;
            }

        #lbl_entity {
            color: white;
        }


        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }



        .profile_container {
        }

        .profile_box {
            height: 220px;
            display: inline-block;
            text-align: center;
            margin: 14px;
        }

            .profile_box .cover_art_with_vinyls {
                cursor: pointer;
                transition: all 0.5s ease;
            }

                .profile_box .cover_art_with_vinyls:hover {
                    transform: scale(1.1);
                }

                .profile_box .cover_art_with_vinyls img {
                    height: 200px;
                }



            .profile_box .entity_name {
                width: 150px;
                text-align: center;
                color: white;
                margin-bottom: 6px;
                margin-top: 3px;
                display: block;
                font-size: small;
                overflow: hidden;
            }

        .text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
         }

        .profile_box .related_entity {
            width: 150px;
            text-align: center;
            color: white;
            margin-bottom: 3px;
            display: block;
            font-size: x-small;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
            width: 150px;
            font-size: small;
        }

        .infobox {
            margin-top: 15px;
            /*margin-left: 50px;*/
            /* height: 60px;*/
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }


        .button_container {
            /*margin-left: 50px;*/
            margin-top: 20px;
        }

        #pic_vertical {
            width: 180px;
            height: 180px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            /*margin-left: 50px;*/
            vertical-align: central;
            display: inline-block;
            background-size: 100% 100%;
            margin-top: 15px;
        }

            #pic_vertical:hover {
                background-color: #5d6952;
            }

        #pic_horizontal {
            width: 200px;
            height: 130px;
            position: relative;
            border: solid;
            border-color: white;
            color: white;
            display: inline-block;
            background-size: 100% 100%;
        }

            #pic_horizontal:hover {
                background-color: #5d6952;
            }




        .plus {
            font-size: 73px;
            text-align: center;
            margin: auto;
            color: white;
            font-weight: 900;
        }


        .add_another {
            display: inline;
            text-align: right;
            color: white;
            position: absolute;
            right: 35px;
            font-size: x-small;
        }

            .add_another:hover {
                color: blue;
            }

        .select_related {
            background-color: black;
            color: white;
            margin: 3px;
            position: relative;
        }

        /*.select_related .close-button {
                position: absolute;
                top: 0;
                right: 0;
                display: none;
            }

            .select_related:hover .close-button {
                display: block;
            }

        .close-button {*/
        /* style the close button, such as color, size, etc. */
        /*color:red;
        }*/


        .parent {
            z-index: 1;
            background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
        }

        .child {
            position: absolute;
            z-index: 1;
        }



        .remove {
            position: absolute;
            top: 0px;
            right: 0px;
            display: block;
            box-sizing: border-box;
            width: 20px;
            height: 20px;
            border-width: 3px;
            border-style: solid;
            border-color: red;
            border-radius: 100%;
            background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
            background-color: red;
            box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
            transition: all 0.3s ease;
        }
    </style>


}




@{
    var add_artist_style = (Model.AddSectionVisible == true) ? "display:block" : "display:none";
}



<div class="container">
    <div style="/*margin-left: 50px; */ margin-bottom: 20px">
        <label id="lbl_entity">Music</label>
        <input type="button" onclick="location.href='@Url.Action("NewAddSection", "AdminMusic")'" class="btn1" value="Add Music" id="btn_add_artist" />

    </div>

    <div style=@add_artist_style id="add_entity">
        <button id="add_entity_close_btn" class="close-button">X</button>

        <div style="/*margin-left: 50px; */ margin-bottom: 20px; font-size: large; color: white;">
            Track
        </div>

        <form id="form" method="post" enctype="multipart/form-data">


            @if (Model.ViewForUpdate == true)
            {
                <input type="button" id="btn_change_track" value="Change Track" />

                <div style="color:white">

                    <input type="file" style="display:none" name="track_file" id="track_file" accept=".mp3,.wav" />

                </div>
            }
            else
            {
                <div style="color:white">

                    <input type="file" name="track_file" id="track_file" accept=".mp3,.wav" />

                </div>
            }



            <div class="infobox">
                <label>Track Title</label>
                @*<input type="text" value="@Model.NewArtist.ArtistName" id="artist_name" />*@
                @Html.TextBoxFor(m => m.Track.TrackTitle)
            </div>

            <div class="infobox" id="infobox_related_artists">
                <label>Artist Name</label>
                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var style in Model.Track.RelatedArtists)
                        {
                            <select class="select_related" name=@Global.RelatedArtistsDDLName>
                                @foreach (var option in Model.AllArtists)
                                {
                                    if (style.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.ArtistName}</option>")
                                    }
                                    else

                                        @Html.Raw($"<option value='{option.Id}'>{option.ArtistName}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllArtists)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.ArtistName}</option>")
                        }

                    </select>
                }

            </div>
            <label id="add_another_related_artist" class="add_another">Add Another Artist</label>


            <div class="infobox">
                <label>Catagory Number</label>
                @Html.TextBoxFor(m => m.Track.CatalogNumber)
            </div>

            <div class="infobox">
                <label>ISRC</label>
                @*<input type="text" id="meta_title" value="@Model.NewArtist.MetaTitle" />*@
                @Html.TextBoxFor(m => m.Track.ISRC)
            </div>

            <div class="infobox">
                <label>Main Genre</label>
                @*<input type="text" id="meta_description" value="@Model.NewArtist.MetaDescription" />*@
                @Html.TextBoxFor(m => m.Track.MainGenre)
            </div>

            <div class="infobox">
                <label>Sub-genre</label>
                @*<input type="text" id="meta_description" value="@Model.NewArtist.MetaDescription" />*@
                @Html.TextBoxFor(m => m.Track.SubGenre)
            </div>

            <div class="infobox">
                <label>BPM</label>
                @*<input type="text" id="meta_description" value="@Model.NewArtist.MetaDescription" />*@
                @Html.TextBoxFor(m => m.Track.BPM)
            </div>

            <div class="infobox" style="width: 300px">
                <label>Publish Date</label>
                @*<input type="date" value="@Model.NewArtist.PublishedDate" id="published_date" style="color-scheme: dark;" />*@
                @Html.TextBoxFor(m => m.Track.PublishDate, new { type = "datetime-local", style = "color-scheme: dark;" })

            </div>



            <div id="pic_vertical" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Track.ArtWorkSquare));</text> } }" onclick="pic_vertical_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Artwork Square</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_vertical_upload' name="pic_vertical_upload" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_horizontal" style="@{ if(Model.ViewForUpdate) { <text>background-image: url(@Url.Content("~/Uploads/"+Model.Track.ArtWorkHoriontal));</text> } }" onclick="pic_horizontal_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">Artwork Horizontal</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_horizontal_upload' name="pic_horizontal_upload" type='file' value="" style='display: none' accept='.jpg' />

            <div style="display:inline-block">
                <div>
                    @Html.CheckBoxFor(m => m.Track.StartCountDown) <span style="color:white">Start count down clock </span>
                </div>

                <div>
                    @Html.CheckBoxFor(m => m.Track.TopPromo) <span style="color:white">Top promo</span>
                </div>
            </div>

            <div class="infobox" id="infobox_buy_links">

                <label>Buy Link</label>

                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var buyLink in Model.Track.BuyLinks)
                        {
                            <input type="text" value="@buyLink.Link" name="@Global.BuyLinkTXTName" />
                        }
                    }

                }


            </div>
            <label id="add_another_buy_link" class="add_another">Add another buy link</label>



            <div class="infobox">
                <label>Spotify Stream Link</label>
                @Html.TextBoxFor(m => m.Track.SpotifyLink)
            </div>

            <div class="infobox">
                <label>Apple Stream Link</label>
                @Html.TextBoxFor(m => m.Track.AppleLink)
            </div>

            <div class="infobox">
                <label>Soundclould Stream Link</label>
                @Html.TextBoxFor(m => m.Track.SoundCloudLink)
            </div>

            <div class="infobox">
                <label>Youtube Music Stream Link</label>
                @Html.TextBoxFor(m => m.Track.YoutubeMusicStreamLink)
            </div>


            <span style="color:white">Offer free download for this track</span> @Html.CheckBoxFor(m => m.Track.OfferFreeDownload)


            <div class="infobox">
                <label>Youtube Video Link</label>
                @Html.TextBoxFor(m => m.Track.YoutubeLink)
            </div>

            <div class="infobox">
                <label>Meta Title</label>
                @Html.TextBoxFor(m => m.Track.MetaTitle)
            </div>

            <div class="infobox">
                <label>Meta Description</label>
                @Html.TextBoxFor(m => m.Track.MetaDescription)
            </div>

            <div class="infobox" style="height: 160px">
                <label>Description</label>
                @Html.TextAreaFor(m => m.Track.Description)
            </div>


            <div class="infobox" id="infobox_related_videos">

                <label>Related Video</label>

                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var video in Model.Track.RelatedVideos)
                        {
                            <select class="select_related" name=@Global.RelatedVideosDDLName>
                                @foreach (var option in Model.AllVideos)
                                {
                                    if (video.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.VideoTitle}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllVideos)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.VideoTitle}</option>")
                        }
                    </select>
                }

            </div>
            <label id="add_another_related_video" class="add_another">Add another related video</label>



            <div class="infobox" id="infobox_related_styles">
                <label>Related Style</label>
                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var style in Model.Track.RelatedStyles)
                        {
                            <select class="select_related" name=@Global.RelatedStylesDDLName>
                                @foreach (var option in Model.AllStyles)
                                {
                                    if (style.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.StyleTitle}</option>")
                                    }
                                    else
                                    {
                                        @Html.Raw($"<option value='{option.Id}'>{option.StyleTitle}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllStyles)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.StyleTitle}</option>")
                        }

                    </select>
                }

            </div>
            <label id="add_another_related_styles" class="add_another">Add another related style</label>




            <div class="infobox" id="infobox_related_journal_entry">
                <label>Related Journal Entries</label>
                @{if (Model.ViewForUpdate == true)
                    {
                        foreach (var style in Model.Track.RelatedJournals)
                        {
                            <select class="select_related" name=@Global.RelatedJournalsDDLName>
                                @foreach (var option in Model.AllJournals)
                                {
                                    if (style.Id == option.Id)
                                    {
                                        @Html.Raw($"<option selected value='{option.Id}'>{option.Headline}</option>")
                                    }
                                    else

                                        @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                                    }
                                }

                            </select>
                        }
                    }

                    <select style="display:none" class="hidden_select">
                        @foreach (var option in Model.AllJournals)
                        {
                            @Html.Raw($"<option value='{option.Id}'>{option.Headline}</option>")
                        }

                    </select>
                }

            </div>
            <label id="add_another_related_journal_entry" class="add_another">Add another related Journal Entry</label>







            <div class="button_container">

                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditPublish","AdminMusic",new { track_id=Model.Track.Id })" value="PUBLISH TRACK" class="btn1" />

                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Publish","AdminMusic")" value="PUBLISH TRACK" class="btn1" />

                }


                @if (Model.ViewForUpdate)
                {
                    <input type="submit" formaction="@Url.Action("EditSave","AdminMusic",new { track_id=Model.Track.Id })" name="save" id="btn_save" value="SAVE" class="btn1" />
                }
                else
                {
                    <input type="submit" formaction="@Url.Action("Save","AdminMusic")" name="save" id="btn_save" value="SAVE" class="btn1" />
                }
                <input type="submit" id="preview_button" value="PREVIEW" class="btn1" />
                @if (Model.ViewForUpdate)
                {
                <input type="button" class="btn1" onclick="confirmDelete()" value="DELETE" />
                }
            </div>

        </form>


    </div>




    <div class="profile_container">
        @using (Data data = new Data())
        {

            //get the music (albums) , not saved
            foreach (var music in data.Musics.Where(m => m.State != State.Saved).Include("Tracks.RelatedArtists").ToList())
            {
                var noOfVinylRecords = music.Tracks.Count();
                string VinylRecordImagePath = Url.Content($"~/vinyls/{noOfVinylRecords}.png");


                List<Artist> relatedArtists = new List<Artist>();


                foreach (var track in music.Tracks)
                {
                    foreach (var artist in track.RelatedArtists)
                    {
                        if (relatedArtists.Contains(artist) == false)
                        {
                            relatedArtists.Add(artist);
                        }

                    }

                }





                <div draggable="true" id="@music.Id" class="profile_box" ondrop="drop(event)" ondragover="allowDrop(event)" ondragstart="drag(event)">

                    <a draggable="false" href="@Url.Action(@"EditMusic",@"AdminMusic",new { id = music.Id })">
                        <div draggable="false" style="height: 150px;" class="cover_art_with_vinyls">
                            <img id="@music.Id" name="music" style="display:inline;height: 100%; margin-right:-4px;" src="@Url.Content($"~/Uploads/{music.ArtWorkSquare}")">
                            <img id="@music.Id" name="music" style="display:inline;height: 100%;" src="@VinylRecordImagePath">
                        </div>
                    </a>

                    <a>
                        <label draggable="false" class="entity_name"><div class="text">@music.Title</div></label>
                    </a>

                    @if (relatedArtists.Count() >= 2)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[1].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[1].ArtistName</div></label>
                        </a>
                    }
                    else if (relatedArtists.Count() == 1)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }
                    else
                    {
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }



                    @{ if (music.State == State.Saved)
                        {
                            <label draggable="false" class="state">Saved</label>
                        }
                        else
                        {
                            if (music.PublishDate > DateTime.Now)
                            {
                                <label draggable="false" class="state">@music.PublishDate</label>
                            }
                            else
                            {

                            }
                        }

                    }

                </div>


            }



            //how to get tracks which are not part of music , not saved
            var tracks = data.Tracks.Where(t => t.State != State.Saved).Include(t => t.RelatedArtists).Where(t => !data.Musics.Any(m => m.Tracks.Any(mt => mt.Id == t.Id)));


            foreach (var track in tracks)
            {

                List<Artist> relatedArtists = new List<Artist>();



                foreach (var artist in track.RelatedArtists)
                {
                    if (relatedArtists.Contains(artist) == false)
                    {
                        relatedArtists.Add(artist);
                    }

                }





                string VinylRecordImagePath = Url.Content($"~/vinyls/1.png");


                <div class="profile_box" id="@track.Id" draggable="true" ondragover="allowDrop(event)" ondrop="drop(event)" ondragstart="drag(event)">
                    <a href="@Url.Action(@"EditTrack",@"AdminMusic",new { id = track.Id  , type = "track"})">
                        <div style="height: 150px;" class="cover_art_with_vinyls">
                            <img id="@track.Id" name="track" style="display:inline;height: 100%; margin-right:-4px;" src="@Url.Content($"~/Uploads/{track.ArtWorkSquare}")">
                            <img id="@track.Id" name="track" style="display:inline;height: 100%;" src="@VinylRecordImagePath">
                        </div>
                    </a>

                    <label class="entity_name"><div class="text">@track.TrackTitle</div></label>


                    @if (relatedArtists.Count() >= 2)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[1].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[1].ArtistName</div></label>
                        </a>
                    }
                    else if (relatedArtists.Count() == 1)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }
                    else
                    {
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }


                    @{ if (track.State == State.Saved)
                        {
                            <label class="state">Saved</label>
                        }
                        else
                        {
                            if (track.PublishDate > DateTime.Now)
                            {
                                <label draggable="false" class="state">@track.PublishDate</label>
                            }
                            else
                            {

                            }
                        }
                    }

                </div>
            }



            //get the music (albums) , saved
            foreach (var music in data.Musics.Where(m => m.State == State.Saved).Include("Tracks.RelatedArtists").ToList())
            {
                var noOfVinylRecords = music.Tracks.Count();
                string VinylRecordImagePath = Url.Content($"~/vinyls/{noOfVinylRecords}.png");


                List<Artist> relatedArtists = new List<Artist>();


                foreach (var track in music.Tracks)
                {
                    foreach (var artist in track.RelatedArtists)
                    {
                        if (relatedArtists.Contains(artist) == false)
                        {
                            relatedArtists.Add(artist);
                        }

                    }

                }





                <div draggable="true" id="@music.Id" class="profile_box" ondrop="drop(event)" ondragover="allowDrop(event)" ondragstart="drag(event)">

                    <a draggable="false" href="@Url.Action(@"EditMusic",@"AdminMusic",new { id = music.Id })">
                        <div draggable="false" style="height: 150px;" class="cover_art_with_vinyls">
                            <img id="@music.Id" name="music" style="display:inline;height: 100%; margin-right:-4px;" src="@Url.Content($"~/Uploads/{music.ArtWorkSquare}")">
                            <img id="@music.Id" name="music" style="display:inline;height: 100%;" src="@VinylRecordImagePath">
                        </div>
                    </a>

                    <label draggable="false" class="entity_name"><div class="text">@music.Title</div></label>


                    @if (relatedArtists.Count() >= 2)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[1].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[1].ArtistName</div></label>
                        </a>
                    }
                    else if (relatedArtists.Count() == 1)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }
                    else
                    {
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }



                    @{ if (music.State == State.Saved)
                        {
                            <label draggable="false" class="state">Saved</label>
                        }
                        else
                        {
                            if (music.PublishDate > DateTime.Now)
                            {
                                <label draggable="false" class="state">@music.PublishDate</label>
                            }
                            else
                            {

                            }
                        }
                    }

                </div>


            }



            //how toget tracks which are not part of music , not saved
            tracks = data.Tracks.Where(t => t.State == State.Saved).Include(t => t.RelatedArtists).Where(t => !data.Musics.Any(m => m.Tracks.Any(mt => mt.Id == t.Id)));


            foreach (var track in tracks)
            {

                List<Artist> relatedArtists = new List<Artist>();



                foreach (var artist in track.RelatedArtists)
                {
                    if (relatedArtists.Contains(artist) == false)
                    {
                        relatedArtists.Add(artist);
                    }

                }





                string VinylRecordImagePath = Url.Content($"~/vinyls/1.png");


                <div class="profile_box" id="@track.Id" draggable="true" ondragover="allowDrop(event)" ondrop="drop(event)" ondragstart="drag(event)">
                    <a href="@Url.Action(@"EditTrack",@"AdminMusic",new { id = track.Id  , type = "track"})">
                        <div style="height: 150px;" class="cover_art_with_vinyls">
                            <img id="@track.Id" name="track" style="display:inline;height: 150px; margin-right:-4px;" src="@Url.Content($"~/Uploads/{track.ArtWorkSquare}")">
                            <img id="@track.Id" name="track" style="display:inline;height: 100%;" src="@VinylRecordImagePath">
                        </div>
                    </a>

                    <label class="entity_name"><div class="text">@track.TrackTitle</div></label>


                    @if (relatedArtists.Count() >= 2)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[1].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[1].ArtistName</div></label>
                        </a>
                    }
                    else if (relatedArtists.Count() == 1)
                    {
                        <a href="@Url.Action("ViewForUpdate2","AdminArtists",new { id=relatedArtists[0].Id })">
                            <label draggable="false" class="related_entity"><div class="text">@relatedArtists[0].ArtistName</div></label>
                        </a>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }
                    else
                    {
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                        <label style="visibility:hidden" draggable="false" class="related_entity">pl</label>
                    }


                    @{ if (track.State == State.Saved)
                        {
                            <label class="state">Saved</label>
                        }
                        else
                        {
                            if (track.PublishDate.Date > DateTime.Now)
                            {
                                <label draggable="false" class="state">@track.PublishDate</label>
                            }
                            else
                            {

                            }
                        }
                    }

                </div>
            }

        }
    </div>


</div>



@section Scripts{


    <script src="~/js/chosen.jquery.min.js"></script>


    <script>

    var publishDate = '@Model.Track.PublishDate.ToString("yyyy-MM-ddTHH:mm:ss")';
    document.getElementById('Track_PublishDate').value = publishDate;

    </script>


    <script type="text/javascript">

        var mouse_focused_related_select_element_id = null;

        window.onload = function () {


            $('select:not(.hidden_select)').each(function () {

                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();


                $(this).attr('id', uniqid);

                $(this).chosen();

                var generated_element_id = uniqid + "_chosen";

                //$(this).on('mouseover', function () {
                //    mouse_focused_related_select_element_id = $(this).attr('id');
                //    console.log('mouse over');
                //    console.log(mouseFocusedRelatedSelectElementId);
                //});


                //$(this).on("mouseout", function () {

                //    mouse_focused_related_select_element_id = null;
                //    console.log("mouse out");
                //});

                document.getElementById(generated_element_id).addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element_id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });


                document.getElementById(generated_element_id).addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });
        }

    </script>


    <script>
        var _hor_image = null;
        var _ver_image = null;

        var test1 = [];
        var videoss = "";
        var e = "";





        var LastRelatedTrackSelectElementPostFix = 0;
        var LastRelatedVidoeSelectElementPostFix = 0;
        var LastRelatedStyleSelectElementPostFix = 0;
        var LastRelatedJournalSelectElementPostFix = 0;


        document.onkeyup = function (e) {
            e = e || window.event;
            if (e.keyCode == "8") {


                if (mouse_focused_related_select_element_id != null) {
                    document.getElementById(mouse_focused_related_select_element_id).remove();
                    document.getElementById(mouse_focused_related_select_element_id.slice(0, -7)).remove();
                }

            }
        };



        function f3(infoboxElementId, selectElementName, clickingElementId) { //"clickingElement" could be a button

            document.getElementById(clickingElementId).addEventListener("click", function () {

                var select = document.createElement("select");
                select.name = selectElementName;
                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();
                select.id = uniqid;
                select.style.display = "inline";





                var hiddenselect = document.getElementById(infoboxElementId).querySelector(".hidden_select");
                for (var i = 0; i < hiddenselect.options.length; i++) {
                    var option = hiddenselect.options[i];
                    select.add(new Option(option.text, option.value));
                }

                document.getElementById(infoboxElementId).appendChild(select);

                $("#" + uniqid).chosen();


                //id of the generated element for the hidden select element

                var generated_element_id = uniqid + "_chosen";

                var generated_element = document.getElementById(generated_element_id);


                generated_element.addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element.id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });


                generated_element.addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });



        }

        f3("infobox_related_videos", "select_related_videos", "add_another_related_video");
        f3("infobox_related_styles", "select_related_styles", "add_another_related_styles");
        f3("infobox_related_journal_entry", "select_related_journal_entry", "add_another_related_journal_entry");
        f3("infobox_related_artists", "select_related_artists", "add_another_related_artist");



        document.getElementById("add_another_buy_link").addEventListener("click", function () {


            let input = document.createElement("input");
            input.type = "text";
            input.name = "input_text_buylink";
            input.style.border = "solid";
            input.style.borderColor = "white";
            input.style.borderWidth = "1px";

            document.getElementById("infobox_buy_links").appendChild(input);
        });





        //when we input an id of a .infobox
        //this returns a collection of objects
        //containing id and text properties
        function f2(infobox_id) {

            const infobox = document.getElementById(infobox_id);
            const selectElements = infobox.querySelectorAll('select');


            let retobjs = [];

            for (const select of selectElements) {
                const selectedIndex = select.selectedIndex;
                const selectedOption = select.options[selectedIndex];
                const selectedText = selectedOption.text;
                const selectedValue = selectedOption.value;

                var obj = Object.assign({}, { id: selectedValue, text: selectedText });
                retobjs.push(obj);

            }

            return retobjs;

        }







        document.getElementById("pic_horizontal_upload").addEventListener("change", hor_image_upload, false);
        document.getElementById("pic_vertical_upload").addEventListener("change", ver_image_upload, false);
        /*document.getElementById("btn_save").addEventListener("click", btn_save_click);*/
        //document.getElementById("btn_add_artist").addEventListener("click", function () {

        //    document.getElementById("add_artist").style.display = "block";

        //});

        document.getElementById("add_entity_close_btn").addEventListener("click", function () {


            document.getElementById("add_entity").style.display = "none";

            //make element vlaues defualt

        });



        function refresh_profile_container() {

            //clear the inside of profile_container
            //get profile data from backend using ajax
        }



        function ver_image_upload() {

            var fileInput = document.getElementById('pic_vertical_upload');
            var file = fileInput.files[0];
            _ver_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_vertical');
            div.style.backgroundImage = 'url(' + fileUrl + ')';
        }

        function hor_image_upload() {

            var fileInput = document.getElementById('pic_horizontal_upload');
            var file = fileInput.files[0];
            _hor_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_horizontal');
            div.style.backgroundImage = 'url(' + fileUrl + ')';

        }

        function SaveImageAndReturnGuid(file) {

            var guid = null;


            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                type: "POST",
                url: "SaveImage.ashx",
                data: formData,
                contentType: false,
                processData: false,
                async: false,
                success: function (response) {
                    guid = response;
                },
                error: function (ex) {
                    test1 = ex;
                }
            });


            return guid;
        }

        


        function validationForm() {




        }


        function confirmDelete() {

           


            if (confirm("Are you sure you want to delete this?")) {
                window.location.href = "/AdminMusic/DeleteTrack?track_id=@Model.Track.Id";
            }
        }

    </script>


    <script>


        var drag_elem_id = "";
        var drag_elem_name = "";
        var drop_elem_id = "";
        var drop_elem_name = "";


        function allowDrop(ev) {

            ev.preventDefault();
        }

        function drag(ev) {

            drag_elem_id = ev.target.id;
            drag_elem_name = ev.target.name;
        }

        function drop(ev) {
            ev.preventDefault();
            drop_elem_id = ev.target.id;
            drop_elem_name = ev.target.name;


            //only a track profile can be drop into another track or a music profile
            if (drag_elem_name == 'track' && drop_elem_name == "music") {

                var redirectUrl = "/AdminMusic/AddTrackToMusic?trackId=" + drag_elem_id + "&musicId=" + drop_elem_id;


                console.log("redirectUrl: " + redirectUrl);

                location.href = redirectUrl;
            }
            else if (drag_elem_name == 'track' && drop_elem_name == "track") {


                var redirectUrl = "/AdminMusic/Combine2Tracks?track1Id=" + drag_elem_id + "&track2Id=" + drop_elem_id;


                console.log("redirectUrl: " + redirectUrl);

                location.href = redirectUrl;

            }
            else {

                //do nothing.
            }

        }



        document.getElementById('btn_change_track').addEventListener('click', function () {

            document.getElementById('track_file').style.display = 'inline';

        });


    </script>


    <script>

        var newTab = null;

    $(document).ready(function () {
        $("#preview_button").click(function (e) {
            e.preventDefault();

            var form = $("#form");
            var formData = new FormData(form[0]);

            $.ajax({
                type: "POST",
                url: "@Url.Action("Preview","Track")",
                data: formData,
                async: false,
                contentType: false,
                processData: false,
                success: function (result) {

                    console.log(result);

                    newTab = window.open();
                    newTab.document.write(result);
                }
            });
        });
    });
    </script>


}
