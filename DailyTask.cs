﻿using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc
{
    public class DailyTask : IHostedService, IDisposable
    {
        private readonly Timer _timer;

        public DailyTask()
        {
            _timer = new Timer(DoWork, null, TimeSpan.Zero, TimeSpan.FromDays(1));
            
        }

        private async void DoWork(object state)
        {
        //    YoutubeAPI youtubeAPI = new YoutubeAPI();
        //    await youtubeAPI.Method();
        //    youtubeAPI.AddTodaysCountForAllVideos();
        //    youtubeAPI.SetVideoRankingsforLast10Days();
        //    youtubeAPI.SetVideoRankingsforLast30Days();

            



        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            // start your background task
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            // stop your background task
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            // dispose the timer
            _timer?.Dispose();
        }
    }
}
