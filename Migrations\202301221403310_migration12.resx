﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>