﻿
/*container1*/
@media screen and (max-width: 584px) {

    .container1 {
        text-align: center;
        max-width: 800px;
        padding: 5%;
        padding-top: 1%;
    }
}

@media screen and (min-width: 585px) {

    .container1 {
        max-width: 60vw;
        padding: 5%;
        padding-top: 0;
    }
}



.artist_main {
    text-align: center;
    width: 100%;
    margin: auto;
    position: relative;
}

container1
.artist_main .back_button {
    width: 20px;
    height: 20px;
    position: absolute;
    top: 0;
    left: 0;
}




.artist_main .back_button:hover {
    background-color: lightgray;
}


.track_title_and_artist {
    display: block;
    text-align: left;
    /*margin-left: 2.5vw;*/
}

.streaming_services {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 4vw;
    gap: 4vw;
    justify-content: flex-start;
}

    .streaming_services .straming_service_icon {
        width: 35px;
        height: 35px;
    }


.artist_main .artist_name {
    display: inline-block;
    min-width: 100px;
    color: black;
    margin: 0;
}

.artist_main .artist_image {
    display: inline-block;
    width: 30%;
    min-width: 200px;
}


.DescriptionTruncated {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: black;
    font-size: 16px;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;
    text-align: left;
}

.DescriptionExtended {
    width: 100%;
    display: none;
    font-size: 16px;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;
    text-align: left;
}


.button1 {
    width: 100%;
    display: block;
    margin: auto;
    background-color: white;
    text-size-adjust: auto;
}

    .button1:hover {
        background-color: lightgray;
    }

.related_styles {
    height: 32vw;
    text-align: left;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

    .related_styles .style_image {
        display: inline;
        height: 100%;
    }

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 */
    height: 0;
    margin-bottom: 5vw;
}

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

#sc_embedment_iframe {
    width: 100%;
    margin-bottom: 4vw;
}

@media screen and (max-width: 400px) {

    .desktopImage {
        display: none;
    }


    .mobileImage {
        display: block;
    }
}


@media screen and (min-width: 401px) {

    .desktopImage {
        display: block;
    }


    .mobileImage {
        display: none;
    }
}

.section_title {
    text-align: left;
    margin-top: 2vw;
    margin-bottom: 2vw;
    font-weight: 900;
}

.related_journal {
}

    .related_journal img {
        width: 100%;
    }



.section {
    margin-bottom: 10vw;
}

::-webkit-scrollbar {
    height: 4px; /* height of horizontal scrollbar ← You're missing this */
    width: 4px; /* width of vertical scrollbar */
    border: 1px solid #d5d5d5;
}


/*social media icons*/
@media screen and (max-width: 584px) {

    .div_social_media_icons {
        display: flex;
        justify-content: left;
        align-items: center;
        flex-wrap: wrap;
    }

    .div_single_icon {
        width: 40px;
        height: 40px;
        padding: 3px;
        margin: 5vw;
    }

        .div_single_icon img {
            width: 100%;
            height: 100%;
        }
}

@media screen and (min-width: 585px) {

    .div_social_media_icons {
        display: flex;
        justify-content: left;
        align-items: center;
        flex-wrap: wrap;
    }

    .div_single_icon {
        width: 50px;
        height: 50px;
        padding: 3px;
        margin: 3vw;
    }

        .div_single_icon img {
            width: 100%;
            height: 100%;
        }
}

