﻿using stockhom_soundrom_mvc.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.ViewModels
{
    public class AdminStylesViewModel
    {
        public bool ViewForUpdate { get; set; }
        public bool AddSectionVisible { get; set; }


        public List<Video> AllVideos { get; set; } = new List<Video>();
        public List<Journal> AllJournals { get; set; } = new List<Journal>();
        public List<Track> AllTracks { get; set; } = new List<Track>();
        public List<Artist> AllArtists { get; set; } = new List<Artist>();
        public Style Style { get; set; }
    }
}
