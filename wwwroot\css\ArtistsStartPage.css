﻿#p_introduction {
    /* height: 90px; */
    font-size: 16px;
    /* min-width: 100%; */
    margin: 44px;
}

.profile_box_container {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
}

.profile_box {
    width: 150px;
    height: 280px;
    display: inline-block;
    text-align: center;
    margin: 14px;
}

    .profile_box img {
        height: 200px;
        width: 150px;
        cursor: pointer;
        transition: all 0.5s ease;
    }

        .profile_box img:hover {
            transform: scale(1.1);
        }

    .profile_box .artist_name {
        width: 150px;
        text-align: center;
        color: black;
        margin-bottom: 3px;
        display: block;
    }

    .profile_box .state {
        border: solid;
        color: white;
        display: block;
    }