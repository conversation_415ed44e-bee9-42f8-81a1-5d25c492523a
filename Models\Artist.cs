﻿using System;
using System.Collections.Generic;

namespace stockhom_soundrom_mvc.Models
{
    public class Artist
    {
        public int Id { get; set; }
        public string VerticalImageUrl { get; set; }
        public string HorizontalImageUrl { get; set; }
        public string ArtistName { get; set; }
        public string Description { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public DateTime PublishedDate { get; set; }
        public List<Track> Tracks { get; set; } = new List<Track>();
        public List<Video> Videos { get; set; } = new List<Video>();
        public List<Style> Styles { get; set; } = new List<Style>();
        public List<Journal> Journals { get; set; } = new List<Journal>();
        public State State { get; set; }

    }
}
