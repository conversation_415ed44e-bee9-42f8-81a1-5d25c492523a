﻿
@section Styles{

    <style>
        .parent {
            position: relative;
            width: 400px;
            /* Set the width of the parent div */
            height: 400px;
            /* Set the height of the parent div */
           
        }

        .moving-image {
            position: absolute;
            width: 50px;
            /* Set the width of the image */
            height: 50px;
            /* Set the height of the image */
            top: 0;
            left: 0;
        }

        .container1 {
            text-align: center;
            max-width: 800px;
            padding: 5%;
            padding-top: 1%;
        }
    </style>
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


<div class="container1">

    <div>Contact</div>
    <hr />

    <div>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation </div>

    <div id="display_email" style="margin-top:5vw">

    </div>

    <div class="parent" id="check" style="margin:auto">
        <img onclick="showEmail();" src="icons/pacman.png" class="moving-image" alt="moving image">
    </div>
</div>


@section Scripts{

    <script>
        const parent = document.querySelector('.parent');
        const movingImage = document.querySelector('.moving-image');

        function moveImage() {
            const parentWidth = parent.clientWidth;
            const parentHeight = parent.clientHeight;
            const imageWidth = movingImage.clientWidth;
            const imageHeight = movingImage.clientHeight;

            const randomX = Math.floor(Math.random() * (parentWidth - imageWidth));
            const randomY = Math.floor(Math.random() * (parentHeight - imageHeight));

            movingImage.style.top = randomY + 'px';
            movingImage.style.left = randomX + 'px';
        }

        setInterval(moveImage, 2000); // Change the value to adjust the speed of movement (in milliseconds)
    </script>


    <script>

        function showEmail() {


            $.ajax({
                url: "/Contact/GetEmail",
                type: "GET",
                async: false,
                success: function (response) {

                    document.getElementById("display_email").innerHTML = response;
                    document.getElementById("display_email").style.display = "block";
                    document.getElementById("check").style.display = "none";
                },
                error: function (xhr) {
                    // handle error
                }
            });



        }


    </script>

}




