﻿@model stockhom_soundrom_mvc.ViewModels.TrackViewModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_layoutUserPages.cshtml";
}


@section Styles{

    <link rel="stylesheet" href="~/css/Track.css" type="text/css" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <style>
        .support_overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .support_heading {
            color: white;
            text-align: center;
            font-size: 2vw;
        }

        .support_body {
            color: white;
            text-align: left;
        }

        #support_closeButton {
            position: absolute;
            top: 0;
            background-color: white;
            color: white;
            height: 3vw;
        }

        /*email prompt*/
        @@media screen and (max-width: 584px) {
            /* #email-prompt-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
        }*/

            #email-prompt {
                position: fixed;
                top: 55vw;
                left: 1vw;
                right: 2vw;
                background-color: black;
                z-index: 10000;
                border-radius: 5px;
                padding: 10px;
                display: none;
                height: 300px;
            }


            #prompt .prompt-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #ccc;
                padding: 5px;
            }

                #prompt .prompt-header .prompt-title {
                    font-size: 1.2rem;
                }

                #prompt .prompt-header .prompt-close {
                    cursor: pointer;
                    font-size: 1.5rem;
                }

            #prompt .prompt-body {
                padding: 10px;
            }

            #prompt input,
            #prompt button {
                margin: 5px;
                padding: 5px;
            }

            #prompt button {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            }

                #prompt button:hover {
                    background-color: #3e8e41;
                }


            #div_new {
                margin: auto;
                width: 75%;
                text-align: center;
                display: block;
                height: 15vw;
            }

            .country {
                height: 50px;
                font-size: 20px;
                margin-left: 0;
                display: block;
                margin-bottom: 20px;
            }


            #input_country {
                display: none;
                width: 100%;
                height: 219px;
                margin: auto;
                border-radius: 4px;
                border-style: none;
                background-color: #222;
                margin-bottom: 5px;
                position: relative;
            }

            #under_country {
                font-size: 18px;
                margin: 3px;
                color: white;
                text-align: left;
                margin-left: 7px;
                margin: auto;
                margin-top: 10px;
                margin-bottom: 20px;
            }

            #input_div2 {
                text-align: center;
                margin: auto;
                height: 4vw;
                width: 100%;
                height: 50px;
            }

            #cross_button {
                position: absolute;
                background-color: #222;
                color: white;
                top: 0;
                right: 0;
            }

                #cross_button:active {
                    background-color: white;
                }
        }

        @@media screen and (min-width: 585px) {
            #email-prompt-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 9999;
                display: none;
            }

            #email-prompt {
                position: fixed;
                top: 20vw;
                left: 1vw;
                right: 2vw;
                background-color: black;
                z-index: 10000;
                border-radius: 5px;
                padding: 10px;
                display: none;
                height: 18vw;
            }


            #prompt .prompt-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #ccc;
                padding: 5px;
            }

                #prompt .prompt-header .prompt-title {
                    font-size: 1.2rem;
                }

                #prompt .prompt-header .prompt-close {
                    cursor: pointer;
                    font-size: 1.5rem;
                }

            #prompt .prompt-body {
                padding: 10px;
            }

            #prompt input,
            #prompt button {
                margin: 5px;
                padding: 5px;
            }

            #prompt button {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            }

                #prompt button:hover {
                    background-color: #3e8e41;
                }


            #div_new {
                margin: auto;
                width: 55vw;
                text-align: center;
                display: block;
                height: 15vw;
            }

            .country {
                height: 3vw;
                font-size: 1.5vw;
                margin-left: 0;
                display: block;
            }


            #input_country {
                display: none;
                width: 100%;
                height: 219px;
                margin: auto;
                border-radius: 4px;
                border-style: none;
                background-color: #222;
                margin-bottom: 5px;
                position: relative;
            }

            #under_country {
                font-size: 1.5vw;
                margin: 3px;
                color: white;
                text-align: left;
                margin-left: 7px;
                margin: auto;
                margin-top: 10px;
                margin-bottom: 1vw;
            }

            #input_div2 {
                text-align: center;
                margin: auto;
                height: 4vw;
                width: 100%;
            }


            #cross_button {
                position: absolute;
                background-color: #222;
                color: white;
                top: 0;
                right: 0;
            }

                #cross_button:active {
                    background-color: white;
                }
        }

        /*youtube subscribe button all other elements inside it */
        @@media screen and (max-width: 584px) {

            .youtube_subs_button {
                width: 100%;
                height: 15vw;
                font-size: 5vw;
                font-weight: 600;
                color: white;
                background-color: black;
                border-radius: 10px;
            }


            .youtube_subs_button_div {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .youtube_subs_button_img {
                width: 14vw;
                height: 14vw;
            }
        }

        @@media screen and (min-width: 585px) {

            .youtube_subs_button {
                width: 100%;
                height: 5vw;
                font-size: 2vw;
                font-weight: 600;
                color: white;
                background-color: black;
                border-radius: 10px;
            }


            .youtube_subs_button_div {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .youtube_subs_button_img {
                width: 4vw;
                height: 4vw;
            }
        }
    </style>

}


<div class="container1">

    <div class="main section">
        @*<img class="back_button" src="~/icons/back.png" />*@

        @if (Model.Track.RelatedArtists.Count > 0)
        {
            <h2 class="track_name">@Model.Track.TrackTitle - @Model.Track.RelatedArtists.First().ArtistName </h2>
        }
        else
        {
            <h2 class="track_name">@Model.Track.TrackTitle</h2>
        }

        <hr>

        <br>

        <p id="description" class="description">@Model.Track.Description</p>
        @if (Model.Track.OfferFreeDownload == true)
        {
            @*<button class="button1">Free Track Download</button>*@
            <input onclick="trackDownloadButtonClick()" id="btn_dnld" type="button" class="button1" value="Free Track Download" />
        }
    </div>

    <div class="sc_embedment_container">
        @Html.Raw(Model.Track.SoundCloudLink)
    </div>

    <div class="streaming_services">
        @if (string.IsNullOrEmpty(Model.Track.SpotifyLink) == false)
        {
            <a href="@Model.Track.SpotifyLink">
                <img class="straming_service_icon" src="~/icons/Spotify ikon svart.png" />
            </a>
        }


        @if (string.IsNullOrEmpty(Model.Track.AppleLink) == false)
        {
            <a href="@Model.Track.AppleLink">
                <img class="straming_service_icon" src="~/icons/Apple music ikon svart.png" />
            </a>
        }

        @if (string.IsNullOrEmpty(Model.Track.YoutubeMusicStreamLink) == false)
        {
            <a href="@Model.Track.YoutubeMusicStreamLink">
                <img class="straming_service_icon" src="~/icons/YouTube music ikon svart.png" />
            </a>
        }



        @if (Model.Track.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).Count() > 0)
        {
            <a href="@Model.Track.BuyLinks.Where(bl => bl.Link.Contains("www.beatport.com")).First().Link">
                <img class="straming_service_icon" src="~/icons/Beatport music ikon svart.png" />
            </a>
        }
        @if (Model.Track.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).Count() > 0)
        {
            <a href="@Model.Track.BuyLinks.Where(bl => bl.Link.Contains("bandcamp.com")).First().Link">
                <img class="straming_service_icon" src="~/icons/Bandcamp music ikon svart.png" />
            </a>
        }
        @if (Model.Track.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).Count() > 0)
        {
            <a href="@Model.Track.BuyLinks.Where(bl => bl.Link.Contains("www.junodownload.com")).First().Link">
                <img class="straming_service_icon" src="~/icons/Juno Download ikon svart.png" />
            </a>
        }



    </div>

    <div class="video-container">
        <iframe width="560" height="315" src="https://www.youtube.com/embed/@Model.Track.YoutubeLink?controls=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
    </div>

    <button onclick="window.location.href='https://www.youtube.com/@@sthlmsndrm?sub_confirmation=1'" class="youtube_subs_button">
        <div class="youtube_subs_button_div">
            <div style="display: inline-block;">SUBSCRIBE</div>
            <img src="/icons/YouTube ikon svart.png" class="youtube_subs_button_img">
            <div style="display: inline-block;">CHANNEL</div>
        </div>
    </button>


    @if (Model.Track.RelatedArtists != null && Model.Track.RelatedArtists.Any())
    {
        <div class="section">
            <div class="section_title">MEET THE ARTISTS</div>

            <div class="related_artists1">

                @foreach (var artist in Model.Track.RelatedArtists)
                {
                    <a href="@Url.Action("index","Artist", new { id=artist.Id})">
                        <img class="related_artists1" src=@Url.Content($"~/Uploads/{artist.VerticalImageUrl}") />
                    </a>
                }
            </div>

        </div>
    }



    @if (Model.OtherTracks != null && Model.OtherTracks.Any())
    {
        <div class="section">
            <div class="section_title">TRACKS FROM SAME MAXI SINGLE / ALBUM / EP</div>

            @foreach (var track in Model.OtherTracks)
            {

                <div class="track_title_and_artist">@track.TrackTitle - @track.RelatedArtists[0].ArtistName</div>
                <div class="sc_embedment_container">
                    @Html.Raw(track.SoundCloudLink)
                </div>
                <button onclick="window.location='@Url.Action("index", "music", new { id = Model.RelatedAlbum.Id })'" class="button1">GO TO @Model.RelatedAlbum.Title Page </button>
            }

        </div>
    }


    <div id="email-prompt-overlay"></div>
    <div id="email-prompt">
        <button id="cross_button">&#x2715;</button>

        <div id="div_new">
            <select id="country" class="country" name="country">
                <option>Select country (Optional)</option>
                <option value="AF">Afghanistan</option>
                <option value="AX">Aland Islands</option>
                <option value="AL">Albania</option>
                <option value="DZ">Algeria</option>
                <option value="AS">American Samoa</option>
                <option value="AD">Andorra</option>
                <option value="AO">Angola</option>
                <option value="AI">Anguilla</option>
                <option value="AQ">Antarctica</option>
                <option value="AG">Antigua and Barbuda</option>
                <option value="AR">Argentina</option>
                <option value="AM">Armenia</option>
                <option value="AW">Aruba</option>
                <option value="AU">Australia</option>
                <option value="AT">Austria</option>
                <option value="AZ">Azerbaijan</option>
                <option value="BS">Bahamas</option>
                <option value="BH">Bahrain</option>
                <option value="BD">Bangladesh</option>
                <option value="BB">Barbados</option>
                <option value="BY">Belarus</option>
                <option value="BE">Belgium</option>
                <option value="BZ">Belize</option>
                <option value="BJ">Benin</option>
                <option value="BM">Bermuda</option>
                <option value="BT">Bhutan</option>
                <option value="BO">Bolivia</option>
                <option value="BQ">Bonaire, Sint Eustatius and Saba</option>
                <option value="BA">Bosnia and Herzegovina</option>
                <option value="BW">Botswana</option>
                <option value="BV">Bouvet Island</option>
                <option value="BR">Brazil</option>
                <option value="IO">British Indian Ocean Territory</option>
                <option value="BN">Brunei Darussalam</option>
                <option value="BG">Bulgaria</option>
                <option value="BF">Burkina Faso</option>
                <option value="BI">Burundi</option>
                <option value="KH">Cambodia</option>
                <option value="CM">Cameroon</option>
                <option value="CA">Canada</option>
                <option value="CV">Cape Verde</option>
                <option value="KY">Cayman Islands</option>
                <option value="CF">Central African Republic</option>
                <option value="TD">Chad</option>
                <option value="CL">Chile</option>
                <option value="CN">China</option>
                <option value="CX">Christmas Island</option>
                <option value="CC">Cocos (Keeling) Islands</option>
                <option value="CO">Colombia</option>
                <option value="KM">Comoros</option>
                <option value="CG">Congo</option>
                <option value="CD">Congo, Democratic Republic of the Congo</option>
                <option value="CK">Cook Islands</option>
                <option value="CR">Costa Rica</option>
                <option value="CI">Cote D'Ivoire</option>
                <option value="HR">Croatia</option>
                <option value="CU">Cuba</option>
                <option value="CW">Curacao</option>
                <option value="CY">Cyprus</option>
                <option value="CZ">Czech Republic</option>
                <option value="DK">Denmark</option>
                <option value="DJ">Djibouti</option>
                <option value="DM">Dominica</option>
                <option value="DO">Dominican Republic</option>
                <option value="EC">Ecuador</option>
                <option value="EG">Egypt</option>
                <option value="SV">El Salvador</option>
                <option value="GQ">Equatorial Guinea</option>
                <option value="ER">Eritrea</option>
                <option value="EE">Estonia</option>
                <option value="ET">Ethiopia</option>
                <option value="FK">Falkland Islands (Malvinas)</option>
                <option value="FO">Faroe Islands</option>
                <option value="FJ">Fiji</option>
                <option value="FI">Finland</option>
                <option value="FR">France</option>
                <option value="GF">French Guiana</option>
                <option value="PF">French Polynesia</option>
                <option value="TF">French Southern Territories</option>
                <option value="GA">Gabon</option>
                <option value="GM">Gambia</option>
                <option value="GE">Georgia</option>
                <option value="DE">Germany</option>
                <option value="GH">Ghana</option>
                <option value="GI">Gibraltar</option>
                <option value="GR">Greece</option>
                <option value="GL">Greenland</option>
                <option value="GD">Grenada</option>
                <option value="GP">Guadeloupe</option>
                <option value="GU">Guam</option>
                <option value="GT">Guatemala</option>
                <option value="GG">Guernsey</option>
                <option value="GN">Guinea</option>
                <option value="GW">Guinea-Bissau</option>
                <option value="GY">Guyana</option>
                <option value="HT">Haiti</option>
                <option value="HM">Heard Island and Mcdonald Islands</option>
                <option value="VA">Holy See (Vatican City State)</option>
                <option value="HN">Honduras</option>
                <option value="HK">Hong Kong</option>
                <option value="HU">Hungary</option>
                <option value="IS">Iceland</option>
                <option value="IN">India</option>
                <option value="ID">Indonesia</option>
                <option value="IR">Iran, Islamic Republic of</option>
                <option value="IQ">Iraq</option>
                <option value="IE">Ireland</option>
                <option value="IM">Isle of Man</option>
                <option value="IL">Israel</option>
                <option value="IT">Italy</option>
                <option value="JM">Jamaica</option>
                <option value="JP">Japan</option>
                <option value="JE">Jersey</option>
                <option value="JO">Jordan</option>
                <option value="KZ">Kazakhstan</option>
                <option value="KE">Kenya</option>
                <option value="KI">Kiribati</option>
                <option value="KP">Korea, Democratic People's Republic of</option>
                <option value="KR">Korea, Republic of</option>
                <option value="XK">Kosovo</option>
                <option value="KW">Kuwait</option>
                <option value="KG">Kyrgyzstan</option>
                <option value="LA">Lao People's Democratic Republic</option>
                <option value="LV">Latvia</option>
                <option value="LB">Lebanon</option>
                <option value="LS">Lesotho</option>
                <option value="LR">Liberia</option>
                <option value="LY">Libyan Arab Jamahiriya</option>
                <option value="LI">Liechtenstein</option>
                <option value="LT">Lithuania</option>
                <option value="LU">Luxembourg</option>
                <option value="MO">Macao</option>
                <option value="MK">Macedonia, the Former Yugoslav Republic of</option>
                <option value="MG">Madagascar</option>
                <option value="MW">Malawi</option>
                <option value="MY">Malaysia</option>
                <option value="MV">Maldives</option>
                <option value="ML">Mali</option>
                <option value="MT">Malta</option>
                <option value="MH">Marshall Islands</option>
                <option value="MQ">Martinique</option>
                <option value="MR">Mauritania</option>
                <option value="MU">Mauritius</option>
                <option value="YT">Mayotte</option>
                <option value="MX">Mexico</option>
                <option value="FM">Micronesia, Federated States of</option>
                <option value="MD">Moldova, Republic of</option>
                <option value="MC">Monaco</option>
                <option value="MN">Mongolia</option>
                <option value="ME">Montenegro</option>
                <option value="MS">Montserrat</option>
                <option value="MA">Morocco</option>
                <option value="MZ">Mozambique</option>
                <option value="MM">Myanmar</option>
                <option value="NA">Namibia</option>
                <option value="NR">Nauru</option>
                <option value="NP">Nepal</option>
                <option value="NL">Netherlands</option>
                <option value="AN">Netherlands Antilles</option>
                <option value="NC">New Caledonia</option>
                <option value="NZ">New Zealand</option>
                <option value="NI">Nicaragua</option>
                <option value="NE">Niger</option>
                <option value="NG">Nigeria</option>
                <option value="NU">Niue</option>
                <option value="NF">Norfolk Island</option>
                <option value="MP">Northern Mariana Islands</option>
                <option value="NO">Norway</option>
                <option value="OM">Oman</option>
                <option value="PK">Pakistan</option>
                <option value="PW">Palau</option>
                <option value="PS">Palestinian Territory, Occupied</option>
                <option value="PA">Panama</option>
                <option value="PG">Papua New Guinea</option>
                <option value="PY">Paraguay</option>
                <option value="PE">Peru</option>
                <option value="PH">Philippines</option>
                <option value="PN">Pitcairn</option>
                <option value="PL">Poland</option>
                <option value="PT">Portugal</option>
                <option value="PR">Puerto Rico</option>
                <option value="QA">Qatar</option>
                <option value="RE">Reunion</option>
                <option value="RO">Romania</option>
                <option value="RU">Russian Federation</option>
                <option value="RW">Rwanda</option>
                <option value="BL">Saint Barthelemy</option>
                <option value="SH">Saint Helena</option>
                <option value="KN">Saint Kitts and Nevis</option>
                <option value="LC">Saint Lucia</option>
                <option value="MF">Saint Martin</option>
                <option value="PM">Saint Pierre and Miquelon</option>
                <option value="VC">Saint Vincent and the Grenadines</option>
                <option value="WS">Samoa</option>
                <option value="SM">San Marino</option>
                <option value="ST">Sao Tome and Principe</option>
                <option value="SA">Saudi Arabia</option>
                <option value="SN">Senegal</option>
                <option value="RS">Serbia</option>
                <option value="CS">Serbia and Montenegro</option>
                <option value="SC">Seychelles</option>
                <option value="SL">Sierra Leone</option>
                <option value="SG">Singapore</option>
                <option value="SX">Sint Maarten</option>
                <option value="SK">Slovakia</option>
                <option value="SI">Slovenia</option>
                <option value="SB">Solomon Islands</option>
                <option value="SO">Somalia</option>
                <option value="ZA">South Africa</option>
                <option value="GS">South Georgia and the South Sandwich Islands</option>
                <option value="SS">South Sudan</option>
                <option value="ES">Spain</option>
                <option value="LK">Sri Lanka</option>
                <option value="SD">Sudan</option>
                <option value="SR">Suriname</option>
                <option value="SJ">Svalbard and Jan Mayen</option>
                <option value="SZ">Swaziland</option>
                <option value="SE">Sweden</option>
                <option value="CH">Switzerland</option>
                <option value="SY">Syrian Arab Republic</option>
                <option value="TW">Taiwan, Province of China</option>
                <option value="TJ">Tajikistan</option>
                <option value="TZ">Tanzania, United Republic of</option>
                <option value="TH">Thailand</option>
                <option value="TL">Timor-Leste</option>
                <option value="TG">Togo</option>
                <option value="TK">Tokelau</option>
                <option value="TO">Tonga</option>
                <option value="TT">Trinidad and Tobago</option>
                <option value="TN">Tunisia</option>
                <option value="TR">Turkey</option>
                <option value="TM">Turkmenistan</option>
                <option value="TC">Turks and Caicos Islands</option>
                <option value="TV">Tuvalu</option>
                <option value="UG">Uganda</option>
                <option value="UA">Ukraine</option>
                <option value="AE">United Arab Emirates</option>
                <option value="GB">United Kingdom</option>
                <option value="US">United States</option>
                <option value="UM">United States Minor Outlying Islands</option>
                <option value="UY">Uruguay</option>
                <option value="UZ">Uzbekistan</option>
                <option value="VU">Vanuatu</option>
                <option value="VE">Venezuela</option>
                <option value="VN">Viet Nam</option>
                <option value="VG">Virgin Islands, British</option>
                <option value="VI">Virgin Islands, U.s.</option>
                <option value="WF">Wallis and Futuna</option>
                <option value="EH">Western Sahara</option>
                <option value="YE">Yemen</option>
                <option value="ZM">Zambia</option>
                <option value="ZW">Zimbabwe</option>
            </select>
            <div id="under_country">
                Why do you want to know where i live?
                <br>
                Because when we plan to throw our next bad ass party in your country you
                will be the first to know.
            </div>
            <div id="input_div2">
                <input id="txt2" class="email_txt" type="email" placeholder="Enter your email address" required>
                <input type="button" onclick="submitEmail(document.getElementById('txt2').value, @Model.Track.Id);" class="email_button" id="btn2" value="SUBMIT">
            </div>
        </div>
    </div>



</div>


@{

    string c_headline = "";
    string c_body = "";
    string c_dialog_inner_html = "";

    using (Data data = new Data())
    {
        c_headline = data.Supports.First().Headline;
        c_body = data.Supports.First().BodyText;


        c_dialog_inner_html = $"<div class='support_heading'>{c_headline}</div><hr><div class='support_body'>{c_body}</div><img id='support_closeButton' class='back_button' src='/icons/back.png'>";




    }


}

@section Scripts{

    <script type="text/javascript" src="/js/track.js"></script>

    @*<script>

            function openPopup() {

                 var dialog_inner_html = "@Html.Raw(c_dialog_inner_html)";




                // Create the overlay
                var overlay = document.createElement('div');
                overlay.classList.add('support_overlay');

                // Calculate the dimensions of the popup
                var screenWidth = window.innerWidth;
                var screenHeight = window.innerHeight;
                var popupWidth = screenWidth * 0.8;
                var popupHeight = screenHeight;

                // Create the popup dialog
                var dialog = document.createElement('div');
                dialog.style.width = popupWidth + 'px';
                dialog.style.height = popupHeight + 'px';
                dialog.style.backgroundColor = 'black';
                dialog.style.padding = '1vw';

                overlay.appendChild(dialog);

                // Add the dialog content
                dialog.innerHTML = dialog_inner_html;

                // Add a click event handler to the close button
                var closeButton = dialog.querySelector('#support_closeButton');

                closeButton.addEventListener('click', function () {
                    document.body.removeChild(overlay);
                });

                // Add the overlay to the page
                document.body.appendChild(overlay);

                // Prevent the default button click behavior
                return false;
            }

            document.getElementById('btn_dnld').addEventListener('click', openPopup);

        </script>*@


}

