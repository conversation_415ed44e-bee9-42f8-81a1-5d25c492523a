﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>