﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>