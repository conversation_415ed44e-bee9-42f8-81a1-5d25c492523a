﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stockhom_soundrom_mvc.Models
{
    public class Music
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public MusicCollectionType  MusicCollectionType { get; set; }
        public DateTime PublishDate { get; set; }
        public bool StartCountDown { get; set; }    
        public bool TopPromo { get; set; }
        public string ArtWorkSquare { get; set; }
        public string ArtWorkHoriontal { get; set; }
        public List<MusicBuyLink> BuyLinks { get; set; } = new List<MusicBuyLink>();
        public string SpotifyAlbumLink { get; set; }
        public string AppleAlbumLink { get; set; }
        public string SoundCloudAlbumLink { get; set; }
        public string YoutubeMusicAlbumLink { get; set; }
        public string YoutubeAlbumLink { get; set; }
        public string Description { get; set; }
        public string MetaTitle { get; set; }
        public string MetaDescription { get; set; }
        public List<Track> Tracks { get; set; } = new List<Track>();
        public State State { get; set; }

    }

    public enum MusicCollectionType
    {
        maxi_single,
        album,
        EP

    }
}
