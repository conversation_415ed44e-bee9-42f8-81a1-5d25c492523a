﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>